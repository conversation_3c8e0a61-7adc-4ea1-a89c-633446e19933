import { defineStore } from 'pinia'
import axios from 'axios'

// 配置axios基础URL
const API_BASE_URL = 'http://localhost:3001/api'
axios.defaults.baseURL = API_BASE_URL

// 认证store
export const useAuthStore = defineStore('auth', {
  state: () => ({
    // 用户信息
    user: null,
    // 令牌信息
    accessToken: null,
    refreshToken: null,
    // 认证状态
    isAuthenticated: false,
    isLoading: false,
    // 权限信息
    permissions: [],
    roles: [],
    departments: []
  }),

  getters: {
    // 获取用户显示名称
    userDisplayName: (state) => {
      return state.user?.real_name || state.user?.username || '未知用户'
    },

    // 检查是否有特定权限
    hasPermission: (state) => {
      return (permission) => {
        if (!state.isAuthenticated) return false
        if (state.roles.includes('super_admin')) return true
        return state.permissions.includes(permission)
      }
    },

    // 检查是否有特定角色
    hasRole: (state) => {
      return (role) => {
        if (!state.isAuthenticated) return false
        return state.roles.includes(role)
      }
    },

    // 检查是否属于特定部门
    inDepartment: (state) => {
      return (department) => {
        if (!state.isAuthenticated) return false
        return state.departments.includes(department)
      }
    },

    // 检查是否为管理员
    isAdmin: (state) => {
      return state.roles.includes('super_admin') || state.roles.includes('admin')
    }
  },

  actions: {
    // 初始化认证状态
    async initAuth() {
      const token = localStorage.getItem('accessToken')
      const refreshToken = localStorage.getItem('refreshToken')
      
      if (token && refreshToken) {
        this.accessToken = token
        this.refreshToken = refreshToken
        
        // 设置axios默认header
        this.setAuthHeader(token)
        
        try {
          // 验证令牌并获取用户信息
          await this.getCurrentUser()
        } catch (error) {
          console.error('令牌验证失败:', error)
          // 尝试刷新令牌
          try {
            await this.refreshAccessToken()
          } catch (refreshError) {
            console.error('刷新令牌失败:', refreshError)
            this.logout()
          }
        }
      }
    },

    // 用户登录
    async login(credentials) {
      this.isLoading = true
      
      try {
        const response = await axios.post('/auth/login', credentials)
        
        if (response.data.success) {
          const { user, tokens } = response.data.data
          
          // 保存用户信息和令牌
          this.setUserData(user, tokens)
          
          return { success: true, message: '登录成功' }
        } else {
          throw new Error(response.data.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        const message = error.response?.data?.message || error.message || '登录失败'
        return { success: false, message }
      } finally {
        this.isLoading = false
      }
    },

    // 用户注册
    async register(userData) {
      this.isLoading = true
      
      try {
        const response = await axios.post('/auth/register', userData)
        
        if (response.data.success) {
          return { success: true, message: response.data.message }
        } else {
          throw new Error(response.data.message || '注册失败')
        }
      } catch (error) {
        console.error('注册失败:', error)
        const message = error.response?.data?.message || error.message || '注册失败'
        return { success: false, message }
      } finally {
        this.isLoading = false
      }
    },

    // 用户登出
    async logout() {
      try {
        if (this.accessToken) {
          await axios.post('/auth/logout')
        }
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        this.clearUserData()
      }
    },

    // 刷新访问令牌
    async refreshAccessToken() {
      if (!this.refreshToken) {
        throw new Error('没有刷新令牌')
      }

      try {
        const response = await axios.post('/auth/refresh', {
          refreshToken: this.refreshToken
        })

        if (response.data.success) {
          const { user, tokens } = response.data.data
          this.setUserData(user, tokens)
          return true
        } else {
          throw new Error(response.data.message || '刷新令牌失败')
        }
      } catch (error) {
        console.error('刷新令牌失败:', error)
        this.clearUserData()
        throw error
      }
    },

    // 获取当前用户信息
    async getCurrentUser() {
      try {
        const response = await axios.get('/auth/me')
        
        if (response.data.success) {
          const user = response.data.data.user
          this.updateUserInfo(user)
          return user
        } else {
          throw new Error(response.data.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      this.isLoading = true
      
      try {
        const response = await axios.put('/auth/change-password', passwordData)
        
        if (response.data.success) {
          return { success: true, message: response.data.message }
        } else {
          throw new Error(response.data.message || '修改密码失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        const message = error.response?.data?.message || error.message || '修改密码失败'
        return { success: false, message }
      } finally {
        this.isLoading = false
      }
    },

    // 设置用户数据
    setUserData(user, tokens) {
      this.user = user
      this.accessToken = tokens.accessToken
      this.refreshToken = tokens.refreshToken
      this.isAuthenticated = true
      
      // 设置权限信息
      this.permissions = user.permissions || []
      this.roles = user.roles || []
      this.departments = user.departments || []
      
      // 保存到localStorage
      localStorage.setItem('accessToken', tokens.accessToken)
      localStorage.setItem('refreshToken', tokens.refreshToken)
      localStorage.setItem('user', JSON.stringify(user))
      
      // 设置axios认证头
      this.setAuthHeader(tokens.accessToken)
    },

    // 更新用户信息
    updateUserInfo(user) {
      this.user = user
      this.permissions = user.permissions || []
      this.roles = user.roles || []
      this.departments = user.departments || []
      this.isAuthenticated = true
      
      // 更新localStorage中的用户信息
      localStorage.setItem('user', JSON.stringify(user))
    },

    // 清除用户数据
    clearUserData() {
      this.user = null
      this.accessToken = null
      this.refreshToken = null
      this.isAuthenticated = false
      this.permissions = []
      this.roles = []
      this.departments = []
      
      // 清除localStorage
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      
      // 清除axios认证头
      delete axios.defaults.headers.common['Authorization']
    },

    // 设置axios认证头
    setAuthHeader(token) {
      if (token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } else {
        delete axios.defaults.headers.common['Authorization']
      }
    }
  }
})

// 配置axios拦截器
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const authStore = useAuthStore()
    
    if (error.response?.status === 401) {
      const originalRequest = error.config
      
      // 避免无限循环
      if (!originalRequest._retry) {
        originalRequest._retry = true
        
        try {
          // 尝试刷新令牌
          await authStore.refreshAccessToken()
          
          // 重新发送原始请求
          originalRequest.headers['Authorization'] = `Bearer ${authStore.accessToken}`
          return axios(originalRequest)
        } catch (refreshError) {
          // 刷新失败，跳转到登录页
          authStore.logout()
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }
    }
    
    return Promise.reject(error)
  }
)
