import axios from 'axios'

const request = axios.create({
  baseURL: 'http://localhost:3001',
  withCredentials: false,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

request.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(new Error(error).message)
  }
)

request.interceptors.response.use(
  response => {
    const { data } = response

    // 如果后端返回的数据格式是 { success: true, data: ... }
    if (data && typeof data === 'object' && 'success' in data) {
      if (data.success) {
        return data  // 返回完整的响应对象，包含success和data字段
      } else {
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }

    // 兼容原有格式
    return data
  },
  error => {
    console.error('请求错误:', error)

    if (error.response) {
      const { status, data } = error.response
      const message = data?.message || `请求失败 (${status})`
      return Promise.reject(new Error(message))
    } else if (error.request) {
      return Promise.reject(new Error('网络连接超时'))
    } else {
      return Promise.reject(new Error(error.message || '请求失败'))
    }
  }
)

export default request
