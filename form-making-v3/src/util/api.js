import request from './request'

// 表单相关API
export const saveForm = (formData) => {
  return request.post('/api/forms', formData)
}

export const updateForm = (id, formData) => {
  return request.put(`/api/forms/${id}`, formData)
}

export const getFormList = (params) => {
  return request.get('/api/forms', { params })
}

export const getFormById = (id) => {
  return request.get(`/api/forms/${id}`)
}

export const deleteForm = (id) => {
  return request.delete(`/api/forms/${id}`)
}
