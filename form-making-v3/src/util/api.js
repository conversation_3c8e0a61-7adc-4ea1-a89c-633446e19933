import request from './request'

// 表单相关API
export const saveForm = (formData) => {
  return request.post('/api/forms', formData)
}

export const updateForm = (id, formData) => {
  return request.put(`/api/forms/${id}`, formData)
}

export const getFormList = (params) => {
  return request.get('/api/forms', { params })
}

export const getFormById = (id) => {
  return request.get(`/api/forms/${id}`)
}

export const deleteForm = (id) => {
  return request.delete(`/api/forms/${id}`)
}

// 字典管理相关API
// 字典分类
export const getDictionaryCategories = () => {
  return request.get('/api/dictionaries/categories')
}

export const createDictionaryCategory = (data) => {
  return request.post('/api/dictionaries/categories', data)
}

export const updateDictionaryCategory = (id, data) => {
  return request.put(`/api/dictionaries/categories/${id}`, data)
}

export const deleteDictionaryCategory = (id) => {
  return request.delete(`/api/dictionaries/categories/${id}`)
}

// 字典管理
export const getDictionaries = (params = {}) => {
  return request.get('/api/dictionaries', { params })
}

export const getDictionary = (id) => {
  return request.get(`/api/dictionaries/${id}`)
}

export const createDictionary = (data) => {
  return request.post('/api/dictionaries', data)
}

export const updateDictionary = (id, data) => {
  return request.put(`/api/dictionaries/${id}`, data)
}

export const deleteDictionary = (id) => {
  return request.delete(`/api/dictionaries/${id}`)
}

export const getDictionaryUsage = (id) => {
  return request.get(`/api/dictionaries/${id}/usage`)
}

export const getDictionaryVersions = (id) => {
  return request.get(`/api/dictionaries/${id}/versions`)
}

// 根据编码获取字典（供表单使用）
export const getDictionaryByCode = (code, categoryCode = null) => {
  const params = categoryCode ? { category_code: categoryCode } : {}
  return request.get(`/api/dictionaries/code/${code}`, { params })
}
