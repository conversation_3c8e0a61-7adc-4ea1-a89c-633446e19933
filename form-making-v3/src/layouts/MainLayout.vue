<template>
  <div class="vuexy-layout">
    <!-- 顶部导航栏 -->
    <header class="navbar">
      <div class="navbar-content">
        <!-- 左侧区域 -->
        <div class="navbar-left">
          <!-- 菜单切换按钮 -->
          <button class="menu-toggle" @click="toggleSidebar">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>

          <!-- 搜索框 -->
          <div class="search-container">
            <div class="search-input">
              <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search (Ctrl + K)"
                class="search-field"
                @keydown.ctrl.k.prevent="focusSearch"
              />
              <kbd class="search-shortcut">⌘K</kbd>
            </div>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="navbar-right">
          <!-- 语言切换 -->
          <button class="navbar-action">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z" fill="currentColor"/>
            </svg>
          </button>

          <!-- 全屏切换 -->
          <button class="navbar-action">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- 主题切换 -->
          <button class="navbar-action" @click="toggleTheme">
            <svg v-if="!isDarkMode" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="1" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="21" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" stroke="currentColor" stroke-width="2"/>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" stroke="currentColor" stroke-width="2"/>
              <line x1="1" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2"/>
              <line x1="21" y1="12" x2="23" y2="12" stroke="currentColor" stroke-width="2"/>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" stroke="currentColor" stroke-width="2"/>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <!-- 通知 -->
          <div class="navbar-notification">
            <button class="navbar-action" @click="showNotifications">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
            </button>
          </div>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-menu">
              <el-avatar :size="32" :src="authStore.user?.avatar" class="user-avatar">
                {{ authStore.userDisplayName?.charAt(0) }}
              </el-avatar>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="user-dropdown-menu">
                <div class="user-info">
                  <div class="user-name">{{ authStore.userDisplayName }}</div>
                  <div class="user-role">管理员</div>
                </div>
                <el-dropdown-item command="profile">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="changePassword">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="16" r="1" fill="currentColor"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4M16 17l5-5-5-5M21 12H9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="layout-content">
      <!-- 左侧菜单栏 -->
      <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <!-- Logo区域 -->
        <div class="sidebar-header">
          <div class="brand">
            <div class="brand-logo">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#7367f0"/>
                    <stop offset="100%" style="stop-color:#9c88ff"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <span v-if="!sidebarCollapsed" class="brand-text">Vuexy</span>
          </div>
        </div>

        <!-- 菜单内容 -->
        <div class="sidebar-content">
          <nav class="sidebar-nav">
            <!-- 仪表盘 -->
            <div class="nav-section">
              <router-link to="/dashboard" class="nav-item" :class="{ active: activeMenu === '/dashboard' }">
                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="2"/>
                  <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span class="nav-text">仪表盘</span>
              </router-link>
            </div>

            <!-- 表单管理 -->
            <div class="nav-section">
              <div class="nav-group-title">应用</div>
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('form') }">
                <button class="nav-group-header" @click="toggleGroup('form')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">表单管理</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/form/designer" class="nav-subitem" :class="{ active: activeMenu === '/form/designer' }">
                    表单设计器
                  </router-link>
                  <router-link to="/form/management" class="nav-subitem" :class="{ active: activeMenu === '/form/management' }">
                    表单管理
                  </router-link>
                  <router-link to="/form/templates" class="nav-subitem" :class="{ active: activeMenu === '/form/templates' }">
                    表单模板
                  </router-link>
                </div>
              </div>
            </div>

            <!-- 字典管理 -->
            <div class="nav-section">
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('dictionary') }">
                <button class="nav-group-header" @click="toggleGroup('dictionary')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="2"/>
                    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">字典管理</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/dictionary/management" class="nav-subitem" :class="{ active: activeMenu === '/dictionary/management' }">
                    字典管理
                  </router-link>
                  <router-link to="/dictionary/categories" class="nav-subitem" :class="{ active: activeMenu === '/dictionary/categories' }">
                    字典分类
                  </router-link>
                  <router-link to="/dictionary/items" class="nav-subitem" :class="{ active: activeMenu === '/dictionary/items' }">
                    字典维护
                  </router-link>
                  <router-link to="/dictionary/versions" class="nav-subitem" :class="{ active: activeMenu === '/dictionary/versions' }">
                    版本管理
                  </router-link>
                </div>
              </div>
            </div>

            <!-- 其他功能组 -->
            <div class="nav-section">
              <div class="nav-group-title">功能模块</div>
              <!-- 列表管理 -->
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('list') }">
                <button class="nav-group-header" @click="toggleGroup('list')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
                    <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
                    <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
                    <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">列表管理</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/list/designer" class="nav-subitem">列表设计器</router-link>
                  <router-link to="/list/views" class="nav-subitem">列表视图</router-link>
                </div>
              </div>

              <!-- 统计分析 -->
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('statistics') }">
                <button class="nav-group-header" @click="toggleGroup('statistics')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <line x1="12" y1="20" x2="12" y2="10" stroke="currentColor" stroke-width="2"/>
                    <line x1="18" y1="20" x2="18" y2="4" stroke="currentColor" stroke-width="2"/>
                    <line x1="6" y1="20" x2="6" y2="16" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">统计分析</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/statistics/dashboard" class="nav-subitem">统计仪表盘</router-link>
                  <router-link to="/statistics/reports" class="nav-subitem">报表管理</router-link>
                  <router-link to="/statistics/charts" class="nav-subitem">图表配置</router-link>
                </div>
              </div>

              <!-- 工作流 -->
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('workflow') }">
                <button class="nav-group-header" @click="toggleGroup('workflow')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">工作流</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/workflow/designer" class="nav-subitem">流程设计</router-link>
                  <router-link to="/workflow/instances" class="nav-subitem">流程实例</router-link>
                  <router-link to="/workflow/tasks" class="nav-subitem">待办任务</router-link>
                </div>
              </div>

              <!-- 系统管理 -->
              <div class="nav-group" :class="{ expanded: expandedGroups.includes('system') }">
                <button class="nav-group-header" @click="toggleGroup('system')">
                  <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">系统管理</span>
                  <svg class="nav-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <div class="nav-group-content">
                  <router-link to="/system/users" class="nav-subitem">用户管理</router-link>
                  <router-link to="/system/roles" class="nav-subitem">角色管理</router-link>
                  <router-link to="/system/permissions" class="nav-subitem">权限管理</router-link>
                  <router-link to="/system/departments" class="nav-subitem">部门管理</router-link>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="content-wrapper">
        <div class="content-area">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'MainLayout',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()

    // 响应式数据
    const sidebarCollapsed = ref(false)
    const searchQuery = ref('')
    const notificationCount = ref(5)
    const isDarkMode = ref(false)
    const expandedGroups = ref(['form', 'dictionary']) // 默认展开的菜单组

    // 计算属性
    const activeMenu = computed(() => route.path)

    // 方法
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    const toggleTheme = () => {
      isDarkMode.value = !isDarkMode.value
      document.documentElement.classList.toggle('dark', isDarkMode.value)
    }

    const toggleGroup = (groupName) => {
      const index = expandedGroups.value.indexOf(groupName)
      if (index > -1) {
        expandedGroups.value.splice(index, 1)
      } else {
        expandedGroups.value.push(groupName)
      }
    }

    const focusSearch = () => {
      // 聚焦搜索框
      const searchInput = document.querySelector('.search-field')
      if (searchInput) {
        searchInput.focus()
      }
    }

    const showNotifications = () => {
      ElMessage.info('通知功能开发中...')
    }

    const handleUserCommand = async (command) => {
      switch (command) {
        case 'profile':
          ElMessage.info('个人资料功能开发中...')
          break
        case 'changePassword':
          ElMessage.info('修改密码功能开发中...')
          break
        case 'settings':
          ElMessage.info('系统设置功能开发中...')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '确认退出',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )

            await authStore.logout()
            ElMessage.success('已退出登录')
            router.push('/login')
          } catch (error) {
            if (error !== 'cancel') {
              console.error('退出登录失败:', error)
              ElMessage.error('退出登录失败')
            }
          }
          break
      }
    }

    onMounted(() => {
      // 检查系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = prefersDark
      document.documentElement.classList.toggle('dark', prefersDark)

      // 监听系统主题变化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        isDarkMode.value = e.matches
        document.documentElement.classList.toggle('dark', e.matches)
      })
    })

    return {
      authStore,
      sidebarCollapsed,
      searchQuery,
      notificationCount,
      isDarkMode,
      expandedGroups,
      activeMenu,
      toggleSidebar,
      toggleTheme,
      toggleGroup,
      focusSearch,
      showNotifications,
      handleUserCommand
    }
  }
}
</script>

<style lang="scss" scoped>
.vuexy-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

// 顶部导航栏 - Vuexy风格
.navbar {
  height: 64px;
  background: var(--navbar-bg);
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 1000;

  .navbar-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
  }

  .navbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;

    .menu-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: none;
      border: none;
      border-radius: var(--border-radius-md);
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
        color: var(--text-primary);
      }
    }

    .search-container {
      margin-left: 1rem;

      .search-input {
        position: relative;
        display: flex;
        align-items: center;
        width: 400px;
        height: 40px;
        background: var(--background-color);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        padding: 0 1rem;
        transition: all 0.2s ease;

        &:focus-within {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
        }

        .search-icon {
          color: var(--text-light);
          margin-right: 0.75rem;
        }

        .search-field {
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          color: var(--text-primary);
          font-size: 14px;

          &::placeholder {
            color: var(--text-light);
          }
        }

        .search-shortcut {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 24px;
          height: 20px;
          background: var(--border-color);
          border-radius: 4px;
          font-size: 11px;
          font-weight: 500;
          color: var(--text-light);
          padding: 0 4px;
        }
      }
    }
  }

  .navbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .navbar-action {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: none;
      border: none;
      border-radius: var(--border-radius-md);
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--background-color);
        color: var(--text-primary);
      }
    }

    .navbar-notification {
      position: relative;

      .notification-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        min-width: 16px;
        height: 16px;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        font-size: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 4px;
      }
    }

    .user-dropdown {
      margin-left: 0.5rem;

      .user-menu {
        display: flex;
        align-items: center;
        cursor: pointer;

        .user-avatar {
          background: var(--primary-gradient);
          color: white;
          font-weight: 600;
        }
      }
    }
  }
}

// 主体内容区域
.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧菜单栏 - Vuexy风格
.sidebar {
  width: 260px;
  background: var(--sidebar-bg);
  transition: width 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.collapsed {
    width: 80px;

    .brand-text {
      opacity: 0;
    }

    .nav-text {
      opacity: 0;
    }

    .nav-arrow {
      opacity: 0;
    }

    .nav-group-title {
      opacity: 0;
    }

    .nav-group-content {
      display: none;
    }
  }

  .sidebar-header {
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    .brand {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .brand-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
      }

      .brand-text {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-white);
        transition: opacity 0.3s ease;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }
  }

  .sidebar-nav {
    .nav-section {
      margin-bottom: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .nav-group-title {
      font-size: 11px;
      font-weight: 600;
      color: var(--text-light);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 0 1.5rem;
      margin-bottom: 0.75rem;
      transition: opacity 0.3s ease;
    }

    .nav-item {
      display: flex;
      align-items: center;
      height: 44px;
      padding: 0 1.5rem;
      color: var(--sidebar-text);
      text-decoration: none;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: var(--sidebar-hover);
        color: var(--text-white);
      }

      &.active {
        background: var(--sidebar-active);
        color: var(--text-white);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background: var(--primary-color);
        }
      }

      .nav-icon {
        margin-right: 0.75rem;
        flex-shrink: 0;
      }

      .nav-text {
        font-size: 14px;
        font-weight: 500;
        transition: opacity 0.3s ease;
      }
    }

    .nav-group {
      .nav-group-header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 44px;
        padding: 0 1.5rem;
        background: none;
        border: none;
        color: var(--sidebar-text);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--sidebar-hover);
          color: var(--text-white);
        }

        .nav-icon {
          margin-right: 0.75rem;
          flex-shrink: 0;
        }

        .nav-text {
          flex: 1;
          text-align: left;
          font-size: 14px;
          font-weight: 500;
          transition: opacity 0.3s ease;
        }

        .nav-arrow {
          margin-left: auto;
          transition: transform 0.2s ease, opacity 0.3s ease;
        }
      }

      &.expanded {
        .nav-arrow {
          transform: rotate(180deg);
        }

        .nav-group-content {
          max-height: 300px;
          opacity: 1;
        }
      }

      .nav-group-content {
        max-height: 0;
        opacity: 0;
        overflow: hidden;
        transition: all 0.3s ease;

        .nav-subitem {
          display: block;
          height: 36px;
          line-height: 36px;
          padding: 0 1.5rem 0 3.25rem;
          color: var(--sidebar-text);
          text-decoration: none;
          font-size: 13px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--sidebar-hover);
            color: var(--text-white);
          }

          &.active {
            background: var(--sidebar-active);
            color: var(--text-white);
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 3px;
              background: var(--primary-color);
            }
          }
        }
      }
    }
  }
}

// 右侧内容区域
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .content-area {
    flex: 1;
    background: var(--background-color);
    padding: 1.5rem;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--background-color);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--border-color);
      border-radius: 3px;

      &:hover {
        background: var(--text-light);
      }
    }
  }
}

// 用户下拉菜单样式
:deep(.user-dropdown-menu) {
  .user-info {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);

    .user-name {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }

    .user-role {
      font-size: 12px;
      color: var(--text-light);
    }
  }

  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    font-size: 14px;

    svg {
      color: var(--text-light);
    }

    &:hover {
      background: var(--background-color);

      svg {
        color: var(--primary-color);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .navbar {
    .search-container {
      .search-input {
        width: 300px;
      }
    }
  }
}

@media (max-width: 768px) {
  .navbar {
    .navbar-content {
      padding: 0 1rem;
    }

    .navbar-left {
      .search-container {
        display: none;
      }
    }

    .navbar-right {
      gap: 0.25rem;

      .navbar-action {
        width: 36px;
        height: 36px;
      }
    }
  }

  .sidebar {
    position: fixed;
    left: -260px;
    top: 64px;
    height: calc(100vh - 64px);
    z-index: 999;
    transition: left 0.3s ease;

    &.show {
      left: 0;
    }
  }

  .content-wrapper {
    margin-left: 0;

    .content-area {
      padding: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .navbar {
    .navbar-right {
      .navbar-action {
        width: 32px;
        height: 32px;

        svg {
          width: 16px;
          height: 16px;
        }
      }

      .user-dropdown {
        .user-avatar {
          width: 28px !important;
          height: 28px !important;
          font-size: 12px;
        }
      }
    }
  }

  .sidebar {
    width: 240px;

    &.collapsed {
      width: 60px;
    }
  }
}

// 深色主题特定样式
html.dark {
  .navbar {
    border-bottom-color: rgba(255, 255, 255, 0.05);
  }

  .sidebar {
    .sidebar-header {
      border-bottom-color: rgba(255, 255, 255, 0.05);
    }
  }

  :deep(.user-dropdown-menu) {
    background: var(--card-background);
    border-color: rgba(255, 255, 255, 0.05);

    .user-info {
      border-bottom-color: rgba(255, 255, 255, 0.05);
    }
  }
}
</style>
