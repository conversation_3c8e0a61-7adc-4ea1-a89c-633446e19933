<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="top-navbar">
      <div class="navbar-left">
        <!-- 菜单切换按钮 -->
        <button class="menu-toggle" @click="toggleSidebar">
          <i class="icon-menu"></i>
        </button>
        
        <!-- Logo和系统名称 -->
        <div class="brand">
          <img src="/logo.png" alt="Logo" class="logo" />
          <span class="brand-text">医疗低代码平台</span>
        </div>
        
        <!-- 面包屑导航 -->
        <nav class="breadcrumb">
          <span v-for="(item, index) in breadcrumbs" :key="index" class="breadcrumb-item">
            <router-link v-if="item.path" :to="item.path">{{ item.name }}</router-link>
            <span v-else>{{ item.name }}</span>
            <i v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">/</i>
          </span>
        </nav>
      </div>
      
      <div class="navbar-right">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索功能..."
            prefix-icon="Search"
            size="small"
            clearable
          />
        </div>
        
        <!-- 通知 -->
        <el-badge :value="notificationCount" class="notification-badge">
          <el-button circle size="small" @click="showNotifications">
            <i class="icon-bell"></i>
          </el-button>
        </el-badge>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-menu">
            <el-avatar :size="32" :src="authStore.user?.avatar" />
            <span class="username">{{ authStore.userDisplayName }}</span>
            <i class="icon-chevron-down"></i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单栏 -->
      <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-content">
          <!-- 主菜单 -->
          <el-menu
            :default-active="activeMenu"
            :collapse="sidebarCollapsed"
            :unique-opened="true"
            router
            class="main-menu"
          >
            <el-menu-item index="/dashboard">
              <i class="icon-home"></i>
              <span>仪表盘</span>
            </el-menu-item>
            
            <el-sub-menu index="form">
              <template #title>
                <i class="icon-edit"></i>
                <span>表单管理</span>
              </template>
              <el-menu-item index="/form/designer">表单设计器</el-menu-item>
              <el-menu-item index="/form/management">表单管理</el-menu-item>
              <el-menu-item index="/form/templates">表单模板</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="list">
              <template #title>
                <i class="icon-list"></i>
                <span>列表管理</span>
              </template>
              <el-menu-item index="/list/designer">列表设计器</el-menu-item>
              <el-menu-item index="/list/views">列表视图</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="statistics">
              <template #title>
                <i class="icon-bar-chart"></i>
                <span>统计分析</span>
              </template>
              <el-menu-item index="/statistics/dashboard">统计仪表盘</el-menu-item>
              <el-menu-item index="/statistics/reports">报表管理</el-menu-item>
              <el-menu-item index="/statistics/charts">图表配置</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="dictionary">
              <template #title>
                <i class="icon-book"></i>
                <span>字典管理</span>
              </template>
              <el-menu-item index="/dictionary/categories">字典分类</el-menu-item>
              <el-menu-item index="/dictionary/items">字典维护</el-menu-item>
              <el-menu-item index="/dictionary/versions">版本管理</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="workflow">
              <template #title>
                <i class="icon-git-branch"></i>
                <span>工作流</span>
              </template>
              <el-menu-item index="/workflow/designer">流程设计</el-menu-item>
              <el-menu-item index="/workflow/instances">流程实例</el-menu-item>
              <el-menu-item index="/workflow/tasks">待办任务</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="system">
              <template #title>
                <i class="icon-settings"></i>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/system/users">用户管理</el-menu-item>
              <el-menu-item index="/system/roles">角色管理</el-menu-item>
              <el-menu-item index="/system/permissions">权限管理</el-menu-item>
              <el-menu-item index="/system/departments">部门管理</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>
      </aside>
      
      <!-- 右侧内容区域 -->
      <main class="content-area">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'MainLayout',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()

    // 响应式数据
    const sidebarCollapsed = ref(false)
    const searchQuery = ref('')
    const notificationCount = ref(5)
    
    // 计算属性
    const activeMenu = computed(() => route.path)
    
    const breadcrumbs = computed(() => {
      const pathSegments = route.path.split('/').filter(Boolean)
      const breadcrumbMap = {
        'dashboard': '仪表盘',
        'form': '表单管理',
        'designer': '设计器',
        'list': '列表管理',
        'statistics': '统计分析',
        'dictionary': '字典管理',
        'workflow': '工作流',
        'system': '系统管理'
      }
      
      const crumbs = [{ name: '首页', path: '/dashboard' }]
      let currentPath = ''
      
      pathSegments.forEach(segment => {
        currentPath += `/${segment}`
        if (breadcrumbMap[segment]) {
          crumbs.push({
            name: breadcrumbMap[segment],
            path: currentPath
          })
        }
      })
      
      return crumbs
    })
    
    // 方法
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }
    
    const showNotifications = () => {
      console.log('显示通知')
    }
    
    const handleUserCommand = async (command) => {
      switch (command) {
        case 'profile':
          // TODO: 打开个人资料对话框
          ElMessage.info('个人资料功能开发中...')
          break
        case 'changePassword':
          // TODO: 打开修改密码对话框
          ElMessage.info('修改密码功能开发中...')
          break
        case 'settings':
          // TODO: 打开系统设置
          ElMessage.info('系统设置功能开发中...')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '确认退出',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )

            await authStore.logout()
            ElMessage.success('已退出登录')
            router.push('/login')
          } catch (error) {
            if (error !== 'cancel') {
              console.error('退出登录失败:', error)
              ElMessage.error('退出登录失败')
            }
          }
          break
      }
    }
    
    onMounted(() => {
      // 初始化逻辑
    })
    
    return {
      authStore,
      sidebarCollapsed,
      searchQuery,
      notificationCount,
      activeMenu,
      breadcrumbs,
      toggleSidebar,
      showNotifications,
      handleUserCommand
    }
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

// 顶部导航栏
.top-navbar {
  height: 64px;
  background: var(--card-background);
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  position: relative;
  z-index: 1000;
  
  .navbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .menu-toggle {
      background: none;
      border: none;
      font-size: 20px;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 8px;
      border-radius: var(--border-radius-sm);
      
      &:hover {
        background: var(--background-color);
      }
    }
    
    .brand {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .logo {
        width: 32px;
        height: 32px;
      }
      
      .brand-text {
        font-size: 18px;
        font-weight: 600;
        color: var(--primary-color);
      }
    }
    
    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: var(--spacing-lg);
      
      .breadcrumb-item {
        color: var(--text-secondary);
        font-size: 14px;
        
        a {
          color: var(--primary-color);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        .breadcrumb-separator {
          margin: 0 8px;
          color: var(--text-light);
        }
      }
    }
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .search-box {
      width: 300px;
    }
    
    .notification-badge {
      .el-button {
        background: none;
        border: none;
        color: var(--text-secondary);
        
        &:hover {
          background: var(--background-color);
        }
      }
    }
    
    .user-menu {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: var(--border-radius-sm);
      cursor: pointer;
      
      &:hover {
        background: var(--background-color);
      }
      
      .username {
        font-size: 14px;
        color: var(--text-primary);
      }
    }
  }
}

// 主体内容区域
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧菜单栏
.sidebar {
  width: 260px;
  background: var(--sidebar-bg);
  transition: width 0.3s ease;
  overflow: hidden;
  
  &.collapsed {
    width: 80px;
  }
  
  .sidebar-content {
    height: 100%;
    overflow-y: auto;
    
    .main-menu {
      border: none;
      background: transparent;
      
      :deep(.el-menu-item) {
        color: var(--sidebar-text);
        height: 48px;
        line-height: 48px;
        
        &:hover {
          background: var(--sidebar-hover);
        }
        
        &.is-active {
          background: var(--sidebar-active);
          color: white;
        }
        
        i {
          margin-right: 12px;
          font-size: 18px;
        }
      }
      
      :deep(.el-sub-menu) {
        .el-sub-menu__title {
          color: var(--sidebar-text);
          height: 48px;
          line-height: 48px;
          
          &:hover {
            background: var(--sidebar-hover);
          }
          
          i {
            margin-right: 12px;
            font-size: 18px;
          }
        }
      }
    }
  }
}

// 右侧内容区域
.content-area {
  flex: 1;
  background: var(--background-color);
  padding: var(--spacing-md);
  overflow-y: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .top-navbar {
    .navbar-left {
      .breadcrumb {
        display: none;
      }
    }
    
    .navbar-right {
      .search-box {
        width: 200px;
      }
    }
  }
  
  .sidebar {
    position: fixed;
    left: 0;
    top: 64px;
    height: calc(100vh - 64px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
  
  .content-area {
    margin-left: 0;
  }
}
</style>
