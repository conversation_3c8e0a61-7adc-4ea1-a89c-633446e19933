<template>
  <PlaceholderPage
    title="字典维护"
    description="维护字典数据项，支持医疗术语、等级分类等各种业务字典"
    :show-actions="true"
    create-text="添加字典项"
    @create="createItem"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'DictionaryItems',
  components: {
    PlaceholderPage
  },
  methods: {
    createItem() {
      console.log('添加字典项')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
