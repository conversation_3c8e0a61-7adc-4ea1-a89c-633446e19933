<template>
  <PlaceholderPage
    title="版本管理"
    description="管理字典数据的版本历史，支持版本回滚和变更追踪"
    :show-actions="true"
    create-text="创建版本"
    @create="createVersion"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'DictionaryVersions',
  components: {
    PlaceholderPage
  },
  methods: {
    createVersion() {
      console.log('创建版本')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
