<template>
  <PlaceholderPage
    title="字典分类"
    description="管理字典数据的分类结构，支持层级分类和权限控制"
    :show-actions="true"
    create-text="创建分类"
    @create="createCategory"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'DictionaryCategories',
  components: {
    PlaceholderPage
  },
  methods: {
    createCategory() {
      console.log('创建分类')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
