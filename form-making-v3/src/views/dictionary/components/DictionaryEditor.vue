<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dictionary?.id ? '编辑字典' : '创建字典'"
    width="900px"
    :before-close="handleClose"
    class="dictionary-editor-dialog"
  >
    <div class="dictionary-editor">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        
        <el-form 
          ref="formRef"
          :model="dictionaryForm"
          :rules="formRules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="字典名称" prop="name">
                <el-input v-model="dictionaryForm.name" placeholder="请输入字典名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="字典编码" prop="code">
                <el-input 
                  v-model="dictionaryForm.code" 
                  placeholder="请输入字典编码"
                  :disabled="!!dictionary?.id"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属分类" prop="category_id">
                <el-select v-model="dictionaryForm.category_id" placeholder="选择分类">
                  <el-option 
                    v-for="category in categories" 
                    :key="category.id" 
                    :label="category.name" 
                    :value="category.id" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据类型" prop="data_type">
                <el-select v-model="dictionaryForm.data_type" placeholder="选择数据类型">
                  <el-option label="字符串" value="string" />
                  <el-option label="数字" value="number" />
                  <el-option label="布尔值" value="boolean" />
                  <el-option label="对象" value="object" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="描述" prop="description">
            <el-input 
              v-model="dictionaryForm.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入字典描述"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 字典项管理 -->
      <el-card class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>字典项管理</span>
            <div class="header-actions">
              <el-button size="small" @click="addItem">
                <i class="icon-plus"></i>
                添加项
              </el-button>
              <el-button size="small" @click="importItems">
                <i class="icon-upload"></i>
                批量导入
              </el-button>
              <el-button size="small" @click="exportItems">
                <i class="icon-download"></i>
                导出
              </el-button>
            </div>
          </div>
        </template>

        <div class="items-table">
          <el-table 
            :data="dictionaryForm.items" 
            style="width: 100%"
            max-height="400"
          >
            <el-table-column type="index" label="#" width="50" />
            <el-table-column label="显示标签" min-width="150">
              <template #default="{ row, $index }">
                <el-input 
                  v-model="row.label" 
                  placeholder="显示标签"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="字典值" min-width="150">
              <template #default="{ row, $index }">
                <el-input 
                  v-model="row.value" 
                  placeholder="字典值"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="描述" min-width="200">
              <template #default="{ row, $index }">
                <el-input 
                  v-model="row.description" 
                  placeholder="描述（可选）"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="排序" width="100">
              <template #default="{ row, $index }">
                <el-input-number 
                  v-model="row.sort_order" 
                  :min="0" 
                  :max="999"
                  size="small"
                  controls-position="right"
                />
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row, $index }">
                <el-switch 
                  v-model="row.status" 
                  active-value="active"
                  inactive-value="inactive"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row, $index }">
                <el-button 
                  size="small" 
                  @click="moveItemUp($index)"
                  :disabled="$index === 0"
                >
                  ↑
                </el-button>
                <el-button 
                  size="small" 
                  @click="moveItemDown($index)"
                  :disabled="$index === dictionaryForm.items.length - 1"
                >
                  ↓
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="removeItem($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="dictionaryForm.items.length === 0" class="empty-items">
          <el-empty description="暂无字典项，点击上方"添加项"按钮开始添加" />
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="saveAndPublish" :loading="saving">
          {{ saving ? '保存中...' : '保存并发布' }}
        </el-button>
      </div>
    </template>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importVisible"
      title="批量导入字典项"
      width="600px"
      append-to-body
    >
      <div class="import-content">
        <el-alert
          title="导入格式说明"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          <p>请按照以下格式输入，每行一个字典项：</p>
          <p><code>显示标签,字典值,描述(可选)</code></p>
          <p>例如：男,male,性别选项</p>
        </el-alert>
        
        <el-input
          v-model="importText"
          type="textarea"
          :rows="10"
          placeholder="请输入字典项数据..."
        />
      </div>
      
      <template #footer>
        <el-button @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="processImport">导入</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createDictionary, updateDictionary, getDictionary } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'saved'])

// 响应式数据
const dialogVisible = ref(false)
const saving = ref(false)
const importVisible = ref(false)
const importText = ref('')
const formRef = ref(null)

// 表单数据
const dictionaryForm = reactive({
  name: '',
  code: '',
  category_id: null,
  description: '',
  data_type: 'string',
  items: []
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { min: 2, max: 100, message: '字典名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { min: 2, max: 50, message: '编码长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择所属分类', trigger: 'change' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
}

// 方法
const loadDictionaryData = async () => {
  if (props.dictionary?.id) {
    try {
      const response = await getDictionary(props.dictionary.id)
      if (response.success) {
        const dict = response.data
        Object.assign(dictionaryForm, {
          name: dict.name,
          code: dict.code,
          category_id: dict.category_id,
          description: dict.description || '',
          data_type: dict.data_type || 'string',
          items: dict.items || []
        })
      }
    } catch (error) {
      console.error('加载字典数据失败:', error)
      ElMessage.error('加载字典数据失败')
    }
  } else if (props.dictionary) {
    // 复制模式
    Object.assign(dictionaryForm, {
      name: props.dictionary.name || '',
      code: props.dictionary.code || '',
      category_id: props.dictionary.category_id || null,
      description: props.dictionary.description || '',
      data_type: props.dictionary.data_type || 'string',
      items: props.dictionary.items || []
    })
  } else {
    // 新建模式
    resetForm()
  }
}

const resetForm = () => {
  Object.assign(dictionaryForm, {
    name: '',
    code: '',
    category_id: null,
    description: '',
    data_type: 'string',
    items: []
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const addItem = () => {
  dictionaryForm.items.push({
    label: '',
    value: '',
    description: '',
    sort_order: dictionaryForm.items.length,
    status: 'active'
  })
}

const removeItem = (index) => {
  dictionaryForm.items.splice(index, 1)
  // 重新排序
  dictionaryForm.items.forEach((item, idx) => {
    item.sort_order = idx
  })
}

const moveItemUp = (index) => {
  if (index > 0) {
    const item = dictionaryForm.items.splice(index, 1)[0]
    dictionaryForm.items.splice(index - 1, 0, item)
    updateSortOrder()
  }
}

const moveItemDown = (index) => {
  if (index < dictionaryForm.items.length - 1) {
    const item = dictionaryForm.items.splice(index, 1)[0]
    dictionaryForm.items.splice(index + 1, 0, item)
    updateSortOrder()
  }
}

const updateSortOrder = () => {
  dictionaryForm.items.forEach((item, index) => {
    item.sort_order = index
  })
}

const importItems = () => {
  importText.value = ''
  importVisible.value = true
}

const processImport = () => {
  if (!importText.value.trim()) {
    ElMessage.warning('请输入导入数据')
    return
  }

  const lines = importText.value.trim().split('\n')
  const newItems = []
  
  lines.forEach((line, index) => {
    const parts = line.split(',').map(part => part.trim())
    if (parts.length >= 2) {
      newItems.push({
        label: parts[0],
        value: parts[1],
        description: parts[2] || '',
        sort_order: dictionaryForm.items.length + index,
        status: 'active'
      })
    }
  })

  if (newItems.length > 0) {
    dictionaryForm.items.push(...newItems)
    ElMessage.success(`成功导入 ${newItems.length} 个字典项`)
    importVisible.value = false
  } else {
    ElMessage.warning('没有有效的导入数据')
  }
}

const exportItems = () => {
  if (dictionaryForm.items.length === 0) {
    ElMessage.warning('没有可导出的字典项')
    return
  }

  const exportData = dictionaryForm.items.map(item => 
    `${item.label},${item.value},${item.description || ''}`
  ).join('\n')

  const blob = new Blob([exportData], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${dictionaryForm.name || 'dictionary'}_items.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const saveDraft = async () => {
  await saveDictionary('draft')
}

const saveAndPublish = async () => {
  await saveDictionary('active')
}

const saveDictionary = async (status) => {
  try {
    await formRef.value.validate()
    
    if (dictionaryForm.items.length === 0) {
      ElMessage.warning('请至少添加一个字典项')
      return
    }

    // 验证字典项
    const invalidItems = dictionaryForm.items.filter(item => !item.label || !item.value)
    if (invalidItems.length > 0) {
      ElMessage.warning('请完善所有字典项的标签和值')
      return
    }

    saving.value = true
    
    const data = {
      ...dictionaryForm,
      status,
      items: dictionaryForm.items
    }

    if (props.dictionary?.id) {
      await updateDictionary(props.dictionary.id, data)
      ElMessage.success('字典更新成功')
    } else {
      await createDictionary(data)
      ElMessage.success('字典创建成功')
    }
    
    emit('saved')
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存字典失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    nextTick(() => {
      loadDictionaryData()
    })
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style scoped>
.dictionary-editor-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.dictionary-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.info-card, .items-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.items-table {
  margin-top: 16px;
}

.empty-items {
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.import-content {
  padding: 0;
}

.import-content code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 图标样式 */
.icon-plus::before { content: "➕"; }
.icon-upload::before { content: "📤"; }
.icon-download::before { content: "📥"; }
</style>
