<template>
  <el-dialog
    v-model="dialogVisible"
    title="字典详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="dictionary" class="dictionary-detail">
      <!-- 基本信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <span class="section-title">基本信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="字典名称">
            {{ dictionary.name }}
          </el-descriptions-item>
          <el-descriptions-item label="字典编码">
            <code class="code-text">{{ dictionary.code }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="所属分类">
            {{ dictionary.category_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="数据类型">
            <el-tag size="small">{{ getDataTypeName(dictionary.data_type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag 
              :type="getStatusType(dictionary.status)" 
              size="small"
            >
              {{ getStatusName(dictionary.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="字典项数量">
            <el-tag type="info" size="small">{{ dictionary.item_count || 0 }} 项</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(dictionary.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(dictionary.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ dictionary.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 字典项列表 -->
      <el-card class="items-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">字典项列表</span>
            <div class="header-actions">
              <el-input
                v-model="searchText"
                placeholder="搜索字典项..."
                prefix-icon="Search"
                size="small"
                style="width: 200px"
                clearable
              />
            </div>
          </div>
        </template>

        <el-table 
          :data="filteredItems" 
          v-loading="loading"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="label" label="显示标签" min-width="150" />
          <el-table-column prop="value" label="字典值" min-width="150">
            <template #default="{ row }">
              <code class="code-text">{{ row.value }}</code>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.description || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="sort_order" label="排序" width="80" align="center" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="row.status === 'active' ? 'success' : 'info'" 
                size="small"
              >
                {{ row.status === 'active' ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="!loading && (!dictionary.items || dictionary.items.length === 0)" class="empty-items">
          <el-empty description="暂无字典项" />
        </div>
      </el-card>

      <!-- API使用示例 -->
      <el-card class="api-section" shadow="never">
        <template #header>
          <span class="section-title">API使用示例</span>
        </template>
        
        <div class="api-examples">
          <div class="example-item">
            <h4>获取字典数据</h4>
            <el-input
              :value="getApiExample()"
              readonly
              type="textarea"
              :rows="2"
            />
            <el-button size="small" @click="copyToClipboard(getApiExample())">
              复制
            </el-button>
          </div>
          
          <div class="example-item">
            <h4>JavaScript调用示例</h4>
            <el-input
              :value="getJsExample()"
              readonly
              type="textarea"
              :rows="4"
            />
            <el-button size="small" @click="copyToClipboard(getJsExample())">
              复制
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="editDictionary">编辑字典</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDictionary } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const searchText = ref('')
const dictionaryDetail = ref(null)

// 计算属性
const dictionary = computed(() => {
  return dictionaryDetail.value || props.dictionary
})

const filteredItems = computed(() => {
  if (!dictionary.value?.items) return []
  
  if (!searchText.value) {
    return dictionary.value.items
  }
  
  const query = searchText.value.toLowerCase()
  return dictionary.value.items.filter(item => 
    item.label.toLowerCase().includes(query) ||
    item.value.toLowerCase().includes(query) ||
    (item.description && item.description.toLowerCase().includes(query))
  )
})

// 方法
const loadDictionaryDetail = async () => {
  if (!props.dictionary?.id) return
  
  try {
    loading.value = true
    const response = await getDictionary(props.dictionary.id)
    if (response.success) {
      dictionaryDetail.value = response.data
    }
  } catch (error) {
    console.error('加载字典详情失败:', error)
    ElMessage.error('加载字典详情失败')
  } finally {
    loading.value = false
  }
}

const getDataTypeName = (type) => {
  const typeMap = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    object: '对象'
  }
  return typeMap[type] || type
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    draft: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用',
    draft: '草稿'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getApiExample = () => {
  if (!dictionary.value) return ''
  return `GET /api/dictionaries/code/${dictionary.value.code}`
}

const getJsExample = () => {
  if (!dictionary.value) return ''
  return `// 获取字典数据
import { getDictionaryByCode } from '@/util/api'

const dict = await getDictionaryByCode('${dictionary.value.code}')
console.log(dict.data.items)`
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const editDictionary = () => {
  emit('edit', dictionary.value)
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    searchText.value = ''
    loadDictionaryDetail()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style scoped>
.dictionary-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section, .items-section, .api-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.section-title {
  font-weight: 600;
  color: #2c3e50;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.code-text {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.empty-items {
  padding: 40px 0;
}

.api-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.example-item .el-button {
  align-self: flex-start;
}
</style>
