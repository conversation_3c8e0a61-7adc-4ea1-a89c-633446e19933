<template>
  <el-dialog
    v-model="dialogVisible"
    title="字典使用情况"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="dictionary" class="usage-dialog">
      <!-- 字典信息 -->
      <div class="dict-info">
        <h3>{{ dictionary.name }}</h3>
        <p class="dict-code">{{ dictionary.code }}</p>
      </div>

      <!-- 使用统计 -->
      <div class="usage-stats">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">{{ usageList.length }}</div>
              <div class="stat-label">使用次数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">{{ uniqueForms.length }}</div>
              <div class="stat-label">关联表单</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">{{ usageTypes.length }}</div>
              <div class="stat-label">使用类型</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 使用详情 -->
      <div class="usage-details">
        <h4>使用详情</h4>
        <el-table 
          :data="usageList" 
          v-loading="loading"
          style="width: 100%"
          max-height="300"
        >
          <el-table-column prop="form_name" label="表单名称" min-width="150">
            <template #default="{ row }">
              <el-link 
                type="primary" 
                @click="viewForm(row.form_id)"
                :underline="false"
              >
                {{ row.form_name }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="field_name" label="字段名称" min-width="120" />
          <el-table-column prop="usage_type" label="使用类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getUsageTypeColor(row.usage_type)">
                {{ getUsageTypeName(row.usage_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="关联时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button 
                size="small" 
                @click="viewForm(row.form_id)"
              >
                查看表单
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="!loading && usageList.length === 0" class="empty-usage">
          <el-empty description="该字典暂未被任何表单使用" />
        </div>
      </div>

      <!-- 使用类型分布 -->
      <div class="usage-distribution">
        <h4>使用类型分布</h4>
        <div class="type-stats">
          <div 
            v-for="type in usageTypeStats" 
            :key="type.type" 
            class="type-stat-item"
          >
            <div class="type-info">
              <el-tag size="small" :type="getUsageTypeColor(type.type)">
                {{ getUsageTypeName(type.type) }}
              </el-tag>
              <span class="type-count">{{ type.count }} 次</span>
            </div>
            <div class="type-progress">
              <el-progress 
                :percentage="(type.count / usageList.length * 100)" 
                :show-text="false"
                :stroke-width="8"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="refreshUsage">刷新数据</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDictionaryUsage } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const usageList = ref([])

// 计算属性
const uniqueForms = computed(() => {
  const formIds = new Set(usageList.value.map(item => item.form_id))
  return Array.from(formIds)
})

const usageTypes = computed(() => {
  const types = new Set(usageList.value.map(item => item.usage_type))
  return Array.from(types)
})

const usageTypeStats = computed(() => {
  const stats = {}
  usageList.value.forEach(item => {
    stats[item.usage_type] = (stats[item.usage_type] || 0) + 1
  })
  
  return Object.entries(stats).map(([type, count]) => ({
    type,
    count
  })).sort((a, b) => b.count - a.count)
})

// 方法
const loadUsageData = async () => {
  if (!props.dictionary?.id) return
  
  try {
    loading.value = true
    const response = await getDictionaryUsage(props.dictionary.id)
    if (response.success) {
      usageList.value = response.data || []
    }
  } catch (error) {
    console.error('加载使用情况失败:', error)
    ElMessage.error('加载使用情况失败')
  } finally {
    loading.value = false
  }
}

const getUsageTypeName = (type) => {
  const typeMap = {
    dropdown: '下拉选择',
    radio: '单选框',
    checkbox: '复选框',
    autocomplete: '自动完成'
  }
  return typeMap[type] || type
}

const getUsageTypeColor = (type) => {
  const colorMap = {
    dropdown: 'primary',
    radio: 'success',
    checkbox: 'warning',
    autocomplete: 'info'
  }
  return colorMap[type] || 'default'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const viewForm = (formId) => {
  // 这里可以跳转到表单详情页面
  window.open(`/form/preview/${formId}`, '_blank')
}

const refreshUsage = () => {
  loadUsageData()
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadUsageData()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style scoped>
.usage-dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dict-info {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dict-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.dict-code {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #6c757d;
  font-size: 14px;
}

.usage-stats {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
}

.usage-details h4,
.usage-distribution h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.empty-usage {
  padding: 40px 0;
}

.type-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.type-count {
  font-size: 12px;
  color: #6c757d;
}

.type-progress {
  flex: 1;
}
</style>
