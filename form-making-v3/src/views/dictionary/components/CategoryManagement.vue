<template>
  <el-dialog
    v-model="dialogVisible"
    title="字典分类管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="category-management">
      <!-- 操作栏 -->
      <div class="action-bar">
        <el-button type="primary" @click="createCategory">
          <i class="icon-plus"></i>
          新建分类
        </el-button>
        <el-button @click="loadCategories">
          <i class="icon-refresh"></i>
          刷新
        </el-button>
      </div>

      <!-- 分类列表 -->
      <el-table 
        :data="categories" 
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        style="width: 100%"
      >
        <el-table-column prop="name" label="分类名称" min-width="150">
          <template #default="{ row }">
            <div class="category-name">
              <strong>{{ row.name }}</strong>
              <span class="category-code">{{ row.code }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="sort_order" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'active' ? 'success' : 'info'" 
              size="small"
            >
              {{ row.status === 'active' ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editCategory(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteCategory(row)"
              :disabled="row.children && row.children.length > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="editorVisible"
      :title="currentCategory?.id ? '编辑分类' : '新建分类'"
      width="500px"
      append-to-body
    >
      <el-form 
        ref="formRef"
        :model="categoryForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input 
            v-model="categoryForm.code" 
            placeholder="请输入分类编码（英文字母和下划线）"
            :disabled="!!currentCategory?.id"
          />
        </el-form-item>
        <el-form-item label="父分类" prop="parent_id">
          <el-select v-model="categoryForm.parent_id" placeholder="选择父分类（可选）" clearable>
            <el-option 
              v-for="category in flatCategories" 
              :key="category.id" 
              :label="category.name" 
              :value="category.id"
              :disabled="category.id === currentCategory?.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="categoryForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number 
            v-model="categoryForm.sort_order" 
            :min="0" 
            :max="999"
            placeholder="排序值"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editorVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDictionaryCategories, createDictionaryCategory } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const saving = ref(false)
const categories = ref([])
const editorVisible = ref(false)
const currentCategory = ref(null)
const formRef = ref(null)

// 表单数据
const categoryForm = reactive({
  name: '',
  code: '',
  parent_id: null,
  description: '',
  sort_order: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { min: 2, max: 30, message: '编码长度在 2 到 30 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const flatCategories = computed(() => {
  const flatten = (cats, level = 0) => {
    let result = []
    cats.forEach(cat => {
      result.push({
        ...cat,
        name: '  '.repeat(level) + cat.name
      })
      if (cat.children && cat.children.length > 0) {
        result = result.concat(flatten(cat.children, level + 1))
      }
    })
    return result
  }
  return flatten(categories.value)
})

// 方法
const loadCategories = async () => {
  try {
    loading.value = true
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = buildCategoryTree(response.data || [])
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  } finally {
    loading.value = false
  }
}

const buildCategoryTree = (flatList) => {
  const map = {}
  const roots = []
  
  // 创建映射
  flatList.forEach(item => {
    map[item.id] = { ...item, children: [] }
  })
  
  // 构建树形结构
  flatList.forEach(item => {
    if (item.parent_id && map[item.parent_id]) {
      map[item.parent_id].children.push(map[item.id])
    } else {
      roots.push(map[item.id])
    }
  })
  
  return roots.sort((a, b) => a.sort_order - b.sort_order)
}

const createCategory = () => {
  currentCategory.value = null
  resetForm()
  editorVisible.value = true
}

const editCategory = (category) => {
  currentCategory.value = category
  Object.assign(categoryForm, {
    name: category.name,
    code: category.code,
    parent_id: category.parent_id,
    description: category.description || '',
    sort_order: category.sort_order || 0
  })
  editorVisible.value = true
}

const resetForm = () => {
  Object.assign(categoryForm, {
    name: '',
    code: '',
    parent_id: null,
    description: '',
    sort_order: 0
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const saveCategory = async () => {
  try {
    await formRef.value.validate()
    saving.value = true
    
    if (currentCategory.value?.id) {
      // 更新分类 - 这里需要实现更新API
      ElMessage.info('更新功能待实现')
    } else {
      // 创建分类
      await createDictionaryCategory(categoryForm)
      ElMessage.success('分类创建成功')
    }
    
    editorVisible.value = false
    loadCategories()
    emit('refresh')
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存分类失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

const deleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    // 这里需要实现删除API
    ElMessage.info('删除功能待实现')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadCategories()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style scoped>
.category-management {
  padding: 0;
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.category-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-code {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.icon-plus::before { content: "➕"; }
.icon-refresh::before { content: "🔄"; }
</style>
