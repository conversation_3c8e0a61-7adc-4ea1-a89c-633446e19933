<template>
  <el-dialog
    v-model="dialogVisible"
    title="版本历史"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="dictionary" class="version-history">
      <!-- 字典信息 -->
      <div class="dict-info">
        <h3>{{ dictionary.name }}</h3>
        <p class="dict-code">{{ dictionary.code }}</p>
      </div>

      <!-- 版本列表 -->
      <div class="version-list">
        <el-timeline>
          <el-timeline-item
            v-for="(version, index) in versionList"
            :key="version.id"
            :timestamp="formatDate(version.created_at)"
            placement="top"
            :type="index === 0 ? 'primary' : 'default'"
            :hollow="index !== 0"
          >
            <el-card class="version-card" shadow="hover">
              <div class="version-header">
                <div class="version-info">
                  <h4 class="version-title">
                    版本 {{ version.version }}
                    <el-tag v-if="index === 0" type="success" size="small">当前版本</el-tag>
                  </h4>
                  <p class="version-meta">
                    由 {{ version.created_by }} 创建于 {{ formatDate(version.created_at) }}
                  </p>
                </div>
                <div class="version-actions">
                  <el-button size="small" @click="viewVersionDetail(version)">
                    查看详情
                  </el-button>
                  <el-button 
                    v-if="index !== 0" 
                    size="small" 
                    type="primary"
                    @click="compareVersion(version)"
                  >
                    对比差异
                  </el-button>
                  <el-dropdown @command="(command) => handleVersionAction(command, version)">
                    <el-button size="small">
                      更多 <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="restore" :disabled="index === 0">
                          <i class="icon-rotate-ccw"></i> 恢复此版本
                        </el-dropdown-item>
                        <el-dropdown-item command="export">
                          <i class="icon-download"></i> 导出数据
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
              
              <div class="version-content">
                <div class="change-log">
                  <strong>变更说明：</strong>
                  <p>{{ version.change_log || '无变更说明' }}</p>
                </div>
                
                <div class="version-stats">
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <div class="stat-item">
                        <span class="stat-label">字典项数量</span>
                        <span class="stat-value">{{ getVersionItemCount(version) }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="stat-item">
                        <span class="stat-label">数据大小</span>
                        <span class="stat-value">{{ getVersionDataSize(version) }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="stat-item">
                        <span class="stat-label">版本类型</span>
                        <span class="stat-value">{{ getVersionType(version, index) }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>

        <div v-if="!loading && versionList.length === 0" class="empty-versions">
          <el-empty description="暂无版本历史" />
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="refreshVersions">刷新数据</el-button>
    </template>

    <!-- 版本详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="版本详情"
      width="600px"
      append-to-body
    >
      <div v-if="currentVersion" class="version-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="版本号">
            {{ currentVersion.version }}
          </el-descriptions-item>
          <el-descriptions-item label="创建者">
            {{ currentVersion.created_by }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDate(currentVersion.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="变更说明" :span="2">
            {{ currentVersion.change_log || '无变更说明' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="version-data">
          <h4>字典项数据</h4>
          <el-table 
            :data="getVersionItems(currentVersion)" 
            style="width: 100%"
            max-height="300"
          >
            <el-table-column type="index" label="#" width="50" />
            <el-table-column prop="label" label="显示标签" min-width="120" />
            <el-table-column prop="value" label="字典值" min-width="120">
              <template #default="{ row }">
                <code class="code-text">{{ row.value }}</code>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
            <el-table-column prop="sort_order" label="排序" width="80" align="center" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 版本对比对话框 -->
    <el-dialog
      v-model="compareVisible"
      title="版本对比"
      width="900px"
      append-to-body
    >
      <div v-if="compareData" class="version-compare">
        <div class="compare-header">
          <div class="compare-item">
            <h4>当前版本</h4>
            <p>{{ compareData.current.version }} - {{ formatDate(compareData.current.created_at) }}</p>
          </div>
          <div class="compare-divider">VS</div>
          <div class="compare-item">
            <h4>对比版本</h4>
            <p>{{ compareData.target.version }} - {{ formatDate(compareData.target.created_at) }}</p>
          </div>
        </div>

        <div class="compare-content">
          <el-tabs>
            <el-tab-pane label="字典项对比" name="items">
              <div class="items-compare">
                <!-- 这里可以实现详细的字典项对比逻辑 -->
                <el-alert
                  title="功能开发中"
                  type="info"
                  description="字典项详细对比功能正在开发中，敬请期待。"
                  :closable="false"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane label="统计对比" name="stats">
              <div class="stats-compare">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="stats-card">
                      <h5>当前版本</h5>
                      <p>字典项数量: {{ getVersionItemCount(compareData.current) }}</p>
                      <p>数据大小: {{ getVersionDataSize(compareData.current) }}</p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="stats-card">
                      <h5>对比版本</h5>
                      <p>字典项数量: {{ getVersionItemCount(compareData.target) }}</p>
                      <p>数据大小: {{ getVersionDataSize(compareData.target) }}</p>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDictionaryVersions } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const versionList = ref([])
const detailVisible = ref(false)
const compareVisible = ref(false)
const currentVersion = ref(null)
const compareData = ref(null)

// 方法
const loadVersionHistory = async () => {
  if (!props.dictionary?.id) return
  
  try {
    loading.value = true
    const response = await getDictionaryVersions(props.dictionary.id)
    if (response.success) {
      versionList.value = response.data || []
    }
  } catch (error) {
    console.error('加载版本历史失败:', error)
    ElMessage.error('加载版本历史失败')
  } finally {
    loading.value = false
  }
}

const getVersionItemCount = (version) => {
  try {
    const items = JSON.parse(version.items_data || '[]')
    return items.length
  } catch {
    return 0
  }
}

const getVersionDataSize = (version) => {
  const size = new Blob([version.items_data || '']).size
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const getVersionType = (version, index) => {
  if (index === 0) return '当前版本'
  return '历史版本'
}

const getVersionItems = (version) => {
  try {
    return JSON.parse(version.items_data || '[]')
  } catch {
    return []
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const viewVersionDetail = (version) => {
  currentVersion.value = version
  detailVisible.value = true
}

const compareVersion = (version) => {
  if (versionList.value.length > 0) {
    compareData.value = {
      current: versionList.value[0], // 当前版本
      target: version // 对比版本
    }
    compareVisible.value = true
  }
}

const handleVersionAction = async (command, version) => {
  switch (command) {
    case 'restore':
      await restoreVersion(version)
      break
    case 'export':
      exportVersion(version)
      break
  }
}

const restoreVersion = async (version) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复到版本 "${version.version}" 吗？此操作将覆盖当前字典数据。`,
      '确认恢复版本',
      {
        confirmButtonText: '恢复',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里需要实现版本恢复API
    ElMessage.info('版本恢复功能待实现')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复版本失败:', error)
      ElMessage.error('恢复失败')
    }
  }
}

const exportVersion = (version) => {
  try {
    const data = {
      version: version.version,
      created_at: version.created_at,
      created_by: version.created_by,
      change_log: version.change_log,
      items: JSON.parse(version.items_data || '[]')
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json;charset=utf-8' 
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.dictionary.name}_v${version.version}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('版本数据导出成功')
  } catch (error) {
    console.error('导出版本失败:', error)
    ElMessage.error('导出失败')
  }
}

const refreshVersions = () => {
  loadVersionHistory()
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadVersionHistory()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style scoped>
.version-history {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dict-info {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dict-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.dict-code {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #6c757d;
  font-size: 14px;
}

.version-list {
  max-height: 500px;
  overflow-y: auto;
}

.version-card {
  margin-bottom: 16px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.version-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-meta {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.version-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.change-log strong {
  color: #2c3e50;
}

.change-log p {
  margin: 4px 0 0 0;
  color: #6c757d;
  font-size: 14px;
}

.version-stats {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.empty-versions {
  padding: 40px 0;
}

.version-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.version-data h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.code-text {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.version-compare {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.compare-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.compare-item {
  text-align: center;
  flex: 1;
}

.compare-item h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.compare-item p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.compare-divider {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  margin: 0 20px;
}

.compare-content {
  min-height: 300px;
}

.stats-compare {
  padding: 16px 0;
}

.stats-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.stats-card h5 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.stats-card p {
  margin: 4px 0;
  font-size: 14px;
  color: #6c757d;
}

/* 图标样式 */
.icon-rotate-ccw::before { content: "↶"; }
.icon-download::before { content: "📥"; }
</style>
