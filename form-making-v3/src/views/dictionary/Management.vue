<template>
  <div class="dictionary-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">字典管理</h1>
        <p class="page-description">管理系统字典数据，为表单字段提供数据源</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshDictionaries">
          <i class="icon-refresh-cw"></i>
          刷新
        </el-button>
        <el-button @click="manageCategoriesVisible = true">
          <i class="icon-folder"></i>
          分类管理
        </el-button>
        <el-button type="primary" @click="createDictionary">
          <i class="icon-plus"></i>
          创建字典
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索字典名称或编码..."
          prefix-icon="Search"
          clearable
          style="width: 300px"
        />
        <el-select v-model="selectedCategory" placeholder="选择分类" clearable style="width: 200px">
          <el-option label="全部分类" value="" />
          <el-option 
            v-for="category in categories" 
            :key="category.id" 
            :label="category.name" 
            :value="category.id" 
          />
        </el-select>
        <el-select v-model="selectedStatus" placeholder="选择状态" clearable style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="已启用" value="active" />
          <el-option label="已停用" value="inactive" />
          <el-option label="草稿" value="draft" />
        </el-select>
      </div>
      <div class="filter-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="table">
            <i class="icon-list"></i>
          </el-radio-button>
          <el-radio-button label="card">
            <i class="icon-grid"></i>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="table-view">
      <el-table 
        :data="filteredDictionaries" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="字典名称" min-width="150">
          <template #default="{ row }">
            <div class="dict-name">
              <strong>{{ row.name }}</strong>
              <span class="dict-code">{{ row.code }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category_name" label="分类" width="120" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="item_count" label="字典项数" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" type="info">{{ row.item_count }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)" 
              size="small"
            >
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="(command) => handleDictionaryAction(command, row)">
              <el-button size="small">
                操作 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <i class="icon-edit"></i> 编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="view">
                    <i class="icon-eye"></i> 查看详情
                  </el-dropdown-item>
                  <el-dropdown-item command="copy">
                    <i class="icon-copy"></i> 复制
                  </el-dropdown-item>
                  <el-dropdown-item command="usage">
                    <i class="icon-link"></i> 使用情况
                  </el-dropdown-item>
                  <el-dropdown-item command="versions">
                    <i class="icon-clock"></i> 版本历史
                  </el-dropdown-item>
                  <el-dropdown-item 
                    command="toggle"
                    :class="row.status === 'active' ? 'text-warning' : 'text-success'"
                  >
                    <i :class="row.status === 'active' ? 'icon-pause' : 'icon-play'"></i>
                    {{ row.status === 'active' ? '停用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" class="text-danger">
                    <i class="icon-trash-2"></i> 删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="card-view">
      <div class="cards-grid" v-loading="loading">
        <div 
          v-for="dictionary in filteredDictionaries" 
          :key="dictionary.id" 
          class="dictionary-card"
        >
          <div class="card-header">
            <div class="dict-info">
              <h3 class="dict-title">{{ dictionary.name }}</h3>
              <span class="dict-code">{{ dictionary.code }}</span>
            </div>
            <div class="dict-status">
              <el-tag 
                :type="getStatusType(dictionary.status)" 
                size="small"
              >
                {{ getStatusName(dictionary.status) }}
              </el-tag>
            </div>
          </div>
          <div class="card-content">
            <p class="dict-description">{{ dictionary.description || '暂无描述' }}</p>
            <div class="dict-meta">
              <span class="meta-item">
                <i class="icon-folder"></i>
                {{ dictionary.category_name }}
              </span>
              <span class="meta-item">
                <i class="icon-list"></i>
                {{ dictionary.item_count }} 项
              </span>
              <span class="meta-item">
                <i class="icon-clock"></i>
                {{ formatDate(dictionary.updated_at) }}
              </span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click="editDictionary(dictionary)">
              <i class="icon-edit"></i>
              编辑
            </el-button>
            <el-button size="small" @click="viewDictionary(dictionary)">
              <i class="icon-eye"></i>
              查看
            </el-button>
            <el-dropdown @command="(command) => handleDictionaryAction(command, dictionary)">
              <el-button size="small">
                更多 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="copy">
                    <i class="icon-copy"></i> 复制
                  </el-dropdown-item>
                  <el-dropdown-item command="usage">
                    <i class="icon-link"></i> 使用情况
                  </el-dropdown-item>
                  <el-dropdown-item command="versions">
                    <i class="icon-clock"></i> 版本历史
                  </el-dropdown-item>
                  <el-dropdown-item 
                    command="toggle"
                    :class="dictionary.status === 'active' ? 'text-warning' : 'text-success'"
                  >
                    <i :class="dictionary.status === 'active' ? 'icon-pause' : 'icon-play'"></i>
                    {{ dictionary.status === 'active' ? '停用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" class="text-danger">
                    <i class="icon-trash-2"></i> 删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalDictionaries"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 分类管理对话框 -->
    <CategoryManagement 
      v-model:visible="manageCategoriesVisible"
      @refresh="loadCategories"
    />

    <!-- 字典编辑对话框 -->
    <DictionaryEditor
      v-model:visible="editorVisible"
      :dictionary="currentDictionary"
      :categories="categories"
      @saved="handleDictionarySaved"
    />

    <!-- 字典详情对话框 -->
    <DictionaryDetail
      v-model:visible="detailVisible"
      :dictionary="currentDictionary"
    />

    <!-- 使用情况对话框 -->
    <UsageDialog
      v-model:visible="usageVisible"
      :dictionary="currentDictionary"
    />

    <!-- 版本历史对话框 -->
    <VersionHistory
      v-model:visible="versionVisible"
      :dictionary="currentDictionary"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDictionaries,
  getDictionaryCategories,
  deleteDictionary,
  updateDictionary
} from '@/util/api'
import CategoryManagement from './components/CategoryManagement.vue'
import DictionaryEditor from './components/DictionaryEditor.vue'
import DictionaryDetail from './components/DictionaryDetail.vue'
import UsageDialog from './components/UsageDialog.vue'
import VersionHistory from './components/VersionHistory.vue'

// 响应式数据
const loading = ref(false)
const dictionaries = ref([])
const categories = ref([])
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const viewMode = ref('table')
const currentPage = ref(1)
const pageSize = ref(20)
const totalDictionaries = ref(0)

// 对话框状态
const manageCategoriesVisible = ref(false)
const editorVisible = ref(false)
const detailVisible = ref(false)
const usageVisible = ref(false)
const versionVisible = ref(false)
const currentDictionary = ref(null)

// 计算属性
const filteredDictionaries = computed(() => {
  let filtered = dictionaries.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(dict =>
      dict.name.toLowerCase().includes(query) ||
      dict.code.toLowerCase().includes(query) ||
      (dict.description && dict.description.toLowerCase().includes(query))
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(dict => dict.category_id === selectedCategory.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(dict => dict.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const loadDictionaries = async () => {
  try {
    loading.value = true
    const response = await getDictionaries({
      page: currentPage.value,
      limit: pageSize.value,
      category_id: selectedCategory.value,
      search: searchQuery.value,
      status: selectedStatus.value
    })

    if (response.success) {
      dictionaries.value = response.data.list || []
      totalDictionaries.value = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('加载字典列表失败:', error)
    ElMessage.error('加载字典列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
    ElMessage.error('加载字典分类失败')
  }
}

const refreshDictionaries = () => {
  currentPage.value = 1
  loadDictionaries()
}

const createDictionary = () => {
  currentDictionary.value = null
  editorVisible.value = true
}

const editDictionary = (dictionary) => {
  currentDictionary.value = dictionary
  editorVisible.value = true
}

const viewDictionary = (dictionary) => {
  currentDictionary.value = dictionary
  detailVisible.value = true
}

const copyDictionary = async (dictionary) => {
  try {
    const newDict = {
      ...dictionary,
      name: `${dictionary.name} - 副本`,
      code: `${dictionary.code}_copy_${Date.now()}`,
      status: 'draft'
    }
    delete newDict.id
    delete newDict.created_at
    delete newDict.updated_at

    currentDictionary.value = newDict
    editorVisible.value = true
  } catch (error) {
    console.error('复制字典失败:', error)
    ElMessage.error('复制字典失败')
  }
}

const toggleDictionaryStatus = async (dictionary) => {
  try {
    const newStatus = dictionary.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '启用' : '停用'

    await ElMessageBox.confirm(
      `确定要${action}字典 "${dictionary.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateDictionary(dictionary.id, { status: newStatus })
    ElMessage.success(`字典${action}成功`)
    loadDictionaries()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换字典状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const deleteDictionaryItem = async (dictionary) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典 "${dictionary.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteDictionary(dictionary.id)
    ElMessage.success('字典删除成功')
    loadDictionaries()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleDictionaryAction = (command, dictionary) => {
  switch (command) {
    case 'edit':
      editDictionary(dictionary)
      break
    case 'view':
      viewDictionary(dictionary)
      break
    case 'copy':
      copyDictionary(dictionary)
      break
    case 'usage':
      currentDictionary.value = dictionary
      usageVisible.value = true
      break
    case 'versions':
      currentDictionary.value = dictionary
      versionVisible.value = true
      break
    case 'toggle':
      toggleDictionaryStatus(dictionary)
      break
    case 'delete':
      deleteDictionaryItem(dictionary)
      break
  }
}

const handleDictionarySaved = () => {
  editorVisible.value = false
  loadDictionaries()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadDictionaries()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadDictionaries()
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    draft: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用',
    draft: '草稿'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听搜索和筛选变化
watch([searchQuery, selectedCategory, selectedStatus], () => {
  currentPage.value = 1
  loadDictionaries()
}, { debounce: 300 })

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
  loadDictionaries()
})
</script>

<style scoped>
.dictionary-management {
  padding: 24px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.header-content p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
}

.table-view {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.dict-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dict-code {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.card-view {
  margin-bottom: 24px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.dictionary-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.dictionary-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.dict-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.card-content {
  padding: 16px 20px;
}

.dict-description {
  margin: 0 0 16px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dict-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6c757d;
}

.meta-item i {
  width: 14px;
  height: 14px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #f0f0f0;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-success {
  color: #28a745 !important;
}

/* 图标样式 */
.icon-refresh-cw::before { content: "🔄"; }
.icon-folder::before { content: "📁"; }
.icon-plus::before { content: "➕"; }
.icon-list::before { content: "📋"; }
.icon-grid::before { content: "⊞"; }
.icon-edit::before { content: "✏️"; }
.icon-eye::before { content: "👁"; }
.icon-copy::before { content: "📋"; }
.icon-link::before { content: "🔗"; }
.icon-clock::before { content: "🕐"; }
.icon-pause::before { content: "⏸"; }
.icon-play::before { content: "▶️"; }
.icon-trash-2::before { content: "🗑"; }
</style>
