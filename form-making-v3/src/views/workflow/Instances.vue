<template>
  <PlaceholderPage
    title="流程实例"
    description="查看和管理正在运行的流程实例，监控流程执行状态"
    :show-actions="true"
    create-text="启动流程"
    @create="startInstance"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'WorkflowInstances',
  components: {
    PlaceholderPage
  },
  methods: {
    startInstance() {
      console.log('启动流程')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
