<template>
  <PlaceholderPage
    title="流程设计"
    description="可视化设计业务流程，支持审批流、业务流等各种工作流程"
    :show-actions="true"
    create-text="创建流程"
    @create="createWorkflow"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'WorkflowDesigner',
  components: {
    PlaceholderPage
  },
  methods: {
    createWorkflow() {
      console.log('创建流程')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
