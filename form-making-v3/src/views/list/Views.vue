<template>
  <PlaceholderPage
    title="列表视图"
    description="管理和查看已创建的列表视图，支持预览、编辑和删除操作"
    :show-actions="true"
    create-text="创建视图"
    @create="createView"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'ListView',
  components: {
    PlaceholderPage
  },
  methods: {
    createView() {
      console.log('创建视图')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
