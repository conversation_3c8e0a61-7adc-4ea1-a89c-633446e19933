<template>
  <PlaceholderPage
    title="列表设计器"
    description="可视化设计数据列表视图，支持查询条件、列配置、操作按钮等功能"
    :show-actions="true"
    create-text="创建列表"
    @create="createList"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'ListDesigner',
  components: {
    PlaceholderPage
  },
  methods: {
    createList() {
      console.log('创建列表')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
