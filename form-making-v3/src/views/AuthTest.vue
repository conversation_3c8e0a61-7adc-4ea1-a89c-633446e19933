<template>
  <div class="auth-test">
    <h2>认证系统测试</h2>
    
    <div class="test-section">
      <h3>认证状态</h3>
      <p>是否已认证: {{ authStore.isAuthenticated }}</p>
      <p>用户信息: {{ authStore.user ? JSON.stringify(authStore.user, null, 2) : '未登录' }}</p>
      <p>权限: {{ authStore.permissions.join(', ') || '无' }}</p>
      <p>角色: {{ authStore.roles.join(', ') || '无' }}</p>
    </div>

    <div class="test-section">
      <h3>登录测试</h3>
      <el-form :model="loginForm" style="max-width: 400px;">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="admin" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="admin123" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testLogin" :loading="authStore.isLoading">
            测试登录
          </el-button>
          <el-button @click="testLogout">
            测试登出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="test-section">
      <h3>API测试</h3>
      <el-button @click="testHealthCheck">健康检查</el-button>
      <el-button @click="testCurrentUser">获取当前用户</el-button>
      <div v-if="apiResult" class="api-result">
        <h4>API结果:</h4>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const authStore = useAuthStore()

const loginForm = reactive({
  username: 'admin',
  password: 'admin123'
})

const apiResult = ref(null)

const testLogin = async () => {
  try {
    const result = await authStore.login(loginForm)
    if (result.success) {
      ElMessage.success('登录成功!')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('登录测试失败:', error)
    ElMessage.error('登录测试失败')
  }
}

const testLogout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('登出成功!')
  } catch (error) {
    console.error('登出测试失败:', error)
    ElMessage.error('登出测试失败')
  }
}

const testHealthCheck = async () => {
  try {
    const response = await axios.get('http://localhost:3001/health')
    apiResult.value = response.data
    ElMessage.success('健康检查成功')
  } catch (error) {
    console.error('健康检查失败:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('健康检查失败')
  }
}

const testCurrentUser = async () => {
  try {
    const user = await authStore.getCurrentUser()
    apiResult.value = user
    ElMessage.success('获取用户信息成功')
  } catch (error) {
    console.error('获取用户信息失败:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('获取用户信息失败')
  }
}
</script>

<style scoped>
.auth-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.api-result {
  margin-top: 15px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.api-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
