<template>
  <div class="vuexy-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Analytics Dashboard</h1>
        <p class="page-description">医疗低代码平台数据概览</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createForm" class="create-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建表单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-header">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path v-if="stat.key === 'forms'" d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
              <polyline v-if="stat.key === 'forms'" points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              <line v-if="stat.key === 'forms'" x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
              <line v-if="stat.key === 'forms'" x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>

              <line v-if="stat.key === 'submissions'" x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
              <polygon v-if="stat.key === 'submissions'" points="22,2 15,22 11,13 2,9" fill="currentColor"/>

              <path v-if="stat.key === 'users'" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
              <circle v-if="stat.key === 'users'" cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>

              <polyline v-if="stat.key === 'workflows'" points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <polyline v-if="stat.trend > 0" points="23 6 13.5 15.5 8.5 10.5 1 18" stroke="currentColor" stroke-width="2"/>
              <polyline v-if="stat.trend <= 0" points="1 18 8.5 10.5 13.5 15.5 23 6" stroke="currentColor" stroke-width="2"/>
            </svg>
            {{ Math.abs(stat.trend) }}%
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
    
    <!-- 主要功能区域 -->
    <div class="main-sections">
      <!-- 快速操作 -->
      <div class="section-card">
        <div class="section-header">
          <h3>快速操作</h3>
          <div class="header-badge">4</div>
        </div>
        <div class="quick-actions">
          <div class="action-item" v-for="action in quickActions" :key="action.key" @click="handleQuickAction(action.key)">
            <div class="action-icon" :style="{ background: action.gradient }">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle v-if="action.key === 'create-form'" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-form'" x1="12" y1="8" x2="12" y2="16" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-form'" x1="8" y1="12" x2="16" y2="12" stroke="currentColor" stroke-width="2"/>

                <line v-if="action.key === 'create-list'" x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-list'" x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-list'" x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-list'" x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-list'" x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
                <line v-if="action.key === 'create-list'" x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>

                <polyline v-if="action.key === 'create-chart'" points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>

                <path v-if="action.key === 'manage-dictionary'" d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="2"/>
                <path v-if="action.key === 'manage-dictionary'" d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="action-content">
              <div class="action-title">{{ action.title }}</div>
              <div class="action-description">{{ action.description }}</div>
            </div>
            <div class="action-arrow">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 最近活动 -->
      <div class="section-card">
        <div class="section-header">
          <h3>最近活动</h3>
          <el-button text @click="viewAllActivities">查看全部</el-button>
        </div>
        <div class="activity-list">
          <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
            <div class="activity-avatar">
              <el-avatar :size="32" :src="activity.user.avatar">
                {{ activity.user.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="activity-content">
              <div class="activity-text">
                <strong>{{ activity.user.name }}</strong> {{ activity.action }} 
                <span class="activity-target">{{ activity.target }}</span>
              </div>
              <div class="activity-time">{{ formatTime(activity.time) }}</div>
            </div>
            <div class="activity-type" :class="activity.type">
              <i :class="getActivityIcon(activity.type)"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 表单使用统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>表单使用统计</h3>
          <el-select v-model="chartPeriod" size="small">
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
            <el-option label="最近90天" value="90d" />
          </el-select>
        </div>
        <div class="chart-content">
          <div id="formUsageChart" class="chart-container"></div>
        </div>
      </div>
      
      <!-- 科室分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>科室分布</h3>
        </div>
        <div class="chart-content">
          <div id="departmentChart" class="chart-container"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const chartPeriod = ref('7d')
    
    const stats = ref([
      {
        key: 'forms',
        label: '表单总数',
        value: '156',
        trend: 12,
        gradient: 'linear-gradient(135deg, #7367f0 0%, #9c88ff 100%)'
      },
      {
        key: 'submissions',
        label: '今日提交',
        value: '2,847',
        trend: 8,
        gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
      },
      {
        key: 'users',
        label: '活跃用户',
        value: '1,234',
        trend: -3,
        gradient: 'linear-gradient(135deg, #28c76f 0%, #06d79c 100%)'
      },
      {
        key: 'workflows',
        label: '工作流程',
        value: '89',
        trend: 15,
        gradient: 'linear-gradient(135deg, #00cfe8 0%, #0396ff 100%)'
      }
    ])
    
    const quickActions = ref([
      {
        key: 'create-form',
        title: '创建表单',
        description: '设计新的表单模板',
        gradient: 'linear-gradient(135deg, #7367f0 0%, #9c88ff 100%)'
      },
      {
        key: 'create-list',
        title: '创建列表',
        description: '设计数据列表视图',
        gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
      },
      {
        key: 'create-chart',
        title: '创建图表',
        description: '配置统计分析图表',
        gradient: 'linear-gradient(135deg, #28c76f 0%, #06d79c 100%)'
      },
      {
        key: 'manage-dictionary',
        title: '管理字典',
        description: '维护系统字典数据',
        gradient: 'linear-gradient(135deg, #00cfe8 0%, #0396ff 100%)'
      }
    ])
    
    const recentActivities = ref([
      {
        id: 1,
        user: { name: '张医生', avatar: '' },
        action: '创建了表单',
        target: '患者入院申请表',
        time: new Date(Date.now() - 1000 * 60 * 30),
        type: 'create'
      },
      {
        id: 2,
        user: { name: '李护士', avatar: '' },
        action: '提交了',
        target: '护理记录表',
        time: new Date(Date.now() - 1000 * 60 * 60),
        type: 'submit'
      },
      {
        id: 3,
        user: { name: '王主任', avatar: '' },
        action: '审批了',
        target: '手术申请流程',
        time: new Date(Date.now() - 1000 * 60 * 60 * 2),
        type: 'approve'
      },
      {
        id: 4,
        user: { name: '陈医生', avatar: '' },
        action: '修改了字典',
        target: '手术等级分类',
        time: new Date(Date.now() - 1000 * 60 * 60 * 4),
        type: 'edit'
      }
    ])
    
    // 方法
    const createForm = () => {
      router.push('/form/designer')
    }
    
    const handleQuickAction = (actionKey) => {
      const actionRoutes = {
        'create-form': '/form/designer',
        'create-list': '/list/designer',
        'create-chart': '/statistics/charts',
        'manage-dictionary': '/dictionary/items'
      }
      
      if (actionRoutes[actionKey]) {
        router.push(actionRoutes[actionKey])
      }
    }
    
    const viewAllActivities = () => {
      console.log('查看全部活动')
    }
    
    const formatTime = (time) => {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }
    
    const getActivityIcon = (type) => {
      const icons = {
        create: 'icon-plus',
        submit: 'icon-send',
        approve: 'icon-check',
        edit: 'icon-edit'
      }
      return icons[type] || 'icon-activity'
    }
    
    const initCharts = () => {
      // 这里将来会初始化图表
      console.log('初始化图表')
    }
    
    onMounted(() => {
      initCharts()
    })
    
    return {
      chartPeriod,
      stats,
      quickActions,
      recentActivities,
      createForm,
      handleQuickAction,
      viewAllActivities,
      formatTime,
      getActivityIcon
    }
  }
}
</script>

<style lang="scss" scoped>
.vuexy-dashboard {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .header-content {
    .page-title {
      font-size: 2rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 0.25rem 0;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-description {
      color: var(--text-light);
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .create-btn {
    background: var(--primary-gradient);
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 25px rgba(115, 103, 240, 0.3);
    }
  }
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .stat-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(115, 103, 240, 0.1);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(34, 41, 47, 0.1);
      border-color: rgba(115, 103, 240, 0.2);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;

        svg {
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;

        &.positive {
          color: var(--success-color);
          background: rgba(40, 199, 111, 0.1);
        }

        &.negative {
          color: var(--danger-color);
          background: rgba(234, 84, 85, 0.1);
        }

        svg {
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }

    .stat-content {
      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        color: var(--text-light);
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }
}

// 主要功能区域
.main-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;

  .section-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    border: 1px solid rgba(115, 103, 240, 0.1);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(115, 103, 240, 0.2);
      box-shadow: 0 10px 30px rgba(34, 41, 47, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
      }

      .header-badge {
        background: var(--primary-gradient);
        color: white;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        min-width: 1.5rem;
        text-align: center;
      }
    }
  }
}

// 快速操作
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;

    &:hover {
      background: rgba(115, 103, 240, 0.05);
      border-color: rgba(115, 103, 240, 0.1);
      transform: translateX(4px);
    }

    .action-icon {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .action-content {
      flex: 1;

      .action-title {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
      }

      .action-description {
        color: var(--text-light);
        font-size: 0.75rem;
        line-height: 1.4;
      }
    }

    .action-arrow {
      color: var(--text-light);
      transition: all 0.2s ease;
    }

    &:hover .action-arrow {
      color: var(--primary-color);
      transform: translateX(2px);
    }
  }
}

// 活动列表
.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(115, 103, 240, 0.1);
    transition: all 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(115, 103, 240, 0.02);
      border-radius: 0.5rem;
      margin: 0 -0.5rem;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }

    .activity-avatar {
      :deep(.el-avatar) {
        background: var(--primary-gradient);
        font-weight: 600;
        font-size: 0.75rem;
      }
    }

    .activity-content {
      flex: 1;

      .activity-text {
        font-size: 0.875rem;
        color: var(--text-primary);
        line-height: 1.4;
        margin-bottom: 0.125rem;

        .activity-target {
          color: var(--primary-color);
          font-weight: 600;
        }
      }

      .activity-time {
        font-size: 0.75rem;
        color: var(--text-light);
      }
    }

    .activity-type {
      width: 1.75rem;
      height: 1.75rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;

      &.create {
        background: linear-gradient(135deg, #28c76f 0%, #06d79c 100%);
        color: white;
      }

      &.submit {
        background: linear-gradient(135deg, #00cfe8 0%, #0396ff 100%);
        color: white;
      }

      &.approve {
        background: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);
        color: white;
      }

      &.edit {
        background: linear-gradient(135deg, #ff9f43 0%, #ff6b35 100%);
        color: white;
      }
    }
  }
}

// 图表区域
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;

  .chart-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    border: 1px solid rgba(115, 103, 240, 0.1);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(115, 103, 240, 0.2);
      box-shadow: 0 10px 30px rgba(34, 41, 47, 0.1);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
      }

      :deep(.el-select) {
        .el-input__inner {
          border: 1px solid rgba(115, 103, 240, 0.2);
          border-radius: 0.375rem;
          font-size: 0.75rem;
        }
      }
    }

    .chart-container {
      height: 300px;
      background: linear-gradient(135deg, rgba(115, 103, 240, 0.02) 0%, rgba(156, 136, 255, 0.02) 100%);
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-light);
      font-size: 0.875rem;
      border: 1px dashed rgba(115, 103, 240, 0.2);
      position: relative;

      &::before {
        content: '📊';
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .vuexy-dashboard {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .create-btn {
      align-self: stretch;
      justify-content: center;
    }
  }

  .main-sections {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
