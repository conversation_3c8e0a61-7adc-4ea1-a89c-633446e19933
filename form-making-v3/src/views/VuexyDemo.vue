<template>
  <div class="vuexy-demo">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Vuexy Design System</h1>
        <p class="page-description">医疗低代码平台设计系统演示</p>
      </div>
      <div class="header-actions">
        <el-button class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="M21 21l-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          搜索
        </el-button>
        <el-button type="primary" class="create-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-header">
          <div class="stat-icon" :style="{ background: stat.gradient }">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path v-if="stat.key === 'users'" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
              <circle v-if="stat.key === 'users'" cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              
              <path v-if="stat.key === 'revenue'" d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="currentColor" stroke-width="2"/>
              
              <polyline v-if="stat.key === 'orders'" points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
              
              <path v-if="stat.key === 'growth'" d="M3 3v18h18" stroke="currentColor" stroke-width="2"/>
              <path v-if="stat.key === 'growth'" d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <polyline v-if="stat.trend > 0" points="23 6 13.5 15.5 8.5 10.5 1 18" stroke="currentColor" stroke-width="2"/>
              <polyline v-if="stat.trend <= 0" points="1 18 8.5 10.5 13.5 15.5 23 6" stroke="currentColor" stroke-width="2"/>
            </svg>
            {{ Math.abs(stat.trend) }}%
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 功能卡片 -->
    <div class="feature-cards">
      <div class="feature-card" v-for="feature in features" :key="feature.key">
        <div class="feature-header">
          <div class="feature-icon" :style="{ background: feature.gradient }">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect v-if="feature.key === 'forms'" x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <line v-if="feature.key === 'forms'" x1="9" y1="9" x2="15" y2="9" stroke="currentColor" stroke-width="2"/>
              <line v-if="feature.key === 'forms'" x1="9" y1="12" x2="15" y2="12" stroke="currentColor" stroke-width="2"/>
              <line v-if="feature.key === 'forms'" x1="9" y1="15" x2="13" y2="15" stroke="currentColor" stroke-width="2"/>
              
              <path v-if="feature.key === 'analytics'" d="M3 3v18h18" stroke="currentColor" stroke-width="2"/>
              <path v-if="feature.key === 'analytics'" d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2"/>
              
              <circle v-if="feature.key === 'workflow'" cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path v-if="feature.key === 'workflow'" d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
              
              <path v-if="feature.key === 'users'" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
              <circle v-if="feature.key === 'users'" cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
        </div>
        <p class="feature-description">{{ feature.description }}</p>
        <div class="feature-actions">
          <el-button size="small" class="feature-btn">查看详情</el-button>
          <el-button size="small" type="primary" class="feature-primary-btn">立即使用</el-button>
        </div>
      </div>
    </div>

    <!-- 最新活动 -->
    <div class="activity-section">
      <div class="section-header">
        <h2>最新活动</h2>
        <el-button text class="view-all-btn">查看全部</el-button>
      </div>
      <div class="activity-list">
        <div class="activity-item" v-for="activity in activities" :key="activity.id">
          <div class="activity-avatar">
            <el-avatar :size="40" :src="activity.user.avatar">
              {{ activity.user.name.charAt(0) }}
            </el-avatar>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>{{ activity.user.name }}</strong> {{ activity.action }} 
              <span class="activity-target">{{ activity.target }}</span>
            </div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
          <div class="activity-type" :class="activity.type">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <circle v-if="activity.type === 'create'" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <line v-if="activity.type === 'create'" x1="12" y1="8" x2="12" y2="16" stroke="currentColor" stroke-width="2"/>
              <line v-if="activity.type === 'create'" x1="8" y1="12" x2="16" y2="12" stroke="currentColor" stroke-width="2"/>
              
              <line v-if="activity.type === 'edit'" x1="11" y1="4" x2="18" y2="11" stroke="currentColor" stroke-width="2"/>
              <path v-if="activity.type === 'edit'" d="M7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2-2v-3" stroke="currentColor" stroke-width="2"/>
              
              <polyline v-if="activity.type === 'approve'" points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
              
              <line v-if="activity.type === 'delete'" x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
              <line v-if="activity.type === 'delete'" x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'VuexyDemo',
  setup() {
    const stats = ref([
      {
        key: 'users',
        label: '总用户数',
        value: '21,459',
        trend: 29,
        gradient: 'linear-gradient(135deg, #7367f0 0%, #9c88ff 100%)'
      },
      {
        key: 'revenue',
        label: '月收入',
        value: '¥4,567',
        trend: 18,
        gradient: 'linear-gradient(135deg, #28c76f 0%, #06d79c 100%)'
      },
      {
        key: 'orders',
        label: '订单数量',
        value: '1,423',
        trend: -14,
        gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
      },
      {
        key: 'growth',
        label: '增长率',
        value: '62%',
        trend: 42,
        gradient: 'linear-gradient(135deg, #00cfe8 0%, #0396ff 100%)'
      }
    ])

    const features = ref([
      {
        key: 'forms',
        title: '表单设计器',
        description: '拖拽式表单设计，支持多种字段类型和复杂布局',
        gradient: 'linear-gradient(135deg, #7367f0 0%, #9c88ff 100%)'
      },
      {
        key: 'analytics',
        title: '数据分析',
        description: '实时数据统计和可视化图表，深入了解业务指标',
        gradient: 'linear-gradient(135deg, #28c76f 0%, #06d79c 100%)'
      },
      {
        key: 'workflow',
        title: '工作流程',
        description: '自定义审批流程，提高工作效率和协作能力',
        gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)'
      },
      {
        key: 'users',
        title: '用户管理',
        description: '完善的权限控制和角色管理，确保系统安全',
        gradient: 'linear-gradient(135deg, #00cfe8 0%, #0396ff 100%)'
      }
    ])

    const activities = ref([
      {
        id: 1,
        user: { name: '张医生', avatar: '' },
        action: '创建了表单',
        target: '患者入院申请表',
        time: new Date(Date.now() - 1000 * 60 * 30),
        type: 'create'
      },
      {
        id: 2,
        user: { name: '李护士', avatar: '' },
        action: '编辑了',
        target: '护理记录表',
        time: new Date(Date.now() - 1000 * 60 * 60),
        type: 'edit'
      },
      {
        id: 3,
        user: { name: '王主任', avatar: '' },
        action: '审批了',
        target: '手术申请流程',
        time: new Date(Date.now() - 1000 * 60 * 60 * 2),
        type: 'approve'
      },
      {
        id: 4,
        user: { name: '陈医生', avatar: '' },
        action: '删除了',
        target: '过期表单模板',
        time: new Date(Date.now() - 1000 * 60 * 60 * 4),
        type: 'delete'
      }
    ])

    const formatTime = (time) => {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }

    return {
      stats,
      features,
      activities,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.vuexy-demo {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .header-content {
    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 0.25rem 0;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-description {
      color: var(--text-light);
      margin: 0;
      font-size: 1rem;
    }
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;

    .action-btn {
      border: 1px solid rgba(115, 103, 240, 0.2);
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;
      background: var(--card-background);
      color: var(--text-primary);

      &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(115, 103, 240, 0.15);
      }
    }

    .create-btn {
      background: var(--primary-gradient);
      border: none;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(115, 103, 240, 0.3);
      }
    }
  }
}

// 统计卡片
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .stat-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(115, 103, 240, 0.1);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(34, 41, 47, 0.1);
      border-color: rgba(115, 103, 240, 0.2);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;

        &.positive {
          color: var(--success-color);
          background: rgba(40, 199, 111, 0.1);
        }

        &.negative {
          color: var(--danger-color);
          background: rgba(234, 84, 85, 0.1);
        }
      }
    }

    .stat-content {
      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        color: var(--text-light);
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }
}

// 功能卡片
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .feature-card {
    background: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(115, 103, 240, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(34, 41, 47, 0.1);
      border-color: rgba(115, 103, 240, 0.2);
    }

    .feature-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      .feature-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .feature-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
      }
    }

    .feature-description {
      color: var(--text-light);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .feature-actions {
      display: flex;
      gap: 0.75rem;

      .feature-btn {
        border: 1px solid rgba(115, 103, 240, 0.2);
        border-radius: 0.375rem;
        font-size: 0.875rem;

        &:hover {
          border-color: var(--primary-color);
          color: var(--primary-color);
        }
      }

      .feature-primary-btn {
        background: var(--primary-gradient);
        border: none;
        border-radius: 0.375rem;
        font-size: 0.875rem;

        &:hover {
          box-shadow: 0 4px 12px rgba(115, 103, 240, 0.3);
        }
      }
    }
  }
}

// 活动部分
.activity-section {
  background: var(--card-background);
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  border: 1px solid rgba(115, 103, 240, 0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }

    .view-all-btn {
      color: var(--primary-color);
      font-weight: 500;

      &:hover {
        color: var(--primary-color);
        text-decoration: underline;
      }
    }
  }

  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid rgba(115, 103, 240, 0.1);
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: rgba(115, 103, 240, 0.02);
        border-radius: 0.5rem;
        margin: 0 -0.75rem;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
      }

      .activity-avatar {
        :deep(.el-avatar) {
          background: var(--primary-gradient);
          font-weight: 600;
          font-size: 1rem;
        }
      }

      .activity-content {
        flex: 1;

        .activity-text {
          font-size: 0.875rem;
          color: var(--text-primary);
          line-height: 1.4;
          margin-bottom: 0.25rem;

          .activity-target {
            color: var(--primary-color);
            font-weight: 600;
          }
        }

        .activity-time {
          font-size: 0.75rem;
          color: var(--text-light);
        }
      }

      .activity-type {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.create {
          background: linear-gradient(135deg, #28c76f 0%, #06d79c 100%);
          color: white;
        }

        &.edit {
          background: linear-gradient(135deg, #ff9f43 0%, #ff6b35 100%);
          color: white;
        }

        &.approve {
          background: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);
          color: white;
        }

        &.delete {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
          color: white;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .feature-cards {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .vuexy-demo {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .header-content .page-title {
      font-size: 2rem;
    }

    .header-actions {
      align-self: stretch;

      .action-btn,
      .create-btn {
        flex: 1;
        justify-content: center;
      }
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .feature-cards {
    grid-template-columns: 1fr;
  }
}
</style>
