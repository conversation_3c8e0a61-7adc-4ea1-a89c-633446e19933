<template>
  <div style="padding: 20px;">
    <h1>简单测试页面</h1>
    
    <div style="margin: 20px 0;">
      <h3>基础信息</h3>
      <p>Vue版本: {{ vueVersion }}</p>
      <p>当前时间: {{ currentTime }}</p>
      <p>当前路由: {{ $route.path }}</p>
    </div>

    <div style="margin: 20px 0;">
      <h3>Element Plus测试</h3>
      <el-button type="primary" @click="showMessage">显示消息</el-button>
      <el-button type="success" @click="testAPI">测试API</el-button>
      <el-button type="warning" @click="testLogin">测试登录</el-button>
    </div>

    <div style="margin: 20px 0;">
      <h3>认证状态</h3>
      <p>认证Store是否可用: {{ authStoreAvailable ? '是' : '否' }}</p>
      <p v-if="authStoreAvailable">认证状态: {{ authStore.isAuthenticated ? '已认证' : '未认证' }}</p>
      <p v-if="authStoreAvailable">用户信息: {{ authStore.user ? authStore.user.username : '无' }}</p>
    </div>

    <div v-if="apiResult" style="margin: 20px 0;">
      <h3>API测试结果</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(apiResult, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { version } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import axios from 'axios'

const vueVersion = version
const currentTime = ref(new Date().toLocaleString())
const apiResult = ref(null)

// 检查认证store是否可用
let authStore = null
let authStoreAvailable = false

try {
  authStore = useAuthStore()
  authStoreAvailable = true
} catch (error) {
  console.error('认证Store不可用:', error)
  authStoreAvailable = false
}

const showMessage = () => {
  ElMessage.success('Element Plus 工作正常!')
}

const testAPI = async () => {
  try {
    const response = await axios.get('http://localhost:3001/health')
    apiResult.value = response.data
    ElMessage.success('API连接成功!')
  } catch (error) {
    console.error('API测试失败:', error)
    apiResult.value = { error: error.message }
    ElMessage.error('API连接失败: ' + error.message)
  }
}

const testLogin = async () => {
  if (!authStoreAvailable) {
    ElMessage.error('认证Store不可用')
    return
  }

  try {
    const result = await authStore.login('admin', 'admin123')
    apiResult.value = { loginResult: result }
    ElMessage.success('登录测试成功!')
  } catch (error) {
    console.error('登录测试失败:', error)
    apiResult.value = { loginError: error.message }
    ElMessage.error('登录测试失败: ' + error.message)
  }
}

onMounted(() => {
  // 每秒更新时间
  setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})
</script>
