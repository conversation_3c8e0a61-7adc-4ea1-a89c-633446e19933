<template>
  <PlaceholderPage
    title="权限管理"
    description="管理系统权限点，定义功能模块的访问控制规则"
    :show-actions="true"
    create-text="添加权限"
    @create="createPermission"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'SystemPermissions',
  components: {
    PlaceholderPage
  },
  methods: {
    createPermission() {
      console.log('添加权限')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
