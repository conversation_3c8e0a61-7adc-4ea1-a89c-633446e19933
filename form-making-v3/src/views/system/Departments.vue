<template>
  <PlaceholderPage
    title="部门管理"
    description="管理医院科室和部门结构，支持层级组织架构和人员分配"
    :show-actions="true"
    create-text="添加部门"
    @create="createDepartment"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'SystemDepartments',
  components: {
    PlaceholderPage
  },
  methods: {
    createDepartment() {
      console.log('添加部门')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
