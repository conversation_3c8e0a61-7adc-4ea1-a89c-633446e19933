<template>
  <PlaceholderPage
    title="用户管理"
    description="管理系统用户账户，包括用户信息、角色分配和权限设置"
    :show-actions="true"
    create-text="添加用户"
    @create="createUser"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'SystemUsers',
  components: {
    PlaceholderPage
  },
  methods: {
    createUser() {
      console.log('添加用户')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
