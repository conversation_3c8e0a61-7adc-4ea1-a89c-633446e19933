<template>
  <PlaceholderPage
    title="角色管理"
    description="定义和管理系统角色，配置角色权限和功能访问范围"
    :show-actions="true"
    create-text="创建角色"
    @create="createRole"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'SystemRoles',
  components: {
    PlaceholderPage
  },
  methods: {
    createRole() {
      console.log('创建角色')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
