<template>
  <PlaceholderPage
    title="菜单管理"
    description="配置系统菜单结构，支持动态菜单和权限控制"
    :show-actions="true"
    create-text="添加菜单"
    @create="createMenu"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'SystemMenus',
  components: {
    PlaceholderPage
  },
  methods: {
    createMenu() {
      console.log('添加菜单')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
