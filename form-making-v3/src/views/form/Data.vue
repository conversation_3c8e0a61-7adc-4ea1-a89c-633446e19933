<template>
  <div class="form-data">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ formName || '表单数据' }}</h1>
        <p class="page-description">查看和管理表单提交的数据</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData">
          <i class="icon-refresh-cw"></i>
          刷新
        </el-button>
        <el-button @click="exportData">
          <i class="icon-download"></i>
          导出数据
        </el-button>
        <el-button @click="goBack">
          <i class="icon-arrow-left"></i>
          返回
        </el-button>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-number">{{ totalSubmissions }}</div>
        <div class="stat-label">总提交数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ todaySubmissions }}</div>
        <div class="stat-label">今日提交</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ weekSubmissions }}</div>
        <div class="stat-label">本周提交</div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <el-table
        :data="submissions"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column
          v-for="column in dynamicColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ getFieldValue(row.form_data, column.prop) }}
          </template>
        </el-table-column>
        <el-table-column prop="submitted_by" label="提交者" width="120" />
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.submitted_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="提交详情"
      width="60%"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedSubmission" class="detail-content">
        <div class="detail-info">
          <p><strong>提交者：</strong>{{ selectedSubmission.submitted_by || '匿名' }}</p>
          <p><strong>提交时间：</strong>{{ formatDate(selectedSubmission.submitted_at) }}</p>
        </div>
        <div class="detail-data">
          <h4>表单数据：</h4>
          <div
            v-for="(value, key) in selectedSubmission.form_data"
            :key="key"
            class="data-item"
          >
            <span class="data-label">{{ getFieldLabel(key) }}：</span>
            <span class="data-value">{{ formatValue(value) }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getFormById } from '../../util/api'

export default {
  name: 'FormData',
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(false)
    const formId = ref(route.query.id)
    const formName = ref(route.query.name || '')
    const formConfig = ref(null)
    const submissions = ref([])
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    const detailVisible = ref(false)
    const selectedSubmission = ref(null)
    
    // 统计数据
    const totalSubmissions = computed(() => submissions.value.length)
    const todaySubmissions = computed(() => {
      const today = new Date().toDateString()
      return submissions.value.filter(item => 
        new Date(item.submitted_at).toDateString() === today
      ).length
    })
    const weekSubmissions = computed(() => {
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return submissions.value.filter(item => 
        new Date(item.submitted_at) >= weekAgo
      ).length
    })
    
    // 动态列配置
    const dynamicColumns = computed(() => {
      if (!formConfig.value || !formConfig.value.list) return []
      
      return formConfig.value.list
        .filter(field => field.type !== 'button')
        .map(field => ({
          prop: field.model,
          label: field.label,
          width: getColumnWidth(field.type)
        }))
    })
    
    // 获取列宽度
    const getColumnWidth = (type) => {
      const widthMap = {
        'input': 150,
        'textarea': 200,
        'select': 120,
        'radio': 100,
        'checkbox': 150,
        'date': 120,
        'datetime': 160,
        'number': 100
      }
      return widthMap[type] || 150
    }
    
    // 获取字段值
    const getFieldValue = (formData, fieldKey) => {
      const value = formData[fieldKey]
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return value || '-'
    }
    
    // 获取字段标签
    const getFieldLabel = (fieldKey) => {
      if (!formConfig.value || !formConfig.value.list) return fieldKey
      
      const field = formConfig.value.list.find(f => f.model === fieldKey)
      return field ? field.label : fieldKey
    }
    
    // 格式化值
    const formatValue = (value) => {
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 2)
      }
      return value || '-'
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 加载表单配置和数据
    const loadData = async () => {
      if (!formId.value) {
        ElMessage.error('缺少表单ID参数')
        return
      }
      
      try {
        loading.value = true
        
        // 获取表单配置
        const formResponse = await getFormById(formId.value)
        if (formResponse.success) {
          formConfig.value = formResponse.data.form_config
          if (!formName.value) {
            formName.value = formResponse.data.name
          }
        }
        
        // 模拟提交数据（实际应该调用API获取）
        submissions.value = generateMockData()
        total.value = submissions.value.length
        
      } catch (error) {
        console.error('加载数据失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    // 生成模拟数据
    const generateMockData = () => {
      const mockData = []
      for (let i = 1; i <= 25; i++) {
        const data = {}
        if (formConfig.value && formConfig.value.list) {
          formConfig.value.list.forEach(field => {
            if (field.model) {
              data[field.model] = generateMockValue(field)
            }
          })
        }
        
        mockData.push({
          id: i,
          form_data: data,
          submitted_by: `用户${i}`,
          submitted_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
        })
      }
      return mockData
    }
    
    // 生成模拟值
    const generateMockValue = (field) => {
      switch (field.type) {
        case 'input':
          return `测试${field.label}${Math.floor(Math.random() * 100)}`
        case 'select':
        case 'radio':
          return field.options && field.options.length > 0 
            ? field.options[Math.floor(Math.random() * field.options.length)].value
            : '选项1'
        case 'checkbox':
          return field.options && field.options.length > 0
            ? field.options.slice(0, Math.floor(Math.random() * field.options.length) + 1).map(opt => opt.value)
            : ['选项1']
        case 'date':
          return new Date().toISOString().split('T')[0]
        case 'datetime':
          return new Date().toISOString()
        case 'number':
          return Math.floor(Math.random() * 1000)
        case 'textarea':
          return `这是${field.label}的详细内容...`
        default:
          return `${field.label}值`
      }
    }
    
    // 刷新数据
    const refreshData = () => {
      loadData()
      ElMessage.success('数据已刷新')
    }
    
    // 导出数据
    const exportData = () => {
      ElMessage.info('导出功能开发中...')
    }
    
    // 返回
    const goBack = () => {
      router.push('/form/management')
    }
    
    // 查看详情
    const viewDetail = (row) => {
      selectedSubmission.value = row
      detailVisible.value = true
    }
    
    // 关闭详情
    const handleCloseDetail = () => {
      detailVisible.value = false
      selectedSubmission.value = null
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      loadData()
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadData()
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      formName,
      submissions,
      currentPage,
      pageSize,
      total,
      detailVisible,
      selectedSubmission,
      totalSubmissions,
      todaySubmissions,
      weekSubmissions,
      dynamicColumns,
      getFieldValue,
      getFieldLabel,
      formatValue,
      formatDate,
      refreshData,
      exportData,
      goBack,
      viewDetail,
      handleCloseDetail,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style lang="scss" scoped>
.form-data {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  h1 {
    margin: 0 0 5px 0;
    font-size: 24px;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.header-actions {
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.stats-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
  }
  
  .stat-label {
    color: #666;
    font-size: 14px;
  }
}

.data-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.pagination-wrapper {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #eee;
}

.detail-content {
  .detail-info {
    margin-bottom: 20px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
    }
  }
  
  .detail-data {
    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }
  }
  
  .data-item {
    display: flex;
    margin-bottom: 10px;
    
    .data-label {
      min-width: 120px;
      font-weight: bold;
      color: #666;
    }
    
    .data-value {
      flex: 1;
      word-break: break-all;
    }
  }
}
</style>
