<template>
  <div class="form-designer">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">表单设计器</h1>
        <p class="page-description">可视化设计表单，支持拖拽组件、属性配置和实时预览</p>
      </div>
      <div class="header-actions">
        <el-button @click="previewForm">
          <i class="icon-eye"></i>
          预览
        </el-button>
        <el-button @click="saveForm">
          <i class="icon-save"></i>
          保存
        </el-button>
        <el-button type="primary" @click="publishForm">
          <i class="icon-send"></i>
          发布
        </el-button>
      </div>
    </div>
    
    <!-- 设计器主体 -->
    <div class="designer-container">
      <!-- 组件库 -->
      <div class="component-panel">
        <div class="panel-header">
          <h3>组件库</h3>
        </div>
        <div class="component-list">
          <div class="component-category">
            <h4>基础组件</h4>
            <div class="component-items">
              <div 
                v-for="component in basicComponents" 
                :key="component.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart(component)"
              >
                <i :class="component.icon"></i>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>
          
          <div class="component-category">
            <h4>高级组件</h4>
            <div class="component-items">
              <div 
                v-for="component in advancedComponents" 
                :key="component.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart(component)"
              >
                <i :class="component.icon"></i>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 设计画布 -->
      <div class="design-canvas">
        <div class="canvas-header">
          <div class="canvas-title">
            <el-input 
              v-model="formConfig.title" 
              placeholder="请输入表单标题"
              class="title-input"
            />
          </div>
          <div class="canvas-tools">
            <el-button-group size="small">
              <el-button @click="undoAction">
                <i class="icon-undo"></i>
              </el-button>
              <el-button @click="redoAction">
                <i class="icon-redo"></i>
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <div 
          class="canvas-content"
          @drop="handleDrop"
          @dragover.prevent
          @dragenter.prevent
        >
          <div v-if="formFields.length === 0" class="empty-canvas">
            <div class="empty-content">
              <i class="icon-plus-circle"></i>
              <p>拖拽左侧组件到此处开始设计表单</p>
            </div>
          </div>
          
          <div v-else class="form-fields">
            <div 
              v-for="(field, index) in formFields" 
              :key="field.id"
              class="form-field"
              :class="{ active: selectedFieldIndex === index }"
              @click="selectField(index)"
            >
              <div class="field-content">
                <component 
                  :is="getFieldComponent(field.type)"
                  v-bind="field.props"
                  :model-value="field.defaultValue"
                />
              </div>
              <div class="field-actions">
                <el-button size="small" @click.stop="editField(index)">
                  <i class="icon-edit"></i>
                </el-button>
                <el-button size="small" @click.stop="deleteField(index)">
                  <i class="icon-trash"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 属性配置 -->
      <div class="property-panel">
        <div class="panel-header">
          <h3>属性配置</h3>
        </div>
        <div class="property-content">
          <div v-if="selectedField" class="field-properties">
            <el-form label-position="top" size="small">
              <el-form-item label="字段标签">
                <el-input v-model="selectedField.label" />
              </el-form-item>
              <el-form-item label="字段名称">
                <el-input v-model="selectedField.name" />
              </el-form-item>
              <el-form-item label="占位符">
                <el-input v-model="selectedField.props.placeholder" />
              </el-form-item>
              <el-form-item label="是否必填">
                <el-switch v-model="selectedField.required" />
              </el-form-item>
              <el-form-item label="默认值">
                <el-input v-model="selectedField.defaultValue" />
              </el-form-item>
            </el-form>
          </div>
          <div v-else class="no-selection">
            <p>请选择一个字段进行配置</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'FormDesigner',
  setup() {
    // 响应式数据
    const formConfig = ref({
      title: '新建表单',
      description: ''
    })
    
    const formFields = ref([])
    const selectedFieldIndex = ref(-1)
    
    // 计算属性
    const selectedField = computed(() => {
      return selectedFieldIndex.value >= 0 ? formFields.value[selectedFieldIndex.value] : null
    })
    
    // 组件库数据
    const basicComponents = ref([
      { type: 'input', label: '单行文本', icon: 'icon-edit' },
      { type: 'textarea', label: '多行文本', icon: 'icon-file-text' },
      { type: 'select', label: '下拉选择', icon: 'icon-chevron-down' },
      { type: 'radio', label: '单选框', icon: 'icon-circle' },
      { type: 'checkbox', label: '复选框', icon: 'icon-check' },
      { type: 'date', label: '日期选择', icon: 'icon-calendar' },
      { type: 'number', label: '数字输入', icon: 'icon-hash' }
    ])
    
    const advancedComponents = ref([
      { type: 'upload', label: '文件上传', icon: 'icon-upload' },
      { type: 'table', label: '表格', icon: 'icon-grid' },
      { type: 'divider', label: '分割线', icon: 'icon-minus' },
      { type: 'html', label: 'HTML', icon: 'icon-code' }
    ])
    
    // 方法
    const handleDragStart = (component) => {
      // 存储拖拽的组件信息
      window.draggedComponent = component
    }
    
    const handleDrop = (event) => {
      event.preventDefault()
      const component = window.draggedComponent
      if (component) {
        addField(component)
        window.draggedComponent = null
      }
    }
    
    const addField = (component) => {
      const newField = {
        id: Date.now(),
        type: component.type,
        label: component.label,
        name: `field_${Date.now()}`,
        required: false,
        defaultValue: '',
        props: {
          placeholder: `请输入${component.label}`
        }
      }
      formFields.value.push(newField)
    }
    
    const selectField = (index) => {
      selectedFieldIndex.value = index
    }
    
    const editField = (index) => {
      selectedFieldIndex.value = index
    }
    
    const deleteField = (index) => {
      formFields.value.splice(index, 1)
      if (selectedFieldIndex.value === index) {
        selectedFieldIndex.value = -1
      } else if (selectedFieldIndex.value > index) {
        selectedFieldIndex.value--
      }
    }
    
    const getFieldComponent = (type) => {
      const componentMap = {
        input: 'el-input',
        textarea: 'el-input',
        select: 'el-select',
        radio: 'el-radio-group',
        checkbox: 'el-checkbox-group',
        date: 'el-date-picker',
        number: 'el-input-number'
      }
      return componentMap[type] || 'el-input'
    }
    
    const previewForm = () => {
      console.log('预览表单', { formConfig: formConfig.value, fields: formFields.value })
      ElMessage.success('预览功能开发中...')
    }
    
    const saveForm = () => {
      console.log('保存表单', { formConfig: formConfig.value, fields: formFields.value })
      ElMessage.success('表单保存成功')
    }
    
    const publishForm = () => {
      console.log('发布表单', { formConfig: formConfig.value, fields: formFields.value })
      ElMessage.success('表单发布成功')
    }
    
    const undoAction = () => {
      ElMessage.info('撤销功能开发中...')
    }
    
    const redoAction = () => {
      ElMessage.info('重做功能开发中...')
    }
    
    return {
      formConfig,
      formFields,
      selectedFieldIndex,
      selectedField,
      basicComponents,
      advancedComponents,
      handleDragStart,
      handleDrop,
      selectField,
      editField,
      deleteField,
      getFieldComponent,
      previewForm,
      saveForm,
      publishForm,
      undoAction,
      redoAction
    }
  }
}
</script>

<style lang="scss" scoped>
.form-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  
  .header-content {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 4px 0;
    }
    
    .page-description {
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

// 设计器容器
.designer-container {
  flex: 1;
  display: grid;
  grid-template-columns: 250px 1fr 300px;
  gap: var(--spacing-md);
  height: calc(100vh - 200px);
}

// 组件面板
.component-panel {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  
  .panel-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .component-list {
    padding: var(--spacing-sm);
    height: calc(100% - 60px);
    overflow-y: auto;
  }
  
  .component-category {
    margin-bottom: var(--spacing-md);
    
    h4 {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-sm) 0;
      padding: 0 var(--spacing-sm);
    }
  }
  
  .component-items {
    display: grid;
    gap: var(--spacing-xs);
  }
  
  .component-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: grab;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: var(--primary-color);
      background: rgba(46, 125, 154, 0.05);
    }
    
    &:active {
      cursor: grabbing;
    }
    
    i {
      color: var(--primary-color);
      font-size: 16px;
    }
    
    span {
      font-size: 14px;
      color: var(--text-primary);
    }
  }
}

// 设计画布
.design-canvas {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .canvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
    
    .title-input {
      max-width: 300px;
    }
  }
  
  .canvas-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
    min-height: 400px;
    position: relative;
  }
  
  .empty-canvas {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .empty-content {
      text-align: center;
      color: var(--text-light);
      
      i {
        font-size: 48px;
        margin-bottom: var(--spacing-sm);
        display: block;
      }
      
      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
  
  .form-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .form-field {
    position: relative;
    padding: var(--spacing-md);
    border: 2px dashed transparent;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    
    &:hover {
      border-color: var(--border-color);
      background: var(--background-light);
    }
    
    &.active {
      border-color: var(--primary-color);
      background: rgba(46, 125, 154, 0.05);
    }
    
    .field-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: none;
      gap: 4px;
    }
    
    &:hover .field-actions,
    &.active .field-actions {
      display: flex;
    }
  }
}

// 属性面板
.property-panel {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  
  .panel-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .property-content {
    padding: var(--spacing-md);
    height: calc(100% - 60px);
    overflow-y: auto;
  }
  
  .no-selection {
    text-align: center;
    color: var(--text-light);
    padding: var(--spacing-xl);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-container {
    grid-template-columns: 200px 1fr 250px;
  }
}

@media (max-width: 768px) {
  .designer-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    height: auto;
  }
  
  .component-panel,
  .property-panel {
    height: 300px;
  }
}
</style>
