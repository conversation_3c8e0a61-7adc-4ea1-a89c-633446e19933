<template>
  <div class="vuexy-form-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Form Management</h1>
        <p class="page-description">管理所有表单，包括创建、编辑、发布和数据统计</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshForms" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </el-button>
        <el-button @click="importForm" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
          </svg>
          导入表单
        </el-button>
        <el-button type="primary" @click="createForm" class="create-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建表单
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索表单..."
          prefix-icon="Search"
          clearable
          style="width: 300px"
        />
        <el-select v-model="selectedStatus" placeholder="选择状态" clearable style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="已启用" value="active" />
          <el-option label="已停用" value="inactive" />
        </el-select>
        <el-select v-model="selectedCategory" placeholder="选择分类" clearable style="width: 150px">
          <el-option label="全部分类" value="" />
          <el-option label="患者管理" value="patient" />
          <el-option label="医疗记录" value="medical" />
          <el-option label="药品管理" value="medicine" />
          <el-option label="设备管理" value="equipment" />
        </el-select>
      </div>
      <div class="filter-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="table">
            <i class="icon-list"></i>
          </el-radio-button>
          <el-radio-button label="card">
            <i class="icon-grid"></i>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <!-- 表单列表 -->
    <div class="forms-container" :class="viewMode">
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="forms-table">
        <el-table :data="filteredForms" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="表单名称" min-width="200">
            <template #default="{ row }">
              <div class="form-name-cell">
                <i class="icon-file-text"></i>
                <span>{{ row.name }}</span>
                <el-tag v-if="row.isTemplate" size="small" type="info">模板</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="250" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusType(row.status)" 
                size="small"
              >
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submissionCount" label="提交数" width="100" />
          <el-table-column prop="updatedAt" label="更新时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="previewForm(row)">
                预览
              </el-button>
              <el-button size="small" @click="editForm(row)">
                编辑
              </el-button>
              <el-button size="small" @click="viewData(row)">
                数据
              </el-button>
              <el-button size="small" type="danger" @click="handleDeleteForm(row)">
                删除
              </el-button>
              <el-dropdown @command="handleFormAction" trigger="click">
                <el-button size="small">
                  更多<i class="icon-chevron-down"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`copy-${row.id}`">复制</el-dropdown-item>
                    <el-dropdown-item :command="`template-${row.id}`">存为模板</el-dropdown-item>
                    <el-dropdown-item :command="`export-${row.id}`">导出</el-dropdown-item>
                    <el-dropdown-item :command="`toggle-${row.id}`" divided>
                      {{ row.status === 'published' ? '停用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${row.id}`">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 卡片视图 -->
      <div v-else class="forms-grid" v-loading="loading">
        <div
          v-for="form in filteredForms"
          :key="form.id"
          class="form-card"
          @click="previewForm(form)"
        >
          <div class="card-header">
            <div class="form-icon">
              <i class="icon-file-text"></i>
            </div>
            <div class="form-status">
              <el-tag 
                :type="getStatusType(form.status)" 
                size="small"
              >
                {{ getStatusName(form.status) }}
              </el-tag>
            </div>
          </div>
          <div class="card-content">
            <h3 class="form-title">{{ form.name }}</h3>
            <p class="form-description">{{ form.description }}</p>
            <div class="form-meta">
              <span class="form-category">{{ getCategoryName(form.category) }}</span>
              <span class="form-submissions">{{ form.submissionCount }}次提交</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click.stop="editForm(form)">
              编辑
            </el-button>
            <el-button size="small" @click.stop="viewData(form)">
              数据
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleDeleteForm(form)">
              删除
            </el-button>
            <el-dropdown @command="handleFormAction" trigger="click">
              <el-button size="small" @click.stop>
                <i class="icon-more-horizontal"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`copy-${form.id}`">复制</el-dropdown-item>
                  <el-dropdown-item :command="`template-${form.id}`">存为模板</el-dropdown-item>
                  <el-dropdown-item :command="`export-${form.id}`">导出</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${form.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalForms"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFormList, deleteForm, getFormById, saveForm, updateForm } from '../../util/api'

export default {
  name: 'FormManagement',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const searchQuery = ref('')
    const selectedStatus = ref('')
    const selectedCategory = ref('')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalForms = ref(0)
    const loading = ref(false)

    // 表单数据
    const forms = ref([])
    
    // 计算属性
    const filteredForms = computed(() => {
      let result = forms.value
      
      // 按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(form => 
          form.name.toLowerCase().includes(query) ||
          form.description.toLowerCase().includes(query)
        )
      }
      
      // 按状态过滤
      if (selectedStatus.value) {
        result = result.filter(form => form.status === selectedStatus.value)
      }
      
      // 按分类过滤
      if (selectedCategory.value) {
        result = result.filter(form => form.category === selectedCategory.value)
      }
      
      totalForms.value = result.length
      
      // 分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return result.slice(start, end)
    })
    
    // 方法
    const getCategoryName = (category) => {
      const categoryMap = {
        patient: '患者管理',
        medical: '医疗记录',
        medicine: '药品管理',
        equipment: '设备管理'
      }
      return categoryMap[category] || '其他'
    }
    
    const getStatusName = (status) => {
      const statusMap = {
        active: '已启用',
        inactive: '已停用',
        // 兼容旧的状态名称
        draft: '草稿',
        published: '已发布',
        disabled: '已停用'
      }
      return statusMap[status] || '未知'
    }

    const getStatusType = (status) => {
      const typeMap = {
        active: 'success',
        inactive: 'danger',
        // 兼容旧的状态名称
        draft: 'info',
        published: 'success',
        disabled: 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN')
    }
    
    // 加载表单列表
    const loadForms = async () => {
      try {
        loading.value = true
        const response = await getFormList()
        if (response.success) {
          // API返回的数据结构是 { data: { list: [...], pagination: {...} } }
          const formList = response.data.list || response.data || []
          forms.value = formList.map(form => ({
            ...form,
            updatedAt: new Date(form.updated_at),
            createdAt: new Date(form.created_at),
            submissionCount: form.submission_count || 0,
            createdBy: form.created_by_name || '未知',
            category: form.category || 'other',
            status: form.status || 'active'
          }))
          console.log('加载的表单数据:', forms.value)
        } else {
          ElMessage.error(response.message || '加载表单列表失败')
        }
      } catch (error) {
        console.error('加载表单列表失败:', error)
        ElMessage.error('加载表单列表失败')
      } finally {
        loading.value = false
      }
    }

    const refreshForms = () => {
      loadForms()
      ElMessage.success('表单列表已刷新')
    }
    
    const createForm = () => {
      router.push('/form/designer')
    }

    const fileInput = ref(null)

    const importForm = () => {
      fileInput.value?.click()
    }

    const handleFileImport = async (event) => {
      const file = event.target.files[0]
      if (!file) return

      try {
        const text = await file.text()
        const importData = JSON.parse(text)

        // 验证导入数据格式
        if (!importData.name || !importData.form_config) {
          ElMessage.error('导入文件格式不正确')
          return
        }

        // 创建导入的表单数据
        const formData = {
          name: `${importData.name} - 导入`,
          description: importData.description ? `${importData.description} (导入)` : '导入的表单',
          form_config: importData.form_config,
          category: importData.category || 'other',
          created_by: importData.created_by || '系统导入'
        }

        // 保存导入的表单
        const response = await saveForm(formData)
        if (response.success) {
          ElMessage.success('表单导入成功')
          loadForms() // 刷新列表
        } else {
          ElMessage.error('导入表单失败')
        }
      } catch (error) {
        console.error('导入表单失败:', error)
        ElMessage.error('导入文件解析失败，请检查文件格式')
      } finally {
        // 清空文件输入，允许重复导入同一文件
        event.target.value = ''
      }
    }
    
    const previewForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          // 打开新窗口显示表单预览
          const previewUrl = `${window.location.origin}${window.location.pathname}#/form/preview?id=${form.id}`
          window.open(previewUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('预览表单失败:', error)
        ElMessage.error('预览表单失败')
      }
    }
    
    const editForm = (form) => {
      router.push(`/form/designer?edit=${form.id}`)
    }
    
    const viewData = (form) => {
      // 跳转到表单数据页面
      router.push(`/form/data?id=${form.id}&name=${encodeURIComponent(form.name)}`)
    }

    const copyForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const originalForm = response.data

          // 创建复制的表单数据
          const copyData = {
            name: `${originalForm.name} - 副本`,
            description: originalForm.description ? `${originalForm.description} (复制)` : '复制的表单',
            form_config: originalForm.form_config,
            category: originalForm.category,
            created_by: originalForm.created_by
          }

          // 保存复制的表单
          const saveResponse = await saveForm(copyData)
          if (saveResponse.success) {
            ElMessage.success('表单复制成功')
            loadForms() // 刷新列表
          } else {
            ElMessage.error('复制表单失败')
          }
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('复制表单失败:', error)
        ElMessage.error('复制表单失败')
      }
    }

    const exportForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const formData = response.data

          // 创建导出数据
          const exportData = {
            name: formData.name,
            description: formData.description,
            form_config: formData.form_config,
            category: formData.category,
            created_by: formData.created_by,
            export_time: new Date().toISOString(),
            version: '1.0'
          }

          // 创建下载链接
          const dataStr = JSON.stringify(exportData, null, 2)
          const dataBlob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(dataBlob)

          // 创建下载链接并触发下载
          const link = document.createElement('a')
          link.href = url
          link.download = `${formData.name}_${new Date().getTime()}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)

          ElMessage.success('表单导出成功')
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('导出表单失败:', error)
        ElMessage.error('导出表单失败')
      }
    }

    const toggleFormStatus = async (form) => {
      try {
        // 注意：数据库中的状态是 'active' 和 'inactive'，而不是 'published' 和 'disabled'
        const newStatus = form.status === 'active' ? 'inactive' : 'active'

        const updateData = {
          name: form.name,
          description: form.description,
          form_config: form.form_config,
          category: form.category,
          created_by: form.created_by,
          status: newStatus
        }

        const response = await updateForm(form.id, updateData)
        if (response.success) {
          form.status = newStatus
          ElMessage.success(`表单已${newStatus === 'active' ? '启用' : '停用'}`)
        } else {
          ElMessage.error('状态更新失败')
        }
      } catch (error) {
        console.error('切换表单状态失败:', error)
        ElMessage.error('切换表单状态失败')
      }
    }

    const saveAsTemplate = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const formData = response.data

          // 创建模板数据（这里简化处理，实际可能需要专门的模板API）
          const templateData = {
            name: `${formData.name} - 模板`,
            description: `基于 ${formData.name} 创建的模板`,
            form_config: formData.form_config,
            category: formData.category,
            created_by: formData.created_by,
            is_template: true // 标记为模板
          }

          // 保存为新表单（标记为模板）
          const saveResponse = await saveForm(templateData)
          if (saveResponse.success) {
            ElMessage.success('已成功存为模板')
          } else {
            ElMessage.error('存为模板失败')
          }
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('存为模板失败:', error)
        ElMessage.error('存为模板失败')
      }
    }

    // 删除表单
    const handleDeleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await deleteForm(form.id)
        if (response.success) {
          ElMessage.success('表单删除成功')
          loadForms() // 重新加载列表
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除表单失败:', error)
          ElMessage.error('删除表单失败')
        }
      }
    }
    
    const handleFormAction = (command) => {
      const [action, id] = command.split('-')
      const form = forms.value.find(f => f.id === parseInt(id))
      
      switch (action) {
        case 'copy':
          copyForm(form)
          break
        case 'template':
          saveAsTemplate(form)
          break
        case 'export':
          exportForm(form)
          break
        case 'toggle':
          toggleFormStatus(form)
          break
        case 'delete':
          handleDeleteForm(form)
          break
      }
    }
    
    const deleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const index = forms.value.findIndex(f => f.id === form.id)
        if (index > -1) {
          forms.value.splice(index, 1)
          ElMessage.success('表单删除成功')
        }
      } catch {
        // 用户取消删除
      }
    }
    
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
    }
    
    onMounted(() => {
      loadForms()
    })
    
    return {
      searchQuery,
      selectedStatus,
      selectedCategory,
      viewMode,
      currentPage,
      pageSize,
      totalForms,
      loading,
      forms,
      filteredForms,
      fileInput,
      getCategoryName,
      getStatusName,
      getStatusType,
      formatDate,
      loadForms,
      refreshForms,
      createForm,
      importForm,
      handleFileImport,
      previewForm,
      editForm,
      viewData,
      copyForm,
      exportForm,
      toggleFormStatus,
      saveAsTemplate,
      handleDeleteForm,
      handleFormAction,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style lang="scss" scoped>
.vuexy-form-management {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .header-content {
    .page-title {
      font-size: 2rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 0.25rem 0;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-description {
      color: var(--text-light);
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;

    .action-btn {
      border: 1px solid rgba(115, 103, 240, 0.2);
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;
      background: var(--card-background);
      color: var(--text-primary);

      &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(115, 103, 240, 0.15);
      }
    }

    .create-btn {
      background: var(--primary-gradient);
      border: none;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(115, 103, 240, 0.3);
      }
    }
  }
}

// 筛选区域
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  
  .filter-left {
    display: flex;
    gap: var(--spacing-sm);
  }
}

// 表格视图
.forms-table {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  
  .form-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      color: var(--primary-color);
    }
  }
}

// 卡片视图
.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-md);
  
  .form-card {
    background: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--hover-shadow);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md);
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      
      .form-icon {
        font-size: 24px;
        color: var(--primary-color);
      }
    }
    
    .card-content {
      padding: var(--spacing-md);
      
      .form-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
      }
      
      .form-description {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0 0 12px 0;
        line-height: 1.4;
      }
      
      .form-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        
        .form-category {
          color: var(--primary-color);
          background: rgba(46, 125, 154, 0.1);
          padding: 2px 8px;
          border-radius: 12px;
        }
        
        .form-submissions {
          color: var(--text-light);
        }
      }
    }
    
    .card-actions {
      padding: var(--spacing-sm) var(--spacing-md);
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

// 分页
.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  margin-top: var(--spacing-md);
}

// 响应式设计
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .filter-left {
      width: 100%;
      flex-direction: column;
    }
  }
  
  .forms-grid {
    grid-template-columns: 1fr;
  }
}
</style>
