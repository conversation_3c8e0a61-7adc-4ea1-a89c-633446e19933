<template>
  <div class="form-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">表单管理</h1>
        <p class="page-description">管理所有表单，包括创建、编辑、发布和数据统计</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshForms">
          <i class="icon-refresh-cw"></i>
          刷新
        </el-button>
        <el-button type="primary" @click="createForm">
          <i class="icon-plus"></i>
          创建表单
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索表单..."
          prefix-icon="Search"
          clearable
          style="width: 300px"
        />
        <el-select v-model="selectedStatus" placeholder="选择状态" clearable style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="已发布" value="published" />
          <el-option label="已停用" value="disabled" />
        </el-select>
        <el-select v-model="selectedCategory" placeholder="选择分类" clearable style="width: 150px">
          <el-option label="全部分类" value="" />
          <el-option label="患者管理" value="patient" />
          <el-option label="医疗记录" value="medical" />
          <el-option label="药品管理" value="medicine" />
          <el-option label="设备管理" value="equipment" />
        </el-select>
      </div>
      <div class="filter-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="table">
            <i class="icon-list"></i>
          </el-radio-button>
          <el-radio-button label="card">
            <i class="icon-grid"></i>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <!-- 表单列表 -->
    <div class="forms-container" :class="viewMode">
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="forms-table">
        <el-table :data="filteredForms" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="表单名称" min-width="200">
            <template #default="{ row }">
              <div class="form-name-cell">
                <i class="icon-file-text"></i>
                <span>{{ row.name }}</span>
                <el-tag v-if="row.isTemplate" size="small" type="info">模板</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="250" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusType(row.status)" 
                size="small"
              >
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submissionCount" label="提交数" width="100" />
          <el-table-column prop="updatedAt" label="更新时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="previewForm(row)">
                预览
              </el-button>
              <el-button size="small" @click="editForm(row)">
                编辑
              </el-button>
              <el-button size="small" @click="viewData(row)">
                数据
              </el-button>
              <el-button size="small" type="danger" @click="handleDeleteForm(row)">
                删除
              </el-button>
              <el-dropdown @command="handleFormAction" trigger="click">
                <el-button size="small">
                  更多<i class="icon-chevron-down"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`copy-${row.id}`">复制</el-dropdown-item>
                    <el-dropdown-item :command="`template-${row.id}`">存为模板</el-dropdown-item>
                    <el-dropdown-item :command="`export-${row.id}`">导出</el-dropdown-item>
                    <el-dropdown-item :command="`toggle-${row.id}`" divided>
                      {{ row.status === 'published' ? '停用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${row.id}`">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 卡片视图 -->
      <div v-else class="forms-grid" v-loading="loading">
        <div
          v-for="form in filteredForms"
          :key="form.id"
          class="form-card"
          @click="previewForm(form)"
        >
          <div class="card-header">
            <div class="form-icon">
              <i class="icon-file-text"></i>
            </div>
            <div class="form-status">
              <el-tag 
                :type="getStatusType(form.status)" 
                size="small"
              >
                {{ getStatusName(form.status) }}
              </el-tag>
            </div>
          </div>
          <div class="card-content">
            <h3 class="form-title">{{ form.name }}</h3>
            <p class="form-description">{{ form.description }}</p>
            <div class="form-meta">
              <span class="form-category">{{ getCategoryName(form.category) }}</span>
              <span class="form-submissions">{{ form.submissionCount }}次提交</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click.stop="editForm(form)">
              编辑
            </el-button>
            <el-button size="small" @click.stop="viewData(form)">
              数据
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleDeleteForm(form)">
              删除
            </el-button>
            <el-dropdown @command="handleFormAction" trigger="click">
              <el-button size="small" @click.stop>
                <i class="icon-more-horizontal"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`copy-${form.id}`">复制</el-dropdown-item>
                  <el-dropdown-item :command="`template-${form.id}`">存为模板</el-dropdown-item>
                  <el-dropdown-item :command="`export-${form.id}`">导出</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${form.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalForms"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFormList, deleteForm } from '../../util/api'

export default {
  name: 'FormManagement',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const searchQuery = ref('')
    const selectedStatus = ref('')
    const selectedCategory = ref('')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalForms = ref(0)
    const loading = ref(false)

    // 表单数据
    const forms = ref([])
    
    // 计算属性
    const filteredForms = computed(() => {
      let result = forms.value
      
      // 按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(form => 
          form.name.toLowerCase().includes(query) ||
          form.description.toLowerCase().includes(query)
        )
      }
      
      // 按状态过滤
      if (selectedStatus.value) {
        result = result.filter(form => form.status === selectedStatus.value)
      }
      
      // 按分类过滤
      if (selectedCategory.value) {
        result = result.filter(form => form.category === selectedCategory.value)
      }
      
      totalForms.value = result.length
      
      // 分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return result.slice(start, end)
    })
    
    // 方法
    const getCategoryName = (category) => {
      const categoryMap = {
        patient: '患者管理',
        medical: '医疗记录',
        medicine: '药品管理',
        equipment: '设备管理'
      }
      return categoryMap[category] || '其他'
    }
    
    const getStatusName = (status) => {
      const statusMap = {
        draft: '草稿',
        published: '已发布',
        disabled: '已停用'
      }
      return statusMap[status] || '未知'
    }
    
    const getStatusType = (status) => {
      const typeMap = {
        draft: 'info',
        published: 'success',
        disabled: 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN')
    }
    
    // 加载表单列表
    const loadForms = async () => {
      try {
        loading.value = true
        const response = await getFormList()
        if (response.success) {
          forms.value = response.data.map(form => ({
            ...form,
            updatedAt: new Date(form.updated_at),
            createdAt: new Date(form.created_at),
            submissionCount: form.submission_count || 0,
            createdBy: form.created_by_name || '未知',
            category: form.category || 'other',
            status: form.status || 'draft'
          }))
        } else {
          ElMessage.error(response.message || '加载表单列表失败')
        }
      } catch (error) {
        console.error('加载表单列表失败:', error)
        ElMessage.error('加载表单列表失败')
      } finally {
        loading.value = false
      }
    }

    const refreshForms = () => {
      loadForms()
      ElMessage.success('表单列表已刷新')
    }
    
    const createForm = () => {
      router.push('/form/designer')
    }
    
    const previewForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          // 打开新窗口显示表单预览
          const previewUrl = `${window.location.origin}${window.location.pathname}#/form/preview?id=${form.id}`
          window.open(previewUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('预览表单失败:', error)
        ElMessage.error('预览表单失败')
      }
    }
    
    const editForm = (form) => {
      router.push(`/form/designer?edit=${form.id}`)
    }
    
    const viewData = (form) => {
      // 跳转到表单数据页面
      router.push(`/form/data?id=${form.id}&name=${encodeURIComponent(form.name)}`)
    }

    // 删除表单
    const handleDeleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await deleteForm(form.id)
        if (response.success) {
          ElMessage.success('表单删除成功')
          loadForms() // 重新加载列表
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除表单失败:', error)
          ElMessage.error('删除表单失败')
        }
      }
    }
    
    const handleFormAction = (command) => {
      const [action, id] = command.split('-')
      const form = forms.value.find(f => f.id === parseInt(id))
      
      switch (action) {
        case 'copy':
          console.log('复制表单:', form)
          ElMessage.success('表单复制成功')
          break
        case 'template':
          console.log('存为模板:', form)
          ElMessage.success('已存为模板')
          break
        case 'export':
          console.log('导出表单:', form)
          ElMessage.success('表单导出成功')
          break
        case 'toggle':
          form.status = form.status === 'published' ? 'disabled' : 'published'
          ElMessage.success(`表单已${form.status === 'published' ? '启用' : '停用'}`)
          break
        case 'delete':
          handleDeleteForm(form)
          break
      }
    }
    
    const deleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const index = forms.value.findIndex(f => f.id === form.id)
        if (index > -1) {
          forms.value.splice(index, 1)
          ElMessage.success('表单删除成功')
        }
      } catch {
        // 用户取消删除
      }
    }
    
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
    }
    
    onMounted(() => {
      loadForms()
    })
    
    return {
      searchQuery,
      selectedStatus,
      selectedCategory,
      viewMode,
      currentPage,
      pageSize,
      totalForms,
      loading,
      forms,
      filteredForms,
      getCategoryName,
      getStatusName,
      getStatusType,
      formatDate,
      loadForms,
      refreshForms,
      createForm,
      previewForm,
      editForm,
      viewData,
      handleDeleteForm,
      handleFormAction,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style lang="scss" scoped>
.form-management {
  max-width: 1200px;
  margin: 0 auto;
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  
  .header-content {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 4px 0;
    }
    
    .page-description {
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

// 筛选区域
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  
  .filter-left {
    display: flex;
    gap: var(--spacing-sm);
  }
}

// 表格视图
.forms-table {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  
  .form-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      color: var(--primary-color);
    }
  }
}

// 卡片视图
.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-md);
  
  .form-card {
    background: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--hover-shadow);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md);
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      
      .form-icon {
        font-size: 24px;
        color: var(--primary-color);
      }
    }
    
    .card-content {
      padding: var(--spacing-md);
      
      .form-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
      }
      
      .form-description {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0 0 12px 0;
        line-height: 1.4;
      }
      
      .form-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        
        .form-category {
          color: var(--primary-color);
          background: rgba(46, 125, 154, 0.1);
          padding: 2px 8px;
          border-radius: 12px;
        }
        
        .form-submissions {
          color: var(--text-light);
        }
      }
    }
    
    .card-actions {
      padding: var(--spacing-sm) var(--spacing-md);
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

// 分页
.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md);
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  margin-top: var(--spacing-md);
}

// 响应式设计
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .filter-left {
      width: 100%;
      flex-direction: column;
    }
  }
  
  .forms-grid {
    grid-template-columns: 1fr;
  }
}
</style>
