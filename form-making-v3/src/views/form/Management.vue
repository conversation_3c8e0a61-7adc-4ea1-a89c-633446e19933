<template>
  <div class="vuexy-form-management">
    <!-- 紧凑页面标题 -->
    <div class="compact-header">
      <div class="header-left">
        <h1 class="page-title">Form Management</h1>
      </div>
      <div class="header-right">
        <button @click="refreshForms" class="compact-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
        <button @click="importForm" class="compact-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
          </svg>
          导入
        </button>
        <button @click="createForm" class="compact-btn primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建表单
        </button>
      </div>
    </div>
    
    <!-- Vuexy 风格筛选区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-search-box">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search Product"
            class="search-input"
          />
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedStatus" class="vuexy-select">
            <option value="">7</option>
            <option value="active">10</option>
            <option value="inactive">25</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedCategory" class="vuexy-select">
            <option value="">Export</option>
            <option value="patient">PDF</option>
            <option value="medical">Excel</option>
            <option value="medicine">CSV</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <div class="filter-right">
        <button class="vuexy-add-btn" @click="createForm">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2"/>
          </svg>
          Add Product
        </button>
      </div>
    </div>
    
    <!-- Vuexy 风格表格 -->
    <div class="vuexy-table-container">
      <div class="vuexy-table">
        <div class="table-header">
          <div class="header-cell checkbox-cell">
            <input type="checkbox" class="vuexy-checkbox" />
          </div>
          <div class="header-cell product-cell">PRODUCT</div>
          <div class="header-cell category-cell">CATEGORY</div>
          <div class="header-cell stock-cell">STOCK</div>
          <div class="header-cell sku-cell">SKU</div>
          <div class="header-cell price-cell">PRICE</div>
          <div class="header-cell qty-cell">QTY</div>
          <div class="header-cell actions-cell"></div>
        </div>

        <div class="table-body">
          <div v-for="form in filteredForms" :key="form.id" class="table-row">
            <div class="table-cell checkbox-cell">
              <input type="checkbox" class="vuexy-checkbox" />
            </div>
            <div class="table-cell product-cell">
              <div class="product-info">
                <div class="product-avatar">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <div class="product-details">
                  <div class="product-name">{{ form.name }}</div>
                  <div class="product-description">{{ form.description }}</div>
                </div>
              </div>
            </div>
            <div class="table-cell category-cell">
              <div class="category-badge" :class="getCategoryClass(form.category)">
                <div class="category-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                {{ getCategoryName(form.category) }}
              </div>
            </div>
            <div class="table-cell stock-cell">
              <div class="stock-indicator" :class="form.status === 'active' ? 'in-stock' : 'out-stock'"></div>
            </div>
            <div class="table-cell sku-cell">{{ form.id }}{{ Math.floor(Math.random() * 1000) }}</div>
            <div class="table-cell price-cell">${{ (Math.random() * 500 + 50).toFixed(2) }}</div>
            <div class="table-cell qty-cell">{{ Math.floor(Math.random() * 1000) + 100 }}</div>
            <div class="table-cell actions-cell">
              <div class="action-buttons">
                <button class="action-btn edit-btn" @click="editForm(form)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <button class="action-btn delete-btn" @click="handleDeleteForm(form)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <button class="action-btn more-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
                    <circle cx="19" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
                    <circle cx="5" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vuexy 分页器 -->
      <div class="vuexy-pagination">
        <div class="pagination-info">
          Showing 1 to 7 of 100 entries
        </div>
        <div class="pagination-controls">
          <button class="pagination-btn prev-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <polyline points="15,18 9,12 15,6" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button class="pagination-btn page-btn active">1</button>
          <button class="pagination-btn page-btn">2</button>
          <button class="pagination-btn page-btn">3</button>
          <button class="pagination-btn page-btn">4</button>
          <button class="pagination-btn page-btn">5</button>
          <span class="pagination-dots">...</span>
          <button class="pagination-btn page-btn">15</button>
          <button class="pagination-btn next-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button class="buy-now-btn">Buy Now</button>
        </div>
      </div>
    </div>

    <!-- 表单列表容器 -->
    <div class="forms-container" :class="viewMode">
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="forms-grid" v-loading="loading">
        <div
          v-for="form in filteredForms"
          :key="form.id"
          class="form-card"
          @click="previewForm(form)"
        >
          <div class="card-header">
            <div class="form-icon">
              <i class="icon-file-text"></i>
            </div>
            <div class="form-status">
              <el-tag 
                :type="getStatusType(form.status)" 
                size="small"
              >
                {{ getStatusName(form.status) }}
              </el-tag>
            </div>
          </div>
          <div class="card-content">
            <h3 class="form-title">{{ form.name }}</h3>
            <p class="form-description">{{ form.description }}</p>
            <div class="form-meta">
              <span class="form-category">{{ getCategoryName(form.category) }}</span>
              <span class="form-submissions">{{ form.submissionCount }}次提交</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click.stop="editForm(form)">
              编辑
            </el-button>
            <el-button size="small" @click.stop="viewData(form)">
              数据
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleDeleteForm(form)">
              删除
            </el-button>
            <el-dropdown @command="handleFormAction" trigger="click">
              <el-button size="small" @click.stop>
                <i class="icon-more-horizontal"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`copy-${form.id}`">复制</el-dropdown-item>
                  <el-dropdown-item :command="`template-${form.id}`">存为模板</el-dropdown-item>
                  <el-dropdown-item :command="`export-${form.id}`">导出</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${form.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalForms"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFormList, deleteForm, getFormById, saveForm, updateForm } from '../../util/api'

export default {
  name: 'FormManagement',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const searchQuery = ref('')
    const selectedStatus = ref('')
    const selectedCategory = ref('')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalForms = ref(0)
    const loading = ref(false)

    // 表单数据
    const forms = ref([])
    
    // 计算属性
    const filteredForms = computed(() => {
      let result = forms.value
      
      // 按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(form => 
          form.name.toLowerCase().includes(query) ||
          form.description.toLowerCase().includes(query)
        )
      }
      
      // 按状态过滤
      if (selectedStatus.value) {
        result = result.filter(form => form.status === selectedStatus.value)
      }
      
      // 按分类过滤
      if (selectedCategory.value) {
        result = result.filter(form => form.category === selectedCategory.value)
      }
      
      totalForms.value = result.length
      
      // 分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return result.slice(start, end)
    })
    
    // 方法
    const getCategoryName = (category) => {
      const categoryMap = {
        patient: '患者管理',
        medical: '医疗记录',
        medicine: '药品管理',
        equipment: '设备管理'
      }
      return categoryMap[category] || '其他'
    }
    
    const getStatusName = (status) => {
      const statusMap = {
        active: '已启用',
        inactive: '已停用',
        // 兼容旧的状态名称
        draft: '草稿',
        published: '已发布',
        disabled: '已停用'
      }
      return statusMap[status] || '未知'
    }

    const getStatusType = (status) => {
      const typeMap = {
        active: 'success',
        inactive: 'danger',
        // 兼容旧的状态名称
        draft: 'info',
        published: 'success',
        disabled: 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN')
    }
    
    // 加载表单列表
    const loadForms = async () => {
      try {
        loading.value = true
        const response = await getFormList()
        if (response.success) {
          // API返回的数据结构是 { data: { list: [...], pagination: {...} } }
          const formList = response.data.list || response.data || []
          forms.value = formList.map(form => ({
            ...form,
            updatedAt: new Date(form.updated_at),
            createdAt: new Date(form.created_at),
            submissionCount: form.submission_count || 0,
            createdBy: form.created_by_name || '未知',
            category: form.category || 'other',
            status: form.status || 'active'
          }))
          console.log('加载的表单数据:', forms.value)
        } else {
          ElMessage.error(response.message || '加载表单列表失败')
        }
      } catch (error) {
        console.error('加载表单列表失败:', error)
        ElMessage.error('加载表单列表失败')
      } finally {
        loading.value = false
      }
    }

    const refreshForms = () => {
      loadForms()
      ElMessage.success('表单列表已刷新')
    }
    
    const createForm = () => {
      router.push('/form/designer')
    }

    const fileInput = ref(null)

    const importForm = () => {
      fileInput.value?.click()
    }

    const handleFileImport = async (event) => {
      const file = event.target.files[0]
      if (!file) return

      try {
        const text = await file.text()
        const importData = JSON.parse(text)

        // 验证导入数据格式
        if (!importData.name || !importData.form_config) {
          ElMessage.error('导入文件格式不正确')
          return
        }

        // 创建导入的表单数据
        const formData = {
          name: `${importData.name} - 导入`,
          description: importData.description ? `${importData.description} (导入)` : '导入的表单',
          form_config: importData.form_config,
          category: importData.category || 'other',
          created_by: importData.created_by || '系统导入'
        }

        // 保存导入的表单
        const response = await saveForm(formData)
        if (response.success) {
          ElMessage.success('表单导入成功')
          loadForms() // 刷新列表
        } else {
          ElMessage.error('导入表单失败')
        }
      } catch (error) {
        console.error('导入表单失败:', error)
        ElMessage.error('导入文件解析失败，请检查文件格式')
      } finally {
        // 清空文件输入，允许重复导入同一文件
        event.target.value = ''
      }
    }
    
    const previewForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          // 打开新窗口显示表单预览
          const previewUrl = `${window.location.origin}${window.location.pathname}#/form/preview?id=${form.id}`
          window.open(previewUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('预览表单失败:', error)
        ElMessage.error('预览表单失败')
      }
    }
    
    const editForm = (form) => {
      router.push(`/form/designer?edit=${form.id}`)
    }
    
    const viewData = (form) => {
      // 跳转到表单数据页面
      router.push(`/form/data?id=${form.id}&name=${encodeURIComponent(form.name)}`)
    }

    const copyForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const originalForm = response.data

          // 创建复制的表单数据
          const copyData = {
            name: `${originalForm.name} - 副本`,
            description: originalForm.description ? `${originalForm.description} (复制)` : '复制的表单',
            form_config: originalForm.form_config,
            category: originalForm.category,
            created_by: originalForm.created_by
          }

          // 保存复制的表单
          const saveResponse = await saveForm(copyData)
          if (saveResponse.success) {
            ElMessage.success('表单复制成功')
            loadForms() // 刷新列表
          } else {
            ElMessage.error('复制表单失败')
          }
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('复制表单失败:', error)
        ElMessage.error('复制表单失败')
      }
    }

    const exportForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const formData = response.data

          // 创建导出数据
          const exportData = {
            name: formData.name,
            description: formData.description,
            form_config: formData.form_config,
            category: formData.category,
            created_by: formData.created_by,
            export_time: new Date().toISOString(),
            version: '1.0'
          }

          // 创建下载链接
          const dataStr = JSON.stringify(exportData, null, 2)
          const dataBlob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(dataBlob)

          // 创建下载链接并触发下载
          const link = document.createElement('a')
          link.href = url
          link.download = `${formData.name}_${new Date().getTime()}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)

          ElMessage.success('表单导出成功')
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('导出表单失败:', error)
        ElMessage.error('导出表单失败')
      }
    }

    const toggleFormStatus = async (form) => {
      try {
        // 注意：数据库中的状态是 'active' 和 'inactive'，而不是 'published' 和 'disabled'
        const newStatus = form.status === 'active' ? 'inactive' : 'active'

        const updateData = {
          name: form.name,
          description: form.description,
          form_config: form.form_config,
          category: form.category,
          created_by: form.created_by,
          status: newStatus
        }

        const response = await updateForm(form.id, updateData)
        if (response.success) {
          form.status = newStatus
          ElMessage.success(`表单已${newStatus === 'active' ? '启用' : '停用'}`)
        } else {
          ElMessage.error('状态更新失败')
        }
      } catch (error) {
        console.error('切换表单状态失败:', error)
        ElMessage.error('切换表单状态失败')
      }
    }

    const saveAsTemplate = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          const formData = response.data

          // 创建模板数据（这里简化处理，实际可能需要专门的模板API）
          const templateData = {
            name: `${formData.name} - 模板`,
            description: `基于 ${formData.name} 创建的模板`,
            form_config: formData.form_config,
            category: formData.category,
            created_by: formData.created_by,
            is_template: true // 标记为模板
          }

          // 保存为新表单（标记为模板）
          const saveResponse = await saveForm(templateData)
          if (saveResponse.success) {
            ElMessage.success('已成功存为模板')
          } else {
            ElMessage.error('存为模板失败')
          }
        } else {
          ElMessage.error('获取表单配置失败')
        }
      } catch (error) {
        console.error('存为模板失败:', error)
        ElMessage.error('存为模板失败')
      }
    }

    // 删除表单
    const handleDeleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await deleteForm(form.id)
        if (response.success) {
          ElMessage.success('表单删除成功')
          loadForms() // 重新加载列表
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除表单失败:', error)
          ElMessage.error('删除表单失败')
        }
      }
    }
    
    const handleFormAction = (command) => {
      const [action, id] = command.split('-')
      const form = forms.value.find(f => f.id === parseInt(id))
      
      switch (action) {
        case 'copy':
          copyForm(form)
          break
        case 'template':
          saveAsTemplate(form)
          break
        case 'export':
          exportForm(form)
          break
        case 'toggle':
          toggleFormStatus(form)
          break
        case 'delete':
          handleDeleteForm(form)
          break
      }
    }
    
    const deleteForm = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const index = forms.value.findIndex(f => f.id === form.id)
        if (index > -1) {
          forms.value.splice(index, 1)
          ElMessage.success('表单删除成功')
        }
      } catch {
        // 用户取消删除
      }
    }
    
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
    }

    // 获取分类样式类名
    const getCategoryClass = (category) => {
      const categoryMap = {
        'patient': 'shoes',
        'medical': 'electronics',
        'medicine': 'electronics',
        'equipment': 'accessories',
        'hr': 'household'
      }
      return categoryMap[category] || 'accessories'
    }

    onMounted(() => {
      loadForms()
    })
    
    return {
      searchQuery,
      selectedStatus,
      selectedCategory,
      viewMode,
      currentPage,
      pageSize,
      totalForms,
      loading,
      forms,
      filteredForms,
      fileInput,
      getCategoryName,
      getCategoryClass,
      getStatusName,
      getStatusType,
      formatDate,
      loadForms,
      refreshForms,
      createForm,
      importForm,
      handleFileImport,
      previewForm,
      editForm,
      viewData,
      copyForm,
      exportForm,
      toggleFormStatus,
      saveAsTemplate,
      handleDeleteForm,
      handleFormAction,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style lang="scss" scoped>
.vuexy-form-management {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

// 紧凑页面头部
.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem 0;

  .header-left {
    .page-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
      color: #d0d2d6;
    }
  }

  .header-right {
    display: flex;
    gap: 0.75rem;

    .compact-btn {
      background: #3b4253;
      border: 1px solid #4a5568;
      border-radius: 0.375rem;
      padding: 0.5rem 1rem;
      color: #d0d2d6;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #4a5568;
        border-color: #6e6b7b;
      }

      &.primary {
        background: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);
        border-color: #7367f0;
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(115, 103, 240, 0.3);
        }
      }
    }
  }
}

// Vuexy 筛选区域样式
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 1.25rem;
  background: #283046;
  border-radius: 0.5rem;
  border: 1px solid #3b4253;

  .filter-left {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .vuexy-search-box {
    position: relative;
    display: flex;
    align-items: center;

    .search-icon {
      position: absolute;
      left: 1rem;
      color: #6e6b7b;
      z-index: 1;
    }

    .search-input {
      background: #3b4253;
      border: 1px solid #4a5568;
      border-radius: 0.375rem;
      padding: 0.75rem 1rem 0.75rem 3rem;
      color: #d0d2d6;
      font-size: 0.875rem;
      width: 300px;
      transition: all 0.2s ease;

      &::placeholder {
        color: #6e6b7b;
      }

      &:focus {
        outline: none;
        border-color: #7367f0;
        box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
      }
    }
  }

  .vuexy-select-wrapper {
    position: relative;

    .vuexy-select {
      background: #3b4253;
      border: 1px solid #4a5568;
      border-radius: 0.375rem;
      padding: 0.75rem 2.5rem 0.75rem 1rem;
      color: #d0d2d6;
      font-size: 0.875rem;
      min-width: 120px;
      appearance: none;
      cursor: pointer;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #7367f0;
        box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
      }
    }

    .select-arrow {
      position: absolute;
      right: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6e6b7b;
      pointer-events: none;
    }
  }

  .filter-right {
    .vuexy-add-btn {
      background: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);
      border: none;
      border-radius: 0.375rem;
      padding: 0.75rem 1.5rem;
      color: white;
      font-size: 0.875rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(115, 103, 240, 0.3);
      }
    }
  }
}

// Vuexy 表格样式
.vuexy-table-container {
  background: #283046;
  border-radius: 0.5rem;
  border: 1px solid #3b4253;
  overflow: hidden;

  .vuexy-table {
    width: 100%;

    .table-header {
      display: grid;
      grid-template-columns: 60px 1fr 150px 80px 120px 100px 80px 120px;
      background: #3b4253;
      border-bottom: 1px solid #4a5568;

      .header-cell {
        padding: 1rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #a2a6b0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
      }
    }

    .table-body {
      .table-row {
        display: grid;
        grid-template-columns: 60px 1fr 150px 80px 120px 100px 80px 120px;
        border-bottom: 1px solid #3b4253;
        transition: background-color 0.2s ease;

        &:hover {
          background: #3b4253;
        }

        .table-cell {
          padding: 1rem 0.75rem;
          display: flex;
          align-items: center;
          color: #d0d2d6;
          font-size: 0.875rem;
        }

        .checkbox-cell {
          justify-content: center;

          .vuexy-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #4a5568;
            border-radius: 0.25rem;
            background: transparent;
            cursor: pointer;

            &:checked {
              background: #7367f0;
              border-color: #7367f0;
            }
          }
        }

        .product-cell {
          .product-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .product-avatar {
              width: 40px;
              height: 40px;
              background: #7367f0;
              border-radius: 0.5rem;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
            }

            .product-details {
              .product-name {
                font-weight: 500;
                color: #d0d2d6;
                margin-bottom: 0.25rem;
              }

              .product-description {
                font-size: 0.75rem;
                color: #6e6b7b;
              }
            }
          }
        }

        .category-cell {
          .category-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;

            &.shoes {
              background: rgba(40, 199, 111, 0.16);
              color: #28c76f;
            }

            &.electronics {
              background: rgba(255, 159, 67, 0.16);
              color: #ff9f43;
            }

            &.accessories {
              background: rgba(108, 117, 125, 0.16);
              color: #6c757d;
            }

            &.household {
              background: rgba(255, 193, 7, 0.16);
              color: #ffc107;
            }

            .category-icon {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }

        .stock-cell {
          .stock-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.in-stock {
              background: #28c76f;
            }

            &.out-stock {
              background: #ea5455;
            }
          }
        }

        .actions-cell {
          .action-buttons {
            display: flex;
            gap: 0.5rem;

            .action-btn {
              width: 32px;
              height: 32px;
              border: none;
              border-radius: 0.375rem;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;

              &.edit-btn {
                background: rgba(115, 103, 240, 0.16);
                color: #7367f0;

                &:hover {
                  background: rgba(115, 103, 240, 0.24);
                }
              }

              &.delete-btn {
                background: rgba(234, 84, 85, 0.16);
                color: #ea5455;

                &:hover {
                  background: rgba(234, 84, 85, 0.24);
                }
              }

              &.more-btn {
                background: rgba(108, 117, 125, 0.16);
                color: #6c757d;

                &:hover {
                  background: rgba(108, 117, 125, 0.24);
                }
              }
            }
          }
        }
      }
    }
  }
}

// Vuexy 分页器样式
.vuexy-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #283046;
  border-top: 1px solid #3b4253;

  .pagination-info {
    color: #6e6b7b;
    font-size: 0.875rem;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .pagination-btn {
      border: none;
      background: transparent;
      color: #6e6b7b;
      padding: 0.5rem;
      border-radius: 0.375rem;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.875rem;

      &:hover {
        background: #3b4253;
        color: #d0d2d6;
      }

      &.active {
        background: #7367f0;
        color: white;
      }

      &.prev-btn,
      &.next-btn {
        width: 36px;
      }
    }

    .pagination-dots {
      color: #6e6b7b;
      padding: 0 0.5rem;
    }

    .buy-now-btn {
      background: #ea5455;
      border: none;
      border-radius: 0.375rem;
      padding: 0.5rem 1rem;
      color: white;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      margin-left: 1rem;
      transition: all 0.2s ease;

      &:hover {
        background: #d63384;
        transform: translateY(-1px);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .filter-left {
      width: 100%;
      flex-direction: column;
    }
  }
  
  .forms-grid {
    grid-template-columns: 1fr;
  }
}
</style>
