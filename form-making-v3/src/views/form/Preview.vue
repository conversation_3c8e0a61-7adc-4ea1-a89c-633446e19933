<template>
  <div class="form-preview">
    <div class="preview-header">
      <h2>{{ formData?.name || '表单预览' }}</h2>
      <p v-if="formData?.description" class="form-description">{{ formData.description }}</p>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-loading text="加载中..." />
    </div>
    
    <div v-else-if="formData" class="preview-content">
      <fm-generate-form
        :data="formData.form_config"
        :value="formValue"
        :remote="remote"
        :preview="true"
        ref="generateForm"
      />
      
      <div class="preview-actions">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit">提交表单</el-button>
      </div>
    </div>
    
    <div v-else class="error-container">
      <el-result
        icon="error"
        title="加载失败"
        sub-title="无法加载表单配置，请检查表单是否存在"
      >
        <template #extra>
          <el-button @click="handleClose">关闭</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { getFormById } from '../../util/api'

export default {
  name: 'FormPreview',
  setup() {
    const route = useRoute()
    const loading = ref(true)
    const formData = ref(null)
    const formValue = ref({})
    const generateForm = ref(null)
    
    // 远程数据配置
    const remote = {
      option: (model) => {
        // 这里可以配置远程选项数据
        return Promise.resolve([])
      }
    }
    
    // 加载表单数据
    const loadFormData = async () => {
      try {
        const formId = route.query.id
        if (!formId) {
          ElMessage.error('缺少表单ID参数')
          return
        }
        
        const response = await getFormById(formId)
        if (response.success) {
          formData.value = response.data
          // 初始化表单值
          if (formData.value.form_config && formData.value.form_config.list) {
            const initialValue = {}
            formData.value.form_config.list.forEach(field => {
              if (field.model) {
                initialValue[field.model] = field.options?.defaultValue || ''
              }
            })
            formValue.value = initialValue
          }
        } else {
          ElMessage.error(response.message || '加载表单失败')
        }
      } catch (error) {
        console.error('加载表单失败:', error)
        ElMessage.error('加载表单失败')
      } finally {
        loading.value = false
      }
    }
    
    // 提交表单
    const handleSubmit = () => {
      if (generateForm.value) {
        generateForm.value.getData().then(data => {
          console.log('表单数据:', data)
          ElMessage.success('表单提交成功（预览模式）')
        }).catch(error => {
          console.error('表单验证失败:', error)
          ElMessage.error('请检查表单填写是否完整')
        })
      }
    }
    
    // 关闭窗口
    const handleClose = () => {
      window.close()
    }
    
    onMounted(() => {
      loadFormData()
    })
    
    return {
      loading,
      formData,
      formValue,
      generateForm,
      remote,
      handleSubmit,
      handleClose
    }
  }
}
</script>

<style lang="scss" scoped>
.form-preview {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.preview-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
  }
  
  .form-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: white;
  border-radius: 8px;
}

.preview-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.preview-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.error-container {
  background: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}
</style>
