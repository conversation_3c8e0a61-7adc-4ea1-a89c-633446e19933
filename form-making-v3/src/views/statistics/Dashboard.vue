<template>
  <PlaceholderPage
    title="统计仪表盘"
    description="数据统计和分析仪表盘，提供各种图表和指标展示"
    :show-actions="true"
    create-text="创建仪表盘"
    @create="createDashboard"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'StatisticsDashboard',
  components: {
    PlaceholderPage
  },
  methods: {
    createDashboard() {
      console.log('创建仪表盘')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
