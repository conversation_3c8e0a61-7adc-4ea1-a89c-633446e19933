<template>
  <PlaceholderPage
    title="图表配置"
    description="配置和管理各种统计图表，支持多种图表类型和数据源"
    :show-actions="true"
    create-text="创建图表"
    @create="createChart"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'StatisticsCharts',
  components: {
    PlaceholderPage
  },
  methods: {
    createChart() {
      console.log('创建图表')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
