<template>
  <PlaceholderPage
    title="报表管理"
    description="创建和管理各种统计报表，支持数据导出和定时生成"
    :show-actions="true"
    create-text="创建报表"
    @create="createReport"
    @refresh="refreshData"
  />
</template>

<script>
import PlaceholderPage from '../../components/common/PlaceholderPage.vue'

export default {
  name: 'StatisticsReports',
  components: {
    PlaceholderPage
  },
  methods: {
    createReport() {
      console.log('创建报表')
    },
    refreshData() {
      console.log('刷新数据')
    }
  }
}
</script>
