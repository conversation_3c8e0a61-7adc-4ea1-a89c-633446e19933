<template>
  <div id="app" class="medical-lowcode-platform">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'MedicalLowcodePlatform'
}
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #2C2C2C;
  background-color: #F8F8F8;
}

#app {
  height: 100%;
  min-height: 100vh;
}

.medical-lowcode-platform {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Vuexy主题色彩变量
:root {
  // 医疗主题色
  --primary-color: #2E7D9A;
  --secondary-color: #7367F0;
  --success-color: #28C76F;
  --warning-color: #FF9F43;
  --danger-color: #EA5455;
  --info-color: #17A2B8;

  // 中性色
  --text-primary: #2C2C2C;
  --text-secondary: #6E6B7B;
  --text-light: #B9B9C3;
  --border-color: #EBE9F1;
  --background-color: #F8F8F8;
  --card-background: #FFFFFF;

  // 侧边栏色彩
  --sidebar-bg: #283046;
  --sidebar-text: #B4B7BD;
  --sidebar-active: #7367F0;
  --sidebar-hover: #34495E;

  // 阴影
  --card-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  --hover-shadow: 0 8px 32px 0 rgba(34, 41, 47, 0.15);

  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  // 间距
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 32px;
  --spacing-xl: 48px;
}

// 全局组件样式
.card {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: var(--hover-shadow);
  }
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;

  &:hover {
    background: #1e5f7a;
    border-color: #1e5f7a;
  }
}

.btn-secondary {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;

  &:hover {
    background: #5a4fcf;
    border-color: #5a4fcf;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 响应式断点
@media (max-width: 768px) {
  html {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 11px;
  }
}
</style>
