:root {
  color-scheme: light;

  // Vuexy 主色调 - 紫色渐变
  --primary-color: #7367f0;
  --primary-light: #8b7ff0;
  --primary-dark: #5e57d9;
  --primary-gradient: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);

  // 背景色系 - 深色主题
  --background-color: #f8f7fa;
  --background-dark: #283046;
  --background-darker: #1e2139;
  --card-background: #ffffff;
  --sidebar-bg: #283046;
  --navbar-bg: #ffffff;

  // 文字颜色
  --text-primary: #5e5873;
  --text-secondary: #6e6b7b;
  --text-light: #b9b9c3;
  --text-white: #ffffff;
  --sidebar-text: #b4b7bd;

  // 边框和分割线
  --border-color: #ebe9f1;
  --border-light: #f1f1f2;
  --divider-color: #ebe9f1;

  // 阴影
  --card-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  --navbar-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);
  --dropdown-shadow: 0 5px 25px rgba(34, 41, 47, 0.1);

  // 状态颜色
  --success-color: #28c76f;
  --warning-color: #ff9f43;
  --danger-color: #ea5455;
  --info-color: #00cfe8;

  // 侧边栏状态
  --sidebar-hover: rgba(255, 255, 255, 0.04);
  --sidebar-active: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;

  // 原有表单设计器变量保持兼容
  --fm-color-primary: var(--primary-color);
  --fm-widget-bg-color: var(--primary-light);
  --fm-text-color-primary: var(--text-primary);
  --fm-text-color-regular: var(--text-secondary);
  --fm-text-color-secondary: var(--text-light);
  --fm-text-color-placeholder: var(--text-light);
  --fm-text-color-disabled: var(--text-light);
  --fm-white: #fff;
  --fm-black: #000;
  --fm-border-color: var(--border-color);
  --fm-border-color-lighter: var(--border-light);
  --fm-border-color-extra: var(--border-light);
  --fm-footer-bg-color: var(--background-color);
  --fm-fill: var(--background-dark);
  --fm-fill-lighter: var(--background-color);
  --fm-fill-extra: var(--background-color);
  --fm-main-bg-color: var(--background-color);
  --fm-box-shadow: var(--card-shadow);
  --fm-box-shadow-lighter: var(--card-shadow);
  --fm-model-color: var(--success-color);
  --fm-drag-color: var(--danger-color);
  --fm-hide-bg-color: rgba(234, 84, 85, 0.1);
  --fm-container-fill-color: rgba(115, 103, 240, 0.1);
  --fm-container-color: var(--primary-color);
  --fm-container-bg-color: rgba(115, 103, 240, 0.1);
  --fm-bg-base: var(--card-background);
  --fm-container-inset-color: rgba(0,0,0,0.1);
}

html.dark {
  color-scheme: dark;

  // Vuexy 深色主题
  --primary-color: #7367f0;
  --primary-light: #8b7ff0;
  --primary-dark: #5e57d9;
  --primary-gradient: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);

  // 深色背景
  --background-color: #161d31;
  --background-dark: #283046;
  --background-darker: #1e2139;
  --card-background: #283046;
  --sidebar-bg: #283046;
  --navbar-bg: #283046;

  // 深色文字
  --text-primary: #d0d2d6;
  --text-secondary: #b4b7bd;
  --text-light: #6e6b7b;
  --text-white: #ffffff;
  --sidebar-text: #b4b7bd;

  // 深色边框
  --border-color: #3b4253;
  --border-light: #404656;
  --divider-color: #3b4253;

  // 深色阴影
  --card-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.2);
  --navbar-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.2);
  --dropdown-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);

  // 状态颜色保持不变
  --success-color: #28c76f;
  --warning-color: #ff9f43;
  --danger-color: #ea5455;
  --info-color: #00cfe8;

  // 深色侧边栏状态
  --sidebar-hover: rgba(255, 255, 255, 0.04);
  --sidebar-active: linear-gradient(135deg, #7367f0 0%, #9c88ff 100%);

  // 原有表单设计器变量保持兼容
  --fm-color-primary: var(--primary-color);
  --fm-widget-bg-color: var(--primary-light);
  --fm-text-color-primary: var(--text-primary);
  --fm-text-color-regular: var(--text-secondary);
  --fm-text-color-secondary: var(--text-light);
  --fm-text-color-placeholder: var(--text-light);
  --fm-text-color-disabled: var(--text-light);
  --fm-white: #fff;
  --fm-black: #000;
  --fm-border-color: var(--border-color);
  --fm-border-color-lighter: var(--border-light);
  --fm-border-color-extra: var(--border-light);
  --fm-footer-bg-color: var(--background-color);
  --fm-fill: var(--background-dark);
  --fm-fill-lighter: var(--background-color);
  --fm-fill-extra: var(--background-color);
  --fm-main-bg-color: var(--background-color);
  --fm-box-shadow: var(--card-shadow);
  --fm-box-shadow-lighter: var(--card-shadow);
  --fm-model-color: var(--success-color);
  --fm-drag-color: var(--danger-color);
  --fm-hide-bg-color: rgba(234, 84, 85, 0.1);
  --fm-container-fill-color: rgba(115, 103, 240, 0.1);
  --fm-container-color: var(--primary-color);
  --fm-container-bg-color: rgba(115, 103, 240, 0.1);
  --fm-bg-base: var(--card-background);
  --fm-container-inset-color: rgba(255,255,255,0.1);
}
