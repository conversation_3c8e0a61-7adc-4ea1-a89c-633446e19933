<template>
  <el-dialog
    v-model="visible"
    title="保存表单"
    width="500px"
    :before-close="handleClose"
  >
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="表单名称" prop="name">
        <el-input 
          v-model="formData.name" 
          placeholder="请输入表单名称"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="表单描述" prop="description">
        <el-input 
          v-model="formData.description" 
          type="textarea"
          placeholder="请输入表单描述（可选）"
          :rows="3"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="创建者" prop="created_by">
        <el-input 
          v-model="formData.created_by" 
          placeholder="请输入创建者名称（可选）"
          maxlength="100"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'
import { saveForm, updateForm } from '../../util/api'

export default {
  name: 'SaveDialog',
  emits: ['saved', 'updated'],
  data() {
    return {
      visible: false,
      saving: false,
      isEdit: false,
      editId: null,
      formConfig: null,
      formData: {
        name: '',
        description: '',
        created_by: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入表单名称', trigger: 'blur' },
          { min: 1, max: 255, message: '表单名称长度在 1 到 255 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    show(config, editData = null) {
      console.log('SaveDialog.show 被调用')
      console.log('config:', config)
      console.log('editData:', editData)

      this.formConfig = config
      this.isEdit = !!editData

      if (editData) {
        console.log('编辑模式，设置表单数据')
        this.editId = editData.id
        this.formData.name = editData.name || ''
        this.formData.description = editData.description || ''
        this.formData.created_by = editData.created_by || ''
        console.log('设置后的formData:', this.formData)
      } else {
        console.log('新建模式')
        this.editId = null
        this.formData.name = ''
        this.formData.description = ''
        this.formData.created_by = ''
      }

      this.visible = true
      this.$nextTick(() => {
        this.$refs.formRef?.clearValidate()
      })
    },
    
    handleClose() {
      this.visible = false
      this.saving = false
    },
    
    async handleSave() {
      try {
        await this.$refs.formRef.validate()
        
        this.saving = true
        
        const requestData = {
          name: this.formData.name,
          description: this.formData.description,
          form_config: this.formConfig,
          created_by: this.formData.created_by
        }
        
        let response
        if (this.isEdit) {
          // 更新表单
          response = await updateForm(this.editId, requestData)
          ElMessage.success('表单更新成功！')
          this.$emit('updated', response)
        } else {
          // 创建新表单
          response = await saveForm(requestData)
          ElMessage.success('表单保存成功！')
          this.$emit('saved', response)
        }
        
        this.handleClose()
      } catch (error) {
        console.error('保存表单失败:', error)
        ElMessage.error(error.response?.data?.message || '保存表单失败，请重试！')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
