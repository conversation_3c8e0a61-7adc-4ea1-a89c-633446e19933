<template>
  <div class="form-designer">
    <div class="designer-header">
      <el-button @click="handleBackToManagement">
        <el-icon><ArrowLeft /></el-icon>
        返回表单管理
      </el-button>
      <span class="form-title">
        {{ currentForm ? `编辑表单: ${currentForm.name}` : '新建表单' }}
      </span>
    </div>

    <fm-making-form
      ref="designerRef"
      :saveable="true"
      :form-config="formConfig"
      :current-form="currentForm"
      @saved="handleFormSaved"
      @updated="handleFormUpdated"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { getFormById } from '../../util/api'

export default {
  name: 'FormManager',
  components: {
    FormList,
    ArrowLeft
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const currentForm = ref(null)
    const formConfig = ref(null)
    const designerRef = ref(null)

    // 返回表单管理页面
    const handleBackToManagement = () => {
      router.push('/form/management')
    }

    // 表单保存成功
    const handleFormSaved = (response) => {
      ElMessage.success('表单保存成功')
      // 可以选择返回列表或继续编辑
      setTimeout(() => {
        handleBackToManagement()
      }, 1500)
    }

    // 表单更新成功
    const handleFormUpdated = (response) => {
      ElMessage.success('表单更新成功')
      // 更新当前表单信息
      if (response.data) {
        currentForm.value = { ...currentForm.value, ...response.data }
      }
    }

    // 初始化 - 检查URL参数
    onMounted(async () => {
      const editId = route.query.edit
      if (editId) {
        try {
          const response = await getFormById(editId)
          if (response.success) {
            currentForm.value = response.data
            formConfig.value = response.data.form_config
          } else {
            ElMessage.error('加载表单失败')
            router.push('/form/management')
          }
        } catch (error) {
          console.error('加载表单失败:', error)
          ElMessage.error('加载表单失败')
          router.push('/form/management')
        }
      }
    })

    return {
      currentForm,
      formConfig,
      designerRef,
      handleBackToManagement,
      handleFormSaved,
      handleFormUpdated
    }
  }
}
</script>

<style scoped>
.form-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.designer-header {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  gap: 15px;
}

.form-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.designer-view :deep(.fm-making-form) {
  flex: 1;
  overflow: hidden;
}
</style>
