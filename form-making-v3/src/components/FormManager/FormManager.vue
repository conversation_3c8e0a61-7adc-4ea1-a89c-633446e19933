<template>
  <div class="form-manager">
    <!-- 表单列表视图 -->
    <div v-if="currentView === 'list'" class="list-view">
      <FormList 
        @create-new="handleCreateNew"
        @edit-form="handleEditForm"
      />
    </div>

    <!-- 表单设计器视图 -->
    <div v-else-if="currentView === 'designer'" class="designer-view">
      <div class="designer-header">
        <el-button @click="handleBackToList">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
        <span class="form-title">
          {{ currentForm ? `编辑: ${currentForm.name}` : '新建表单' }}
        </span>
      </div>
      
      <fm-making-form
        ref="designerRef"
        :saveable="true"
        :form-config="formConfig"
        @saved="handleFormSaved"
        @updated="handleFormUpdated"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import FormList from './FormList.vue'
import { getFormById } from '../../util/api'

export default {
  name: 'FormManager',
  components: {
    FormList,
    ArrowLeft
  },
  setup() {
    const currentView = ref('list') // 'list' | 'designer'
    const currentForm = ref(null)
    const formConfig = ref(null)
    const designerRef = ref(null)

    // 处理创建新表单
    const handleCreateNew = () => {
      currentForm.value = null
      formConfig.value = null
      currentView.value = 'designer'
    }

    // 处理编辑表单
    const handleEditForm = async (form) => {
      try {
        // 获取完整的表单配置
        const response = await getFormById(form.id)
        if (response.success) {
          currentForm.value = form
          formConfig.value = response.data.form_config
          currentView.value = 'designer'
        } else {
          ElMessage.error(response.message || '加载表单失败')
        }
      } catch (error) {
        console.error('加载表单失败:', error)
        ElMessage.error('加载表单失败')
      }
    }

    // 返回列表
    const handleBackToList = () => {
      currentView.value = 'list'
      currentForm.value = null
      formConfig.value = null
    }

    // 表单保存成功
    const handleFormSaved = (response) => {
      ElMessage.success('表单保存成功')
      // 可以选择返回列表或继续编辑
      setTimeout(() => {
        handleBackToList()
      }, 1500)
    }

    // 表单更新成功
    const handleFormUpdated = (response) => {
      ElMessage.success('表单更新成功')
      // 更新当前表单信息
      if (response.data) {
        currentForm.value = { ...currentForm.value, ...response.data }
      }
    }

    return {
      currentView,
      currentForm,
      formConfig,
      designerRef,
      handleCreateNew,
      handleEditForm,
      handleBackToList,
      handleFormSaved,
      handleFormUpdated
    }
  }
}
</script>

<style scoped>
.form-manager {
  height: 100vh;
  overflow: hidden;
}

.list-view {
  height: 100%;
  overflow: auto;
}

.designer-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.designer-header {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  gap: 15px;
}

.form-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.designer-view :deep(.fm-making-form) {
  flex: 1;
  overflow: hidden;
}
</style>
