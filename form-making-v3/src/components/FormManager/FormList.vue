<template>
  <div class="form-list-container">
    <div class="header">
      <h2>表单管理</h2>
      <el-button type="primary" @click="$emit('create-new')">
        <el-icon><Plus /></el-icon>
        新建表单
      </el-button>
    </div>

    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索表单名称或描述"
        style="width: 300px"
        @input="handleSearch"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <el-table
      v-loading="loading"
      :data="formList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="表单名称" min-width="150">
        <template #default="{ row }">
          <el-link type="primary" @click="handleEdit(row)">
            {{ row.name }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="created_by" label="创建者" width="120" />
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="updated_at" label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" @click="handlePreview(row)">预览</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedForms.length > 0" class="batch-actions">
      <el-button type="danger" @click="handleBatchDelete">
        批量删除 ({{ selectedForms.length }})
      </el-button>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="表单预览"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <div v-if="previewForm" class="preview-container">
        <div class="preview-header">
          <h3>{{ previewForm.name }}</h3>
          <p>{{ previewForm.description }}</p>
        </div>
        <div class="form-preview">
          <!-- 使用真正的表单渲染组件 -->
          <fm-generate-form
            v-if="previewForm.form_config"
            :data="previewForm.form_config"
            :value="{}"
            :preview="true"
            ref="previewFormRef"
          />
          <div v-else class="no-config">
            <el-empty description="表单配置为空" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { getFormList, deleteForm } from '../../util/api'
import GenerateForm from '../GenerateForm.vue'

export default {
  name: 'FormList',
  components: {
    Plus,
    Search,
    'fm-generate-form': GenerateForm
  },
  emits: ['create-new', 'edit-form'],
  setup(props, { emit }) {
    const loading = ref(false)
    const formList = ref([])
    const searchKeyword = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const selectedForms = ref([])
    const previewVisible = ref(false)
    const previewForm = ref(null)

    // 加载表单列表
    const loadFormList = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }
        if (searchKeyword.value) {
          params.search = searchKeyword.value
        }
        
        const response = await getFormList(params)
        if (response.success) {
          formList.value = response.data.list
          total.value = response.data.pagination.total
        } else {
          ElMessage.error(response.message || '加载表单列表失败')
        }
      } catch (error) {
        console.error('加载表单列表失败:', error)
        ElMessage.error('加载表单列表失败')
      } finally {
        loading.value = false
      }
    }

    // 搜索处理
    let searchTimer = null
    const handleSearch = () => {
      if (searchTimer) {
        clearTimeout(searchTimer)
      }
      searchTimer = setTimeout(() => {
        currentPage.value = 1
        loadFormList()
      }, 500)
    }

    // 分页处理
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      loadFormList()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadFormList()
    }

    // 选择处理
    const handleSelectionChange = (selection) => {
      selectedForms.value = selection
    }

    // 编辑表单
    const handleEdit = (form) => {
      emit('edit-form', form)
    }

    // 预览表单
    const handlePreview = (form) => {
      console.log('=== 预览表单数据 ===')
      console.log('原始表单数据:', form)
      console.log('form_config:', form.form_config)
      console.log('form_config 类型:', typeof form.form_config)

      // 确保form_config是对象格式
      let formConfig = form.form_config
      if (typeof formConfig === 'string') {
        try {
          formConfig = JSON.parse(formConfig)
        } catch (e) {
          console.error('解析form_config失败:', e)
          ElMessage.error('表单配置数据格式错误')
          return
        }
      }

      previewForm.value = {
        ...form,
        form_config: formConfig
      }
      previewVisible.value = true
    }

    const handlePreviewClose = () => {
      previewVisible.value = false
      previewForm.value = null
    }

    // 删除表单
    const handleDelete = async (form) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除表单"${form.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const response = await deleteForm(form.id)
        if (response.success) {
          ElMessage.success('删除成功')
          loadFormList()
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除表单失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 批量删除
    const handleBatchDelete = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedForms.value.length} 个表单吗？此操作不可恢复。`,
          '确认批量删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const deletePromises = selectedForms.value.map(form => deleteForm(form.id))
        await Promise.all(deletePromises)
        
        ElMessage.success('批量删除成功')
        selectedForms.value = []
        loadFormList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          ElMessage.error('批量删除失败')
        }
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    onMounted(() => {
      loadFormList()
    })

    return {
      loading,
      formList,
      searchKeyword,
      currentPage,
      pageSize,
      total,
      selectedForms,
      previewVisible,
      previewForm,
      loadFormList,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      handleEdit,
      handlePreview,
      handlePreviewClose,
      handleDelete,
      handleBatchDelete,
      formatDate
    }
  }
}
</script>

<style scoped>
.form-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.preview-container {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.preview-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.preview-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.form-preview {
  background: #ffffff;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  min-height: 200px;
}

.no-config {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
</style>
