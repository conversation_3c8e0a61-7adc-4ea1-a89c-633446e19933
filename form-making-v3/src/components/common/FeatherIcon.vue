<template>
  <i 
    :class="iconClass" 
    :style="iconStyle"
    v-html="iconSvg"
  ></i>
</template>

<script>
import { computed } from 'vue'
import feather from 'feather-icons'

export default {
  name: 'FeatherIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: [String, Number],
      default: 16
    },
    color: {
      type: String,
      default: 'currentColor'
    },
    strokeWidth: {
      type: [String, Number],
      default: 2
    }
  },
  setup(props) {
    const iconSvg = computed(() => {
      try {
        return feather.icons[props.name]?.toSvg({
          width: props.size,
          height: props.size,
          color: props.color,
          'stroke-width': props.strokeWidth
        }) || ''
      } catch (error) {
        console.warn(`Icon "${props.name}" not found in feather-icons`)
        return ''
      }
    })
    
    const iconClass = computed(() => {
      return ['feather-icon', `feather-icon-${props.name}`]
    })
    
    const iconStyle = computed(() => {
      return {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: `${props.size}px`,
        height: `${props.size}px`
      }
    })
    
    return {
      iconSvg,
      iconClass,
      iconStyle
    }
  }
}
</script>

<style lang="scss" scoped>
.feather-icon {
  :deep(svg) {
    display: block;
  }
}
</style>
