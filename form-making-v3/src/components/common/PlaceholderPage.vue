<template>
  <div class="placeholder-page">
    <div class="placeholder-content">
      <div class="placeholder-icon">
        <i class="icon-file-text"></i>
      </div>
      <h2 class="placeholder-title">{{ title }}</h2>
      <p class="placeholder-description">{{ description }}</p>
      <div class="placeholder-actions" v-if="showActions">
        <el-button type="primary" @click="$emit('create')">
          <i class="icon-plus"></i>
          {{ createText }}
        </el-button>
        <el-button @click="$emit('refresh')">
          <i class="icon-refresh-cw"></i>
          刷新
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PlaceholderPage',
  props: {
    title: {
      type: String,
      default: '功能开发中'
    },
    description: {
      type: String,
      default: '该功能正在开发中，敬请期待...'
    },
    showActions: {
      type: Boolean,
      default: false
    },
    createText: {
      type: String,
      default: '创建'
    }
  },
  emits: ['create', 'refresh']
}
</script>

<style lang="scss" scoped>
.placeholder-page {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  
  .placeholder-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
    
    .placeholder-icon {
      font-size: 64px;
      color: var(--text-light);
      margin-bottom: var(--spacing-md);
    }
    
    .placeholder-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }
    
    .placeholder-description {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-lg) 0;
      line-height: 1.5;
    }
    
    .placeholder-actions {
      display: flex;
      gap: var(--spacing-sm);
      justify-content: center;
    }
  }
}
</style>
