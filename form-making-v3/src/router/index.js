import { createRouter, createWebHashHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 布局组件
import MainLayout from '../layouts/MainLayout.vue'

// 页面组件
import Dashboard from '../views/Dashboard.vue'
import Login from '../views/Login.vue'
import Home from '../demo/Home.vue'
import Test from '../demo/Test.vue'
import FormManager from '../components/FormManager/FormManager.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        component: Dashboard,
        meta: { title: '仪表盘', requiresAuth: true }
      }
    ]
  },
  {
    path: '/form',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'designer',
        component: Home,
        meta: { title: '表单设计器' }
      },
      {
        path: 'management',
        component: () => import('../views/form/Management.vue'),
        meta: { title: '表单管理' }
      },
      {
        path: 'templates',
        component: () => import('../views/form/Templates.vue'),
        meta: { title: '表单模板' }
      },
      {
        path: 'legacy',
        component: () => import('../views/form/Designer.vue'),
        meta: { title: '新版表单设计器（开发中）' }
      }
    ]
  },
  {
    path: '/list',
    component: MainLayout,
    children: [
      {
        path: 'designer',
        component: () => import('../views/list/Designer.vue'),
        meta: { title: '列表设计器' }
      },
      {
        path: 'views',
        component: () => import('../views/list/Views.vue'),
        meta: { title: '列表视图' }
      }
    ]
  },
  {
    path: '/statistics',
    component: MainLayout,
    children: [
      {
        path: 'dashboard',
        component: () => import('../views/statistics/Dashboard.vue'),
        meta: { title: '统计仪表盘' }
      },
      {
        path: 'reports',
        component: () => import('../views/statistics/Reports.vue'),
        meta: { title: '报表管理' }
      },
      {
        path: 'charts',
        component: () => import('../views/statistics/Charts.vue'),
        meta: { title: '图表配置' }
      }
    ]
  },
  {
    path: '/dictionary',
    component: MainLayout,
    children: [
      {
        path: 'categories',
        component: () => import('../views/dictionary/Categories.vue'),
        meta: { title: '字典分类' }
      },
      {
        path: 'items',
        component: () => import('../views/dictionary/Items.vue'),
        meta: { title: '字典维护' }
      },
      {
        path: 'versions',
        component: () => import('../views/dictionary/Versions.vue'),
        meta: { title: '版本管理' }
      }
    ]
  },
  {
    path: '/workflow',
    component: MainLayout,
    children: [
      {
        path: 'designer',
        component: () => import('../views/workflow/Designer.vue'),
        meta: { title: '流程设计' }
      },
      {
        path: 'instances',
        component: () => import('../views/workflow/Instances.vue'),
        meta: { title: '流程实例' }
      },
      {
        path: 'tasks',
        component: () => import('../views/workflow/Tasks.vue'),
        meta: { title: '待办任务' }
      }
    ]
  },
  {
    path: '/system',
    component: MainLayout,
    children: [
      {
        path: 'users',
        component: () => import('../views/system/Users.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'roles',
        component: () => import('../views/system/Roles.vue'),
        meta: { title: '角色管理' }
      },
      {
        path: 'permissions',
        component: () => import('../views/system/Permissions.vue'),
        meta: { title: '权限管理' }
      },
      {
        path: 'departments',
        component: () => import('../views/system/Departments.vue'),
        meta: { title: '部门管理' }
      }
    ]
  },
  // 兼容旧路由
  {
    path: '/test',
    component: Test
  },
  // 认证测试页面
  {
    path: '/auth-test',
    component: () => import('../views/AuthTest.vue'),
    meta: {
      title: '认证测试',
      requiresAuth: false
    }
  },
  // 简单测试页面
  {
    path: '/simple-test',
    component: () => import('../views/SimpleTest.vue'),
    meta: {
      title: '简单测试',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - 医疗低代码平台`
  } else {
    document.title = '医疗低代码平台'
  }

  // 检查是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)

  if (requiresAuth) {
    const authStore = useAuthStore()

    // 如果还没有初始化认证状态，先初始化
    if (!authStore.isAuthenticated && !authStore.user) {
      try {
        await authStore.initAuth()
      } catch (error) {
        console.error('认证初始化失败:', error)
      }
    }

    // 检查是否已认证
    if (!authStore.isAuthenticated) {
      // 保存当前路径，登录后跳转回来
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  next()
})

export default router