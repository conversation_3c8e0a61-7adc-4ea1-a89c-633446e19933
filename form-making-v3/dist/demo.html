<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <!-- 引入样式 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css">
  <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/vant@4/lib/index.css"/>
  <link rel="stylesheet" href="./index.css">
  <style>
    html,body,#app{
      height: 100%;
    }
  </style>
</head>
<body >
  <div id="app" >
    <fm-making-form preview generate-code generate-json use-vant-form>
    </fm-making-form>
  </div>
</body>
<!-- 引入组件库 -->
<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.global.prod.js"></script>
<script src="https://cdn.jsdelivr.net/npm/element-plus"></script>
<script src="https://fastly.jsdelivr.net/npm/vant@4.9.9/lib/vant.min.js"></script>
<script src="./form-making-v3.umd.js"></script>

<script>
  Vue.createApp({}).use(ElementPlus).use(vant).use(FormMakingV3).mount('#app')
</script>
</html>