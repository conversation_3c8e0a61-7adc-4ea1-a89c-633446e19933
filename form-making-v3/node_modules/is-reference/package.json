{"name": "is-reference", "version": "1.2.1", "description": "Determine whether an AST node is a reference", "main": "dist/is-reference.js", "module": "dist/is-reference.es.js", "types": "dist/types/index.d.ts", "files": ["dist/*.js", "dist/types/**/*.d.ts"], "scripts": {"test": "mocha", "build": "rollup -c && tsc --emitDeclarationOnly", "pretest": "npm run build", "prepare": "npm run build", "prepublishOnly": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/is-reference.git"}, "keywords": ["ast", "javascript", "estree", "acorn"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/is-reference/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/is-reference#readme", "dependencies": {"@types/estree": "*"}, "devDependencies": {"acorn": "^7.2.0", "acorn-class-fields": "^0.3.2", "acorn-static-class-features": "^0.2.1", "estree-walker": "^2.0.1", "mocha": "^7.1.2", "rollup": "^2.10.3", "rollup-plugin-typescript": "^1.0.1", "tslib": "^2.0.0", "typescript": "^3.9.2"}}