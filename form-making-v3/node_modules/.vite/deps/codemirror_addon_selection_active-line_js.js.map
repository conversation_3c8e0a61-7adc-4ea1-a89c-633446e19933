{"version": 3, "sources": ["../../codemirror/addon/selection/active-line.js", "dep:codemirror_addon_selection_active-line_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var WRAP_CLASS = \"CodeMirror-activeline\";\n  var BACK_CLASS = \"CodeMirror-activeline-background\";\n  var GUTT_CLASS = \"CodeMirror-activeline-gutter\";\n\n  CodeMirror.defineOption(\"styleActiveLine\", false, function(cm, val, old) {\n    var prev = old == CodeMirror.Init ? false : old;\n    if (val == prev) return\n    if (prev) {\n      cm.off(\"beforeSelectionChange\", selectionChange);\n      clearActiveLines(cm);\n      delete cm.state.activeLines;\n    }\n    if (val) {\n      cm.state.activeLines = [];\n      updateActiveLines(cm, cm.listSelections());\n      cm.on(\"beforeSelectionChange\", selectionChange);\n    }\n  });\n\n  function clearActiveLines(cm) {\n    for (var i = 0; i < cm.state.activeLines.length; i++) {\n      cm.removeLineClass(cm.state.activeLines[i], \"wrap\", WRAP_CLASS);\n      cm.removeLineClass(cm.state.activeLines[i], \"background\", BACK_CLASS);\n      cm.removeLineClass(cm.state.activeLines[i], \"gutter\", GUTT_CLASS);\n    }\n  }\n\n  function sameArray(a, b) {\n    if (a.length != b.length) return false;\n    for (var i = 0; i < a.length; i++)\n      if (a[i] != b[i]) return false;\n    return true;\n  }\n\n  function updateActiveLines(cm, ranges) {\n    var active = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      var option = cm.getOption(\"styleActiveLine\");\n      if (typeof option == \"object\" && option.nonEmpty ? range.anchor.line != range.head.line : !range.empty())\n        continue\n      var line = cm.getLineHandleVisualStart(range.head.line);\n      if (active[active.length - 1] != line) active.push(line);\n    }\n    if (sameArray(cm.state.activeLines, active)) return;\n    cm.operation(function() {\n      clearActiveLines(cm);\n      for (var i = 0; i < active.length; i++) {\n        cm.addLineClass(active[i], \"wrap\", WRAP_CLASS);\n        cm.addLineClass(active[i], \"background\", BACK_CLASS);\n        cm.addLineClass(active[i], \"gutter\", GUTT_CLASS);\n      }\n      cm.state.activeLines = active;\n    });\n  }\n\n  function selectionChange(cm, sel) {\n    updateActiveLines(cm, sel.ranges);\n  }\n});\n", "export default require(\"./node_modules/codemirror/addon/selection/active-line.js\");"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AACA,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,MAAAA,YAAW,aAAa,mBAAmB,OAAO,SAAS,IAAI,KAAK,KAAK;AACvE,YAAI,OAAO,OAAOA,YAAW,OAAO,QAAQ;AAC5C,YAAI,OAAO;AAAM;AACjB,YAAI,MAAM;AACR,aAAG,IAAI,yBAAyB,eAAe;AAC/C,2BAAiB,EAAE;AACnB,iBAAO,GAAG,MAAM;AAAA,QAClB;AACA,YAAI,KAAK;AACP,aAAG,MAAM,cAAc,CAAC;AACxB,4BAAkB,IAAI,GAAG,eAAe,CAAC;AACzC,aAAG,GAAG,yBAAyB,eAAe;AAAA,QAChD;AAAA,MACF,CAAC;AAED,eAAS,iBAAiB,IAAI;AAC5B,iBAAS,IAAI,GAAG,IAAI,GAAG,MAAM,YAAY,QAAQ,KAAK;AACpD,aAAG,gBAAgB,GAAG,MAAM,YAAY,IAAI,QAAQ,UAAU;AAC9D,aAAG,gBAAgB,GAAG,MAAM,YAAY,IAAI,cAAc,UAAU;AACpE,aAAG,gBAAgB,GAAG,MAAM,YAAY,IAAI,UAAU,UAAU;AAAA,QAClE;AAAA,MACF;AAEA,eAAS,UAAU,GAAG,GAAG;AACvB,YAAI,EAAE,UAAU,EAAE;AAAQ,iBAAO;AACjC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,cAAI,EAAE,MAAM,EAAE;AAAI,mBAAO;AAC3B,eAAO;AAAA,MACT;AAEA,eAAS,kBAAkB,IAAI,QAAQ;AACrC,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO;AACnB,cAAI,SAAS,GAAG,UAAU,iBAAiB;AAC3C,cAAI,OAAO,UAAU,YAAY,OAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,KAAK,OAAO,CAAC,MAAM,MAAM;AACrG;AACF,cAAI,OAAO,GAAG,yBAAyB,MAAM,KAAK,IAAI;AACtD,cAAI,OAAO,OAAO,SAAS,MAAM;AAAM,mBAAO,KAAK,IAAI;AAAA,QACzD;AACA,YAAI,UAAU,GAAG,MAAM,aAAa,MAAM;AAAG;AAC7C,WAAG,UAAU,WAAW;AACtB,2BAAiB,EAAE;AACnB,mBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,eAAG,aAAa,OAAOA,KAAI,QAAQ,UAAU;AAC7C,eAAG,aAAa,OAAOA,KAAI,cAAc,UAAU;AACnD,eAAG,aAAa,OAAOA,KAAI,UAAU,UAAU;AAAA,UACjD;AACA,aAAG,MAAM,cAAc;AAAA,QACzB,CAAC;AAAA,MACH;AAEA,eAAS,gBAAgB,IAAI,KAAK;AAChC,0BAAkB,IAAI,IAAI,MAAM;AAAA,MAClC;AAAA,IACF,CAAC;AAAA;AAAA;;;ACvED,IAAO,oDAAQ;", "names": ["CodeMirror", "i"]}