{"version": 3, "sources": ["../../viewerjs/dist/viewer.esm.js", "dep:viewerjs"], "sourcesContent": ["/*!\n * Viewer.js v1.11.7\n * https://fengyuanchen.github.io/viewerjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2024-11-24T04:32:19.116Z\n */\n\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nvar DEFAULTS = {\n  /**\n   * Enable a modal backdrop, specify `static` for a backdrop\n   * which doesn't close the modal on click.\n   * @type {boolean}\n   */\n  backdrop: true,\n  /**\n   * Show the button on the top-right of the viewer.\n   * @type {boolean}\n   */\n  button: true,\n  /**\n   * Show the navbar.\n   * @type {boolean | number}\n   */\n  navbar: true,\n  /**\n   * Specify the visibility and the content of the title.\n   * @type {boolean | number | Function | Array}\n   */\n  title: true,\n  /**\n   * Show the toolbar.\n   * @type {boolean | number | Object}\n   */\n  toolbar: true,\n  /**\n   * Custom class name(s) to add to the viewer's root element.\n   * @type {string}\n   */\n  className: '',\n  /**\n   * Define where to put the viewer in modal mode.\n   * @type {string | Element}\n   */\n  container: 'body',\n  /**\n   * Filter the images for viewing. Return true if the image is viewable.\n   * @type {Function}\n   */\n  filter: null,\n  /**\n   * Enable to request fullscreen when play.\n   * {@link https://developer.mozilla.org/en-US/docs/Web/API/FullscreenOptions}\n   * @type {boolean|FullscreenOptions}\n   */\n  fullscreen: true,\n  /**\n   * Define the extra attributes to inherit from the original image.\n   * @type {Array}\n   */\n  inheritedAttributes: ['crossOrigin', 'decoding', 'isMap', 'loading', 'referrerPolicy', 'sizes', 'srcset', 'useMap'],\n  /**\n   * Define the initial coverage of the viewing image.\n   * @type {number}\n   */\n  initialCoverage: 0.9,\n  /**\n   * Define the initial index of the image for viewing.\n   * @type {number}\n   */\n  initialViewIndex: 0,\n  /**\n   * Enable inline mode.\n   * @type {boolean}\n   */\n  inline: false,\n  /**\n   * The amount of time to delay between automatically cycling an image when playing.\n   * @type {number}\n   */\n  interval: 5000,\n  /**\n   * Enable keyboard support.\n   * @type {boolean}\n   */\n  keyboard: true,\n  /**\n   * Focus the viewer when initialized.\n   * @type {boolean}\n   */\n  focus: true,\n  /**\n   * Indicate if show a loading spinner when load image or not.\n   * @type {boolean}\n   */\n  loading: true,\n  /**\n   * Indicate if enable loop viewing or not.\n   * @type {boolean}\n   */\n  loop: true,\n  /**\n   * Min width of the viewer in inline mode.\n   * @type {number}\n   */\n  minWidth: 200,\n  /**\n   * Min height of the viewer in inline mode.\n   * @type {number}\n   */\n  minHeight: 100,\n  /**\n   * Enable to move the image.\n   * @type {boolean}\n   */\n  movable: true,\n  /**\n   * Enable to rotate the image.\n   * @type {boolean}\n   */\n  rotatable: true,\n  /**\n   * Enable to scale the image.\n   * @type {boolean}\n   */\n  scalable: true,\n  /**\n   * Enable to zoom the image.\n   * @type {boolean}\n   */\n  zoomable: true,\n  /**\n   * Enable to zoom the current image by dragging on the touch screen.\n   * @type {boolean}\n   */\n  zoomOnTouch: true,\n  /**\n   * Enable to zoom the image by wheeling mouse.\n   * @type {boolean}\n   */\n  zoomOnWheel: true,\n  /**\n   * Enable to slide to the next or previous image by swiping on the touch screen.\n   * @type {boolean}\n   */\n  slideOnTouch: true,\n  /**\n   * Indicate if toggle the image size between its natural size\n   * and initial size when double click on the image or not.\n   * @type {boolean}\n   */\n  toggleOnDblclick: true,\n  /**\n   * Show the tooltip with image ratio (percentage) when zoom in or zoom out.\n   * @type {boolean}\n   */\n  tooltip: true,\n  /**\n   * Enable CSS3 Transition for some special elements.\n   * @type {boolean}\n   */\n  transition: true,\n  /**\n   * Define the CSS `z-index` value of viewer in modal mode.\n   * @type {number}\n   */\n  zIndex: 2015,\n  /**\n   * Define the CSS `z-index` value of viewer in inline mode.\n   * @type {number}\n   */\n  zIndexInline: 0,\n  /**\n   * Define the ratio when zoom the image by wheeling mouse.\n   * @type {number}\n   */\n  zoomRatio: 0.1,\n  /**\n   * Define the min ratio of the image when zoom out.\n   * @type {number}\n   */\n  minZoomRatio: 0.01,\n  /**\n   * Define the max ratio of the image when zoom in.\n   * @type {number}\n   */\n  maxZoomRatio: 100,\n  /**\n   * Define where to get the original image URL for viewing.\n   * @type {string | Function}\n   */\n  url: 'src',\n  /**\n   * Event shortcuts.\n   * @type {Function}\n   */\n  ready: null,\n  show: null,\n  shown: null,\n  hide: null,\n  hidden: null,\n  view: null,\n  viewed: null,\n  move: null,\n  moved: null,\n  rotate: null,\n  rotated: null,\n  scale: null,\n  scaled: null,\n  zoom: null,\n  zoomed: null,\n  play: null,\n  stop: null\n};\n\nvar TEMPLATE = '<div class=\"viewer-container\" tabindex=\"-1\" touch-action=\"none\">' + '<div class=\"viewer-canvas\"></div>' + '<div class=\"viewer-footer\">' + '<div class=\"viewer-title\"></div>' + '<div class=\"viewer-toolbar\"></div>' + '<div class=\"viewer-navbar\">' + '<ul class=\"viewer-list\" role=\"navigation\"></ul>' + '</div>' + '</div>' + '<div class=\"viewer-tooltip\" role=\"alert\" aria-hidden=\"true\"></div>' + '<div class=\"viewer-button\" data-viewer-action=\"mix\" role=\"button\"></div>' + '<div class=\"viewer-player\"></div>' + '</div>';\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'viewer';\n\n// Actions\nvar ACTION_MOVE = 'move';\nvar ACTION_SWITCH = 'switch';\nvar ACTION_ZOOM = 'zoom';\n\n// Classes\nvar CLASS_ACTIVE = \"\".concat(NAMESPACE, \"-active\");\nvar CLASS_CLOSE = \"\".concat(NAMESPACE, \"-close\");\nvar CLASS_FADE = \"\".concat(NAMESPACE, \"-fade\");\nvar CLASS_FIXED = \"\".concat(NAMESPACE, \"-fixed\");\nvar CLASS_FULLSCREEN = \"\".concat(NAMESPACE, \"-fullscreen\");\nvar CLASS_FULLSCREEN_EXIT = \"\".concat(NAMESPACE, \"-fullscreen-exit\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_HIDE_MD_DOWN = \"\".concat(NAMESPACE, \"-hide-md-down\");\nvar CLASS_HIDE_SM_DOWN = \"\".concat(NAMESPACE, \"-hide-sm-down\");\nvar CLASS_HIDE_XS_DOWN = \"\".concat(NAMESPACE, \"-hide-xs-down\");\nvar CLASS_IN = \"\".concat(NAMESPACE, \"-in\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_LOADING = \"\".concat(NAMESPACE, \"-loading\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\nvar CLASS_OPEN = \"\".concat(NAMESPACE, \"-open\");\nvar CLASS_SHOW = \"\".concat(NAMESPACE, \"-show\");\nvar CLASS_TRANSITION = \"\".concat(NAMESPACE, \"-transition\");\n\n// Native events\nvar EVENT_CLICK = 'click';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_DRAG_START = 'dragstart';\nvar EVENT_FOCUSIN = 'focusin';\nvar EVENT_KEY_DOWN = 'keydown';\nvar EVENT_LOAD = 'load';\nvar EVENT_ERROR = 'error';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_RESIZE = 'resize';\nvar EVENT_TRANSITION_END = 'transitionend';\nvar EVENT_WHEEL = 'wheel';\n\n// Custom events\nvar EVENT_READY = 'ready';\nvar EVENT_SHOW = 'show';\nvar EVENT_SHOWN = 'shown';\nvar EVENT_HIDE = 'hide';\nvar EVENT_HIDDEN = 'hidden';\nvar EVENT_VIEW = 'view';\nvar EVENT_VIEWED = 'viewed';\nvar EVENT_MOVE = 'move';\nvar EVENT_MOVED = 'moved';\nvar EVENT_ROTATE = 'rotate';\nvar EVENT_ROTATED = 'rotated';\nvar EVENT_SCALE = 'scale';\nvar EVENT_SCALED = 'scaled';\nvar EVENT_ZOOM = 'zoom';\nvar EVENT_ZOOMED = 'zoomed';\nvar EVENT_PLAY = 'play';\nvar EVENT_STOP = 'stop';\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\n\n// RegExps\nvar REGEXP_SPACES = /\\s\\s*/;\n\n// Misc\nvar BUTTONS = ['zoom-in', 'zoom-out', 'one-to-one', 'reset', 'prev', 'play', 'next', 'rotate-left', 'rotate-right', 'flip-horizontal', 'flip-vertical'];\n\n/**\n * Check if the given value is a string.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a string, else `false`.\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      var length = data.length;\n      var i;\n      for (i = 0; i < length; i += 1) {\n        if (callback.call(data, data[i], i, data) === false) {\n          break;\n        }\n      }\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} obj - The object to be extended.\n * @param {*} args - The rest objects which will be merged to the first object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(obj) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(obj) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          obj[key] = arg[key];\n        });\n      }\n    });\n  }\n  return obj;\n};\nvar REGEXP_SUFFIX = /^(?:width|height|left|top|marginLeft|marginTop)$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value += 'px';\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Escape a string for using in HTML.\n * @param {String} value - The string to escape.\n * @returns {String} Returns the escaped string.\n */\nfunction escapeHTMLEntities(value) {\n  return isString(value) ? value.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;') : value;\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  if (!element || !value) {\n    return false;\n  }\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!element || !value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!element || !value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_HYPHENATE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction hyphenate(value) {\n  return value.replace(REGEXP_HYPHENATE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(hyphenate(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(hyphenate(name)), data);\n  }\n}\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @param {Object} options - The additional event options.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data, options) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, _objectSpread2({\n      bubbles: true,\n      cancelable: true,\n      detail: data\n    }, options));\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get an image name from an image url.\n * @param {string} url - The target url.\n * @example\n * // picture.jpg\n * getImageNameFromURL('https://domain.com/path/to/picture.jpg?size=1280×960')\n * @returns {string} A string contains the image name.\n */\nfunction getImageNameFromURL(url) {\n  return isString(url) ? decodeURIComponent(url.replace(/^.*\\//, '').replace(/[?&#].*$/, '')) : '';\n}\nvar IS_SAFARI = WINDOW.navigator && /Version\\/\\d+(\\.\\d+)+?\\s+Safari/i.test(WINDOW.navigator.userAgent);\n\n/**\n * Get an image's natural sizes.\n * @param {string} image - The target image.\n * @param {Object} options - The viewer options.\n * @param {Function} callback - The callback function.\n * @returns {HTMLImageElement} The new image.\n */\nfunction getImageNaturalSizes(image, options, callback) {\n  var newImage = document.createElement('img');\n\n  // Modern browsers (except Safari)\n  if (image.naturalWidth && !IS_SAFARI) {\n    callback(image.naturalWidth, image.naturalHeight);\n    return newImage;\n  }\n  var body = document.body || document.documentElement;\n  newImage.onload = function () {\n    callback(newImage.width, newImage.height);\n    if (!IS_SAFARI) {\n      body.removeChild(newImage);\n    }\n  };\n  forEach(options.inheritedAttributes, function (name) {\n    var value = image.getAttribute(name);\n    if (value !== null) {\n      newImage.setAttribute(name, value);\n    }\n  });\n  newImage.src = image.src;\n\n  // iOS Safari will convert the image automatically\n  // with its orientation once append it into DOM\n  if (!IS_SAFARI) {\n    newImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n    body.appendChild(newImage);\n  }\n  return newImage;\n}\n\n/**\n * Get the related class name of a responsive type number.\n * @param {string} type - The responsive type.\n * @returns {string} The related class name.\n */\nfunction getResponsiveClass(type) {\n  switch (type) {\n    case 2:\n      return CLASS_HIDE_XS_DOWN;\n    case 3:\n      return CLASS_HIDE_SM_DOWN;\n    case 4:\n      return CLASS_HIDE_MD_DOWN;\n    default:\n      return '';\n  }\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var ratios = [];\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      ratios.push(ratio);\n    });\n  });\n  ratios.sort(function (a, b) {\n    return Math.abs(a) < Math.abs(b);\n  });\n  return ratios[0];\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    timeStamp: Date.now(),\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initViewer();\n    this.initList();\n    this.renderViewer();\n  },\n  initBody: function initBody() {\n    var ownerDocument = this.element.ownerDocument;\n    var body = ownerDocument.body || ownerDocument.documentElement;\n    this.body = body;\n    this.scrollbarWidth = window.innerWidth - ownerDocument.documentElement.clientWidth;\n    this.initialBodyPaddingRight = body.style.paddingRight;\n    this.initialBodyComputedPaddingRight = window.getComputedStyle(body).paddingRight;\n  },\n  initContainer: function initContainer() {\n    this.containerData = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  },\n  initViewer: function initViewer() {\n    var options = this.options,\n      parent = this.parent;\n    var viewerData;\n    if (options.inline) {\n      viewerData = {\n        width: Math.max(parent.offsetWidth, options.minWidth),\n        height: Math.max(parent.offsetHeight, options.minHeight)\n      };\n      this.parentData = viewerData;\n    }\n    if (this.fulled || !viewerData) {\n      viewerData = this.containerData;\n    }\n    this.viewerData = assign({}, viewerData);\n  },\n  renderViewer: function renderViewer() {\n    if (this.options.inline && !this.fulled) {\n      setStyle(this.viewer, this.viewerData);\n    }\n  },\n  initList: function initList() {\n    var _this = this;\n    var element = this.element,\n      options = this.options,\n      list = this.list;\n    var items = [];\n\n    // initList may be called in this.update, so should keep idempotent\n    list.innerHTML = '';\n    forEach(this.images, function (image, index) {\n      var src = image.src;\n      var alt = image.alt || getImageNameFromURL(src);\n      var url = _this.getImageURL(image);\n      if (src || url) {\n        var item = document.createElement('li');\n        var img = document.createElement('img');\n        forEach(options.inheritedAttributes, function (name) {\n          var value = image.getAttribute(name);\n          if (value !== null) {\n            img.setAttribute(name, value);\n          }\n        });\n        if (options.navbar) {\n          img.src = src || url;\n        }\n        img.alt = alt;\n        img.setAttribute('data-original-url', url || src);\n        item.setAttribute('data-index', index);\n        item.setAttribute('data-viewer-action', 'view');\n        item.setAttribute('role', 'button');\n        if (options.keyboard) {\n          item.setAttribute('tabindex', 0);\n        }\n        item.appendChild(img);\n        list.appendChild(item);\n        items.push(item);\n      }\n    });\n    this.items = items;\n    forEach(items, function (item) {\n      var image = item.firstElementChild;\n      var onLoad;\n      var onError;\n      setData(image, 'filled', true);\n      if (options.loading) {\n        addClass(item, CLASS_LOADING);\n      }\n      addListener(image, EVENT_LOAD, onLoad = function onLoad(event) {\n        removeListener(image, EVENT_ERROR, onError);\n        if (options.loading) {\n          removeClass(item, CLASS_LOADING);\n        }\n        _this.loadImage(event);\n      }, {\n        once: true\n      });\n      addListener(image, EVENT_ERROR, onError = function onError() {\n        removeListener(image, EVENT_LOAD, onLoad);\n        if (options.loading) {\n          removeClass(item, CLASS_LOADING);\n        }\n      }, {\n        once: true\n      });\n    });\n    if (options.transition) {\n      addListener(element, EVENT_VIEWED, function () {\n        addClass(list, CLASS_TRANSITION);\n      }, {\n        once: true\n      });\n    }\n  },\n  renderList: function renderList() {\n    var index = this.index;\n    var item = this.items[index];\n    if (!item) {\n      return;\n    }\n    var next = item.nextElementSibling;\n    var gutter = parseInt(window.getComputedStyle(next || item).marginLeft, 10);\n    var offsetWidth = item.offsetWidth;\n    var outerWidth = offsetWidth + gutter;\n\n    // Place the active item in the center of the screen\n    setStyle(this.list, assign({\n      width: outerWidth * this.length - gutter\n    }, getTransforms({\n      translateX: (this.viewerData.width - offsetWidth) / 2 - outerWidth * index\n    })));\n  },\n  resetList: function resetList() {\n    var list = this.list;\n    list.innerHTML = '';\n    removeClass(list, CLASS_TRANSITION);\n    setStyle(list, getTransforms({\n      translateX: 0\n    }));\n  },\n  initImage: function initImage(done) {\n    var _this2 = this;\n    var options = this.options,\n      image = this.image,\n      viewerData = this.viewerData;\n    var footerHeight = this.footer.offsetHeight;\n    var viewerWidth = viewerData.width;\n    var viewerHeight = Math.max(viewerData.height - footerHeight, footerHeight);\n    var oldImageData = this.imageData || {};\n    var sizingImage;\n    this.imageInitializing = {\n      abort: function abort() {\n        sizingImage.onload = null;\n      }\n    };\n    sizingImage = getImageNaturalSizes(image, options, function (naturalWidth, naturalHeight) {\n      var aspectRatio = naturalWidth / naturalHeight;\n      var initialCoverage = Math.max(0, Math.min(1, options.initialCoverage));\n      var width = viewerWidth;\n      var height = viewerHeight;\n      _this2.imageInitializing = false;\n      if (viewerHeight * aspectRatio > viewerWidth) {\n        height = viewerWidth / aspectRatio;\n      } else {\n        width = viewerHeight * aspectRatio;\n      }\n      initialCoverage = isNumber(initialCoverage) ? initialCoverage : 0.9;\n      width = Math.min(width * initialCoverage, naturalWidth);\n      height = Math.min(height * initialCoverage, naturalHeight);\n      var left = (viewerWidth - width) / 2;\n      var top = (viewerHeight - height) / 2;\n      var imageData = {\n        left: left,\n        top: top,\n        x: left,\n        y: top,\n        width: width,\n        height: height,\n        oldRatio: 1,\n        ratio: width / naturalWidth,\n        aspectRatio: aspectRatio,\n        naturalWidth: naturalWidth,\n        naturalHeight: naturalHeight\n      };\n      var initialImageData = assign({}, imageData);\n      if (options.rotatable) {\n        imageData.rotate = oldImageData.rotate || 0;\n        initialImageData.rotate = 0;\n      }\n      if (options.scalable) {\n        imageData.scaleX = oldImageData.scaleX || 1;\n        imageData.scaleY = oldImageData.scaleY || 1;\n        initialImageData.scaleX = 1;\n        initialImageData.scaleY = 1;\n      }\n      _this2.imageData = imageData;\n      _this2.initialImageData = initialImageData;\n      if (done) {\n        done();\n      }\n    });\n  },\n  renderImage: function renderImage(done) {\n    var _this3 = this;\n    var image = this.image,\n      imageData = this.imageData;\n    setStyle(image, assign({\n      width: imageData.width,\n      height: imageData.height,\n      // XXX: Not to use translateX/Y to avoid image shaking when zooming\n      marginLeft: imageData.x,\n      marginTop: imageData.y\n    }, getTransforms(imageData)));\n    if (done) {\n      if ((this.viewing || this.moving || this.rotating || this.scaling || this.zooming) && this.options.transition && hasClass(image, CLASS_TRANSITION)) {\n        var onTransitionEnd = function onTransitionEnd() {\n          _this3.imageRendering = false;\n          done();\n        };\n        this.imageRendering = {\n          abort: function abort() {\n            removeListener(image, EVENT_TRANSITION_END, onTransitionEnd);\n          }\n        };\n        addListener(image, EVENT_TRANSITION_END, onTransitionEnd, {\n          once: true\n        });\n      } else {\n        done();\n      }\n    }\n  },\n  resetImage: function resetImage() {\n    var image = this.image;\n    if (image) {\n      if (this.viewing) {\n        this.viewing.abort();\n      }\n      image.parentNode.removeChild(image);\n      this.image = null;\n      this.title.innerHTML = '';\n    }\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var options = this.options,\n      viewer = this.viewer,\n      canvas = this.canvas;\n    var document = this.element.ownerDocument;\n    addListener(viewer, EVENT_CLICK, this.onClick = this.click.bind(this));\n    addListener(viewer, EVENT_DRAG_START, this.onDragStart = this.dragstart.bind(this));\n    addListener(canvas, EVENT_POINTER_DOWN, this.onPointerDown = this.pointerdown.bind(this));\n    addListener(document, EVENT_POINTER_MOVE, this.onPointerMove = this.pointermove.bind(this));\n    addListener(document, EVENT_POINTER_UP, this.onPointerUp = this.pointerup.bind(this));\n    addListener(document, EVENT_KEY_DOWN, this.onKeyDown = this.keydown.bind(this));\n    addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(viewer, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleOnDblclick) {\n      addListener(canvas, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var options = this.options,\n      viewer = this.viewer,\n      canvas = this.canvas;\n    var document = this.element.ownerDocument;\n    removeListener(viewer, EVENT_CLICK, this.onClick);\n    removeListener(viewer, EVENT_DRAG_START, this.onDragStart);\n    removeListener(canvas, EVENT_POINTER_DOWN, this.onPointerDown);\n    removeListener(document, EVENT_POINTER_MOVE, this.onPointerMove);\n    removeListener(document, EVENT_POINTER_UP, this.onPointerUp);\n    removeListener(document, EVENT_KEY_DOWN, this.onKeyDown);\n    removeListener(window, EVENT_RESIZE, this.onResize);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(viewer, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleOnDblclick) {\n      removeListener(canvas, EVENT_DBLCLICK, this.onDblclick);\n    }\n  }\n};\n\nvar handlers = {\n  click: function click(event) {\n    var options = this.options,\n      imageData = this.imageData;\n    var target = event.target;\n    var action = getData(target, DATA_ACTION);\n    if (!action && target.localName === 'img' && target.parentElement.localName === 'li') {\n      target = target.parentElement;\n      action = getData(target, DATA_ACTION);\n    }\n\n    // Cancel the emulated click when the native click event was triggered.\n    if (IS_TOUCH_DEVICE && event.isTrusted && target === this.canvas) {\n      clearTimeout(this.clickCanvasTimeout);\n    }\n    switch (action) {\n      case 'mix':\n        if (this.played) {\n          this.stop();\n        } else if (options.inline) {\n          if (this.fulled) {\n            this.exit();\n          } else {\n            this.full();\n          }\n        } else {\n          this.hide();\n        }\n        break;\n      case 'hide':\n        if (!this.pointerMoved) {\n          this.hide();\n        }\n        break;\n      case 'view':\n        this.view(getData(target, 'index'));\n        break;\n      case 'zoom-in':\n        this.zoom(0.1, true);\n        break;\n      case 'zoom-out':\n        this.zoom(-0.1, true);\n        break;\n      case 'one-to-one':\n        this.toggle();\n        break;\n      case 'reset':\n        this.reset();\n        break;\n      case 'prev':\n        this.prev(options.loop);\n        break;\n      case 'play':\n        this.play(options.fullscreen);\n        break;\n      case 'next':\n        this.next(options.loop);\n        break;\n      case 'rotate-left':\n        this.rotate(-90);\n        break;\n      case 'rotate-right':\n        this.rotate(90);\n        break;\n      case 'flip-horizontal':\n        this.scaleX(-imageData.scaleX || -1);\n        break;\n      case 'flip-vertical':\n        this.scaleY(-imageData.scaleY || -1);\n        break;\n      default:\n        if (this.played) {\n          this.stop();\n        }\n    }\n  },\n  dblclick: function dblclick(event) {\n    event.preventDefault();\n    if (this.viewed && event.target === this.image) {\n      // Cancel the emulated double click when the native dblclick event was triggered.\n      if (IS_TOUCH_DEVICE && event.isTrusted) {\n        clearTimeout(this.doubleClickImageTimeout);\n      }\n\n      // XXX: No pageX/Y properties in custom event, fallback to the original event.\n      this.toggle(event.isTrusted ? event : event.detail && event.detail.originalEvent);\n    }\n  },\n  load: function load() {\n    var _this = this;\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n      this.timeout = false;\n    }\n    var element = this.element,\n      options = this.options,\n      image = this.image,\n      index = this.index,\n      viewerData = this.viewerData;\n    removeClass(image, CLASS_INVISIBLE);\n    if (options.loading) {\n      removeClass(this.canvas, CLASS_LOADING);\n    }\n    image.style.cssText = 'height:0;' + \"margin-left:\".concat(viewerData.width / 2, \"px;\") + \"margin-top:\".concat(viewerData.height / 2, \"px;\") + 'max-width:none!important;' + 'position:relative;' + 'width:0;';\n    this.initImage(function () {\n      toggleClass(image, CLASS_MOVE, options.movable);\n      toggleClass(image, CLASS_TRANSITION, options.transition);\n      _this.renderImage(function () {\n        _this.viewed = true;\n        _this.viewing = false;\n        if (isFunction(options.viewed)) {\n          addListener(element, EVENT_VIEWED, options.viewed, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_VIEWED, {\n          originalImage: _this.images[index],\n          index: index,\n          image: image\n        }, {\n          cancelable: false\n        });\n      });\n    });\n  },\n  loadImage: function loadImage(event) {\n    var image = event.target;\n    var parent = image.parentNode;\n    var parentWidth = parent.offsetWidth || 30;\n    var parentHeight = parent.offsetHeight || 50;\n    var filled = !!getData(image, 'filled');\n    getImageNaturalSizes(image, this.options, function (naturalWidth, naturalHeight) {\n      var aspectRatio = naturalWidth / naturalHeight;\n      var width = parentWidth;\n      var height = parentHeight;\n      if (parentHeight * aspectRatio > parentWidth) {\n        if (filled) {\n          width = parentHeight * aspectRatio;\n        } else {\n          height = parentWidth / aspectRatio;\n        }\n      } else if (filled) {\n        height = parentWidth / aspectRatio;\n      } else {\n        width = parentHeight * aspectRatio;\n      }\n      setStyle(image, assign({\n        width: width,\n        height: height\n      }, getTransforms({\n        translateX: (parentWidth - width) / 2,\n        translateY: (parentHeight - height) / 2\n      })));\n    });\n  },\n  keydown: function keydown(event) {\n    var options = this.options;\n    if (!options.keyboard) {\n      return;\n    }\n    var keyCode = event.keyCode || event.which || event.charCode;\n    switch (keyCode) {\n      // Enter\n      case 13:\n        if (this.viewer.contains(event.target)) {\n          this.click(event);\n        }\n        break;\n    }\n    if (!this.fulled) {\n      return;\n    }\n    switch (keyCode) {\n      // Escape\n      case 27:\n        if (this.played) {\n          this.stop();\n        } else if (options.inline) {\n          if (this.fulled) {\n            this.exit();\n          }\n        } else {\n          this.hide();\n        }\n        break;\n\n      // Space\n      case 32:\n        if (this.played) {\n          this.stop();\n        }\n        break;\n\n      // ArrowLeft\n      case 37:\n        if (this.played && this.playing) {\n          this.playing.prev();\n        } else {\n          this.prev(options.loop);\n        }\n        break;\n\n      // ArrowUp\n      case 38:\n        // Prevent scroll on Firefox\n        event.preventDefault();\n\n        // Zoom in\n        this.zoom(options.zoomRatio, true);\n        break;\n\n      // ArrowRight\n      case 39:\n        if (this.played && this.playing) {\n          this.playing.next();\n        } else {\n          this.next(options.loop);\n        }\n        break;\n\n      // ArrowDown\n      case 40:\n        // Prevent scroll on Firefox\n        event.preventDefault();\n\n        // Zoom out\n        this.zoom(-options.zoomRatio, true);\n        break;\n\n      // Ctrl + 0\n      case 48:\n      // Fall through\n\n      // Ctrl + 1\n      // eslint-disable-next-line no-fallthrough\n      case 49:\n        if (event.ctrlKey) {\n          event.preventDefault();\n          this.toggle();\n        }\n        break;\n    }\n  },\n  dragstart: function dragstart(event) {\n    if (event.target.localName === 'img') {\n      event.preventDefault();\n    }\n  },\n  pointerdown: function pointerdown(event) {\n    var options = this.options,\n      pointers = this.pointers;\n    var buttons = event.buttons,\n      button = event.button;\n    this.pointerMoved = false;\n    if (!this.viewed || this.showing || this.viewing || this.hiding\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n\n    // Prevent default behaviours as page zooming in touch devices.\n    event.preventDefault();\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    var action = options.movable ? ACTION_MOVE : false;\n    if (options.zoomOnTouch && options.zoomable && Object.keys(pointers).length > 1) {\n      action = ACTION_ZOOM;\n    } else if (options.slideOnTouch && (event.pointerType === 'touch' || event.type === 'touchstart') && this.isSwitchable()) {\n      action = ACTION_SWITCH;\n    }\n    if (options.transition && (action === ACTION_MOVE || action === ACTION_ZOOM)) {\n      removeClass(this.image, CLASS_TRANSITION);\n    }\n    this.action = action;\n  },\n  pointermove: function pointermove(event) {\n    var pointers = this.pointers,\n      action = this.action;\n    if (!this.viewed || !action) {\n      return;\n    }\n    event.preventDefault();\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  pointerup: function pointerup(event) {\n    var _this2 = this;\n    var options = this.options,\n      action = this.action,\n      pointers = this.pointers;\n    var pointer;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        pointer = pointers[touch.identifier];\n        delete pointers[touch.identifier];\n      });\n    } else {\n      pointer = pointers[event.pointerId || 0];\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (options.transition && (action === ACTION_MOVE || action === ACTION_ZOOM)) {\n      addClass(this.image, CLASS_TRANSITION);\n    }\n    this.action = false;\n\n    // Emulate click and double click in touch devices to support backdrop and image zooming (#210).\n    if (IS_TOUCH_DEVICE && action !== ACTION_ZOOM && pointer && Date.now() - pointer.timeStamp < 500) {\n      clearTimeout(this.clickCanvasTimeout);\n      clearTimeout(this.doubleClickImageTimeout);\n      if (options.toggleOnDblclick && this.viewed && event.target === this.image) {\n        if (this.imageClicked) {\n          this.imageClicked = false;\n\n          // This timeout will be cleared later when a native dblclick event is triggering\n          this.doubleClickImageTimeout = setTimeout(function () {\n            dispatchEvent(_this2.image, EVENT_DBLCLICK, {\n              originalEvent: event\n            });\n          }, 50);\n        } else {\n          this.imageClicked = true;\n\n          // The default timing of a double click in Windows is 500 ms\n          this.doubleClickImageTimeout = setTimeout(function () {\n            _this2.imageClicked = false;\n          }, 500);\n        }\n      } else {\n        this.imageClicked = false;\n        if (options.backdrop && options.backdrop !== 'static' && event.target === this.canvas) {\n          // This timeout will be cleared later when a native click event is triggering\n          this.clickCanvasTimeout = setTimeout(function () {\n            dispatchEvent(_this2.canvas, EVENT_CLICK, {\n              originalEvent: event\n            });\n          }, 50);\n        }\n      }\n    }\n  },\n  resize: function resize() {\n    var _this3 = this;\n    if (!this.isShown || this.hiding) {\n      return;\n    }\n    if (this.fulled) {\n      this.close();\n      this.initBody();\n      this.open();\n    }\n    this.initContainer();\n    this.initViewer();\n    this.renderViewer();\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this3.renderImage();\n      });\n    }\n    if (this.played) {\n      if (this.options.fullscreen && this.fulled && !(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n        this.stop();\n        return;\n      }\n      forEach(this.player.getElementsByTagName('img'), function (image) {\n        addListener(image, EVENT_LOAD, _this3.loadImage.bind(_this3), {\n          once: true\n        });\n        dispatchEvent(image, EVENT_LOAD);\n      });\n    }\n  },\n  wheel: function wheel(event) {\n    var _this4 = this;\n    if (!this.viewed) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this4.wheeling = false;\n    }, 50);\n    var ratio = Number(this.options.zoomRatio) || 0.1;\n    var delta = 1;\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, true, null, event);\n  }\n};\n\nvar methods = {\n  /** Show the viewer (only available in modal mode)\n   * @param {boolean} [immediate=false] - Indicates if show the viewer immediately or not.\n   * @returns {Viewer} this\n   */\n  show: function show() {\n    var immediate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var element = this.element,\n      options = this.options;\n    if (options.inline || this.showing || this.isShown || this.showing) {\n      return this;\n    }\n    if (!this.ready) {\n      this.build();\n      if (this.ready) {\n        this.show(immediate);\n      }\n      return this;\n    }\n    if (isFunction(options.show)) {\n      addListener(element, EVENT_SHOW, options.show, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_SHOW) === false || !this.ready) {\n      return this;\n    }\n    if (this.hiding) {\n      this.transitioning.abort();\n    }\n    this.showing = true;\n    this.open();\n    var viewer = this.viewer;\n    removeClass(viewer, CLASS_HIDE);\n    viewer.setAttribute('role', 'dialog');\n    viewer.setAttribute('aria-labelledby', this.title.id);\n    viewer.setAttribute('aria-modal', true);\n    viewer.removeAttribute('aria-hidden');\n    if (options.transition && !immediate) {\n      var shown = this.shown.bind(this);\n      this.transitioning = {\n        abort: function abort() {\n          removeListener(viewer, EVENT_TRANSITION_END, shown);\n          removeClass(viewer, CLASS_IN);\n        }\n      };\n      addClass(viewer, CLASS_TRANSITION);\n\n      // Force reflow to enable CSS3 transition\n      viewer.initialOffsetWidth = viewer.offsetWidth;\n      addListener(viewer, EVENT_TRANSITION_END, shown, {\n        once: true\n      });\n      addClass(viewer, CLASS_IN);\n    } else {\n      addClass(viewer, CLASS_IN);\n      this.shown();\n    }\n    return this;\n  },\n  /**\n   * Hide the viewer (only available in modal mode)\n   * @param {boolean} [immediate=false] - Indicates if hide the viewer immediately or not.\n   * @returns {Viewer} this\n   */\n  hide: function hide() {\n    var _this = this;\n    var immediate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var element = this.element,\n      options = this.options;\n    if (options.inline || this.hiding || !(this.isShown || this.showing)) {\n      return this;\n    }\n    if (isFunction(options.hide)) {\n      addListener(element, EVENT_HIDE, options.hide, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_HIDE) === false) {\n      return this;\n    }\n    if (this.showing) {\n      this.transitioning.abort();\n    }\n    this.hiding = true;\n    if (this.played) {\n      this.stop();\n    } else if (this.viewing) {\n      this.viewing.abort();\n    }\n    var viewer = this.viewer,\n      image = this.image;\n    var hideImmediately = function hideImmediately() {\n      removeClass(viewer, CLASS_IN);\n      _this.hidden();\n    };\n    if (options.transition && !immediate) {\n      var _onViewerTransitionEnd = function onViewerTransitionEnd(event) {\n        // Ignore all propagating `transitionend` events (#275).\n        if (event && event.target === viewer) {\n          removeListener(viewer, EVENT_TRANSITION_END, _onViewerTransitionEnd);\n          _this.hidden();\n        }\n      };\n      var onImageTransitionEnd = function onImageTransitionEnd() {\n        // In case of show the viewer by `viewer.show(true)` previously (#407).\n        if (hasClass(viewer, CLASS_TRANSITION)) {\n          addListener(viewer, EVENT_TRANSITION_END, _onViewerTransitionEnd);\n          removeClass(viewer, CLASS_IN);\n        } else {\n          hideImmediately();\n        }\n      };\n      this.transitioning = {\n        abort: function abort() {\n          if (_this.viewed && hasClass(image, CLASS_TRANSITION)) {\n            removeListener(image, EVENT_TRANSITION_END, onImageTransitionEnd);\n          } else if (hasClass(viewer, CLASS_TRANSITION)) {\n            removeListener(viewer, EVENT_TRANSITION_END, _onViewerTransitionEnd);\n          }\n        }\n      };\n\n      // In case of hiding the viewer when holding on the image (#255),\n      // note that the `CLASS_TRANSITION` class will be removed on pointer down.\n      if (this.viewed && hasClass(image, CLASS_TRANSITION)) {\n        addListener(image, EVENT_TRANSITION_END, onImageTransitionEnd, {\n          once: true\n        });\n        this.zoomTo(0, false, null, null, true);\n      } else {\n        onImageTransitionEnd();\n      }\n    } else {\n      hideImmediately();\n    }\n    return this;\n  },\n  /**\n   * View one of the images with image's index\n   * @param {number} index - The index of the image to view.\n   * @returns {Viewer} this\n   */\n  view: function view() {\n    var _this2 = this;\n    var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.initialViewIndex;\n    index = Number(index) || 0;\n    if (this.hiding || this.played || index < 0 || index >= this.length || this.viewed && index === this.index) {\n      return this;\n    }\n    if (!this.isShown) {\n      this.index = index;\n      return this.show();\n    }\n    if (this.viewing) {\n      this.viewing.abort();\n    }\n    var element = this.element,\n      options = this.options,\n      title = this.title,\n      canvas = this.canvas;\n    var item = this.items[index];\n    var img = item.querySelector('img');\n    var url = getData(img, 'originalUrl');\n    var alt = img.getAttribute('alt');\n    var image = document.createElement('img');\n    forEach(options.inheritedAttributes, function (name) {\n      var value = img.getAttribute(name);\n      if (value !== null) {\n        image.setAttribute(name, value);\n      }\n    });\n    image.src = url;\n    image.alt = alt;\n    if (isFunction(options.view)) {\n      addListener(element, EVENT_VIEW, options.view, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_VIEW, {\n      originalImage: this.images[index],\n      index: index,\n      image: image\n    }) === false || !this.isShown || this.hiding || this.played) {\n      return this;\n    }\n    var activeItem = this.items[this.index];\n    if (activeItem) {\n      removeClass(activeItem, CLASS_ACTIVE);\n      activeItem.removeAttribute('aria-selected');\n    }\n    addClass(item, CLASS_ACTIVE);\n    item.setAttribute('aria-selected', true);\n    if (options.focus) {\n      item.focus();\n    }\n    this.image = image;\n    this.viewed = false;\n    this.index = index;\n    this.imageData = {};\n    addClass(image, CLASS_INVISIBLE);\n    if (options.loading) {\n      addClass(canvas, CLASS_LOADING);\n    }\n    canvas.innerHTML = '';\n    canvas.appendChild(image);\n\n    // Center current item\n    this.renderList();\n\n    // Clear title\n    title.innerHTML = '';\n\n    // Generate title after viewed\n    var onViewed = function onViewed() {\n      var imageData = _this2.imageData;\n      var render = Array.isArray(options.title) ? options.title[1] : options.title;\n      title.innerHTML = escapeHTMLEntities(isFunction(render) ? render.call(_this2, image, imageData) : \"\".concat(alt, \" (\").concat(imageData.naturalWidth, \" \\xD7 \").concat(imageData.naturalHeight, \")\"));\n    };\n    var onLoad;\n    var onError;\n    addListener(element, EVENT_VIEWED, onViewed, {\n      once: true\n    });\n    this.viewing = {\n      abort: function abort() {\n        removeListener(element, EVENT_VIEWED, onViewed);\n        if (image.complete) {\n          if (_this2.imageRendering) {\n            _this2.imageRendering.abort();\n          } else if (_this2.imageInitializing) {\n            _this2.imageInitializing.abort();\n          }\n        } else {\n          // Cancel download to save bandwidth.\n          image.src = '';\n          removeListener(image, EVENT_LOAD, onLoad);\n          if (_this2.timeout) {\n            clearTimeout(_this2.timeout);\n          }\n        }\n      }\n    };\n    if (image.complete) {\n      this.load();\n    } else {\n      addListener(image, EVENT_LOAD, onLoad = function onLoad() {\n        removeListener(image, EVENT_ERROR, onError);\n        _this2.load();\n      }, {\n        once: true\n      });\n      addListener(image, EVENT_ERROR, onError = function onError() {\n        removeListener(image, EVENT_LOAD, onLoad);\n        if (_this2.timeout) {\n          clearTimeout(_this2.timeout);\n          _this2.timeout = false;\n        }\n        removeClass(image, CLASS_INVISIBLE);\n        if (options.loading) {\n          removeClass(_this2.canvas, CLASS_LOADING);\n        }\n      }, {\n        once: true\n      });\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n      }\n\n      // Make the image visible if it fails to load within 1s\n      this.timeout = setTimeout(function () {\n        removeClass(image, CLASS_INVISIBLE);\n        _this2.timeout = false;\n      }, 1000);\n    }\n    return this;\n  },\n  /**\n   * View the previous image\n   * @param {boolean} [loop=false] - Indicate if view the last one\n   * when it is the first one at present.\n   * @returns {Viewer} this\n   */\n  prev: function prev() {\n    var loop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var index = this.index - 1;\n    if (index < 0) {\n      index = loop ? this.length - 1 : 0;\n    }\n    this.view(index);\n    return this;\n  },\n  /**\n   * View the next image\n   * @param {boolean} [loop=false] - Indicate if view the first one\n   * when it is the last one at present.\n   * @returns {Viewer} this\n   */\n  next: function next() {\n    var loop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var maxIndex = this.length - 1;\n    var index = this.index + 1;\n    if (index > maxIndex) {\n      index = loop ? 0 : maxIndex;\n    }\n    this.view(index);\n    return this;\n  },\n  /**\n   * Move the image with relative offsets.\n   * @param {number} x - The moving distance in the horizontal direction.\n   * @param {number} [y=x] The moving distance in the vertical direction.\n   * @returns {Viewer} this\n   */\n  move: function move(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var imageData = this.imageData;\n    this.moveTo(isUndefined(x) ? x : imageData.x + Number(x), isUndefined(y) ? y : imageData.y + Number(y));\n    return this;\n  },\n  /**\n   * Move the image to an absolute point.\n   * @param {number} x - The new position in the horizontal direction.\n   * @param {number} [y=x] - The new position in the vertical direction.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  moveTo: function moveTo(x) {\n    var _this3 = this;\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var _originalEvent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    x = Number(x);\n    y = Number(y);\n    if (this.viewed && !this.played && options.movable) {\n      var oldX = imageData.x;\n      var oldY = imageData.y;\n      var changed = false;\n      if (isNumber(x)) {\n        changed = true;\n      } else {\n        x = oldX;\n      }\n      if (isNumber(y)) {\n        changed = true;\n      } else {\n        y = oldY;\n      }\n      if (changed) {\n        if (isFunction(options.move)) {\n          addListener(element, EVENT_MOVE, options.move, {\n            once: true\n          });\n        }\n        if (dispatchEvent(element, EVENT_MOVE, {\n          x: x,\n          y: y,\n          oldX: oldX,\n          oldY: oldY,\n          originalEvent: _originalEvent\n        }) === false) {\n          return this;\n        }\n        imageData.x = x;\n        imageData.y = y;\n        imageData.left = x;\n        imageData.top = y;\n        this.moving = true;\n        this.renderImage(function () {\n          _this3.moving = false;\n          if (isFunction(options.moved)) {\n            addListener(element, EVENT_MOVED, options.moved, {\n              once: true\n            });\n          }\n          dispatchEvent(element, EVENT_MOVED, {\n            x: x,\n            y: y,\n            oldX: oldX,\n            oldY: oldY,\n            originalEvent: _originalEvent\n          }, {\n            cancelable: false\n          });\n        });\n      }\n    }\n    return this;\n  },\n  /**\n   * Rotate the image with a relative degree.\n   * @param {number} degree - The rotate degree.\n   * @returns {Viewer} this\n   */\n  rotate: function rotate(degree) {\n    this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n    return this;\n  },\n  /**\n   * Rotate the image to an absolute degree.\n   * @param {number} degree - The rotate degree.\n   * @returns {Viewer} this\n   */\n  rotateTo: function rotateTo(degree) {\n    var _this4 = this;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    degree = Number(degree);\n    if (isNumber(degree) && this.viewed && !this.played && options.rotatable) {\n      var oldDegree = imageData.rotate;\n      if (isFunction(options.rotate)) {\n        addListener(element, EVENT_ROTATE, options.rotate, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_ROTATE, {\n        degree: degree,\n        oldDegree: oldDegree\n      }) === false) {\n        return this;\n      }\n      imageData.rotate = degree;\n      this.rotating = true;\n      this.renderImage(function () {\n        _this4.rotating = false;\n        if (isFunction(options.rotated)) {\n          addListener(element, EVENT_ROTATED, options.rotated, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_ROTATED, {\n          degree: degree,\n          oldDegree: oldDegree\n        }, {\n          cancelable: false\n        });\n      });\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Viewer} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    this.scale(_scaleX, this.imageData.scaleY);\n    return this;\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Viewer} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    this.scale(this.imageData.scaleX, _scaleY);\n    return this;\n  },\n  /**\n   * Scale the image.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Viewer} this\n   */\n  scale: function scale(scaleX) {\n    var _this5 = this;\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var element = this.element,\n      options = this.options,\n      imageData = this.imageData;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.viewed && !this.played && options.scalable) {\n      var oldScaleX = imageData.scaleX;\n      var oldScaleY = imageData.scaleY;\n      var changed = false;\n      if (isNumber(scaleX)) {\n        changed = true;\n      } else {\n        scaleX = oldScaleX;\n      }\n      if (isNumber(scaleY)) {\n        changed = true;\n      } else {\n        scaleY = oldScaleY;\n      }\n      if (changed) {\n        if (isFunction(options.scale)) {\n          addListener(element, EVENT_SCALE, options.scale, {\n            once: true\n          });\n        }\n        if (dispatchEvent(element, EVENT_SCALE, {\n          scaleX: scaleX,\n          scaleY: scaleY,\n          oldScaleX: oldScaleX,\n          oldScaleY: oldScaleY\n        }) === false) {\n          return this;\n        }\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n        this.scaling = true;\n        this.renderImage(function () {\n          _this5.scaling = false;\n          if (isFunction(options.scaled)) {\n            addListener(element, EVENT_SCALED, options.scaled, {\n              once: true\n            });\n          }\n          dispatchEvent(element, EVENT_SCALED, {\n            scaleX: scaleX,\n            scaleY: scaleY,\n            oldScaleX: oldScaleX,\n            oldScaleY: oldScaleY\n          }, {\n            cancelable: false\n          });\n        });\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the image with a relative ratio.\n   * @param {number} ratio - The target ratio.\n   * @param {boolean} [showTooltip=false] - Indicates whether to show the tooltip.\n   * @param {Object} [pivot] - The pivot point coordinate for zooming.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  zoom: function zoom(ratio) {\n    var showTooltip = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var pivot = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var _originalEvent = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    var imageData = this.imageData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    this.zoomTo(imageData.width * ratio / imageData.naturalWidth, showTooltip, pivot, _originalEvent);\n    return this;\n  },\n  /**\n   * Zoom the image to an absolute ratio.\n   * @param {number} ratio - The target ratio.\n   * @param {boolean} [showTooltip] - Indicates whether to show the tooltip.\n   * @param {Object} [pivot] - The pivot point coordinate for zooming.\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @param {Event} [_zoomable=false] - Indicates if the current zoom is available or not.\n   * @returns {Viewer} this\n   */\n  zoomTo: function zoomTo(ratio) {\n    var _this6 = this;\n    var showTooltip = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var pivot = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var _originalEvent = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    var _zoomable = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    var element = this.element,\n      options = this.options,\n      pointers = this.pointers,\n      imageData = this.imageData;\n    var x = imageData.x,\n      y = imageData.y,\n      width = imageData.width,\n      height = imageData.height,\n      naturalWidth = imageData.naturalWidth,\n      naturalHeight = imageData.naturalHeight;\n    ratio = Math.max(0, ratio);\n    if (isNumber(ratio) && this.viewed && !this.played && (_zoomable || options.zoomable)) {\n      if (!_zoomable) {\n        var minZoomRatio = Math.max(0.01, options.minZoomRatio);\n        var maxZoomRatio = Math.min(100, options.maxZoomRatio);\n        ratio = Math.min(Math.max(ratio, minZoomRatio), maxZoomRatio);\n      }\n      if (_originalEvent) {\n        switch (_originalEvent.type) {\n          case 'wheel':\n            if (options.zoomRatio >= 0.055 && ratio > 0.95 && ratio < 1.05) {\n              ratio = 1;\n            }\n            break;\n          case 'pointermove':\n          case 'touchmove':\n          case 'mousemove':\n            if (ratio > 0.99 && ratio < 1.01) {\n              ratio = 1;\n            }\n            break;\n        }\n      }\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      var offsetWidth = newWidth - width;\n      var offsetHeight = newHeight - height;\n      var oldRatio = imageData.ratio;\n      if (isFunction(options.zoom)) {\n        addListener(element, EVENT_ZOOM, options.zoom, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: oldRatio,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      this.zooming = true;\n      if (_originalEvent) {\n        var offset = getOffset(this.viewer);\n        var center = pointers && Object.keys(pointers).length > 0 ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        imageData.x -= offsetWidth * ((center.pageX - offset.left - x) / width);\n        imageData.y -= offsetHeight * ((center.pageY - offset.top - y) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        imageData.x -= offsetWidth * ((pivot.x - x) / width);\n        imageData.y -= offsetHeight * ((pivot.y - y) / height);\n      } else {\n        // Zoom from the center of the image\n        imageData.x -= offsetWidth / 2;\n        imageData.y -= offsetHeight / 2;\n      }\n      imageData.left = imageData.x;\n      imageData.top = imageData.y;\n      imageData.width = newWidth;\n      imageData.height = newHeight;\n      imageData.oldRatio = oldRatio;\n      imageData.ratio = ratio;\n      this.renderImage(function () {\n        _this6.zooming = false;\n        if (isFunction(options.zoomed)) {\n          addListener(element, EVENT_ZOOMED, options.zoomed, {\n            once: true\n          });\n        }\n        dispatchEvent(element, EVENT_ZOOMED, {\n          ratio: ratio,\n          oldRatio: oldRatio,\n          originalEvent: _originalEvent\n        }, {\n          cancelable: false\n        });\n      });\n      if (showTooltip) {\n        this.tooltip();\n      }\n    }\n    return this;\n  },\n  /**\n   * Play the images\n   * @param {boolean|FullscreenOptions} [fullscreen=false] - Indicate if request fullscreen or not.\n   * @returns {Viewer} this\n   */\n  play: function play() {\n    var _this7 = this;\n    var fullscreen = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!this.isShown || this.played) {\n      return this;\n    }\n    var element = this.element,\n      options = this.options;\n    if (isFunction(options.play)) {\n      addListener(element, EVENT_PLAY, options.play, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_PLAY) === false) {\n      return this;\n    }\n    var player = this.player;\n    var onLoad = this.loadImage.bind(this);\n    var list = [];\n    var total = 0;\n    var index = 0;\n    this.played = true;\n    this.onLoadWhenPlay = onLoad;\n    if (fullscreen) {\n      this.requestFullscreen(fullscreen);\n    }\n    addClass(player, CLASS_SHOW);\n    forEach(this.items, function (item, i) {\n      var img = item.querySelector('img');\n      var image = document.createElement('img');\n      image.src = getData(img, 'originalUrl');\n      image.alt = img.getAttribute('alt');\n      image.referrerPolicy = img.referrerPolicy;\n      total += 1;\n      addClass(image, CLASS_FADE);\n      toggleClass(image, CLASS_TRANSITION, options.transition);\n      if (hasClass(item, CLASS_ACTIVE)) {\n        addClass(image, CLASS_IN);\n        index = i;\n      }\n      list.push(image);\n      addListener(image, EVENT_LOAD, onLoad, {\n        once: true\n      });\n      player.appendChild(image);\n    });\n    if (isNumber(options.interval) && options.interval > 0) {\n      var _prev = function prev() {\n        clearTimeout(_this7.playing.timeout);\n        removeClass(list[index], CLASS_IN);\n        index -= 1;\n        index = index >= 0 ? index : total - 1;\n        addClass(list[index], CLASS_IN);\n        _this7.playing.timeout = setTimeout(_prev, options.interval);\n      };\n      var _next = function next() {\n        clearTimeout(_this7.playing.timeout);\n        removeClass(list[index], CLASS_IN);\n        index += 1;\n        index = index < total ? index : 0;\n        addClass(list[index], CLASS_IN);\n        _this7.playing.timeout = setTimeout(_next, options.interval);\n      };\n      if (total > 1) {\n        this.playing = {\n          prev: _prev,\n          next: _next,\n          timeout: setTimeout(_next, options.interval)\n        };\n      }\n    }\n    return this;\n  },\n  // Stop play\n  stop: function stop() {\n    var _this8 = this;\n    if (!this.played) {\n      return this;\n    }\n    var element = this.element,\n      options = this.options;\n    if (isFunction(options.stop)) {\n      addListener(element, EVENT_STOP, options.stop, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_STOP) === false) {\n      return this;\n    }\n    var player = this.player;\n    clearTimeout(this.playing.timeout);\n    this.playing = false;\n    this.played = false;\n    forEach(player.getElementsByTagName('img'), function (image) {\n      removeListener(image, EVENT_LOAD, _this8.onLoadWhenPlay);\n    });\n    removeClass(player, CLASS_SHOW);\n    player.innerHTML = '';\n    this.exitFullscreen();\n    return this;\n  },\n  // Enter modal mode (only available in inline mode)\n  full: function full() {\n    var _this9 = this;\n    var options = this.options,\n      viewer = this.viewer,\n      image = this.image,\n      list = this.list;\n    if (!this.isShown || this.played || this.fulled || !options.inline) {\n      return this;\n    }\n    this.fulled = true;\n    this.open();\n    addClass(this.button, CLASS_FULLSCREEN_EXIT);\n    if (options.transition) {\n      removeClass(list, CLASS_TRANSITION);\n      if (this.viewed) {\n        removeClass(image, CLASS_TRANSITION);\n      }\n    }\n    addClass(viewer, CLASS_FIXED);\n    viewer.setAttribute('role', 'dialog');\n    viewer.setAttribute('aria-labelledby', this.title.id);\n    viewer.setAttribute('aria-modal', true);\n    viewer.removeAttribute('style');\n    setStyle(viewer, {\n      zIndex: options.zIndex\n    });\n    if (options.focus) {\n      this.enforceFocus();\n    }\n    this.initContainer();\n    this.viewerData = assign({}, this.containerData);\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this9.renderImage(function () {\n          if (options.transition) {\n            setTimeout(function () {\n              addClass(image, CLASS_TRANSITION);\n              addClass(list, CLASS_TRANSITION);\n            }, 0);\n          }\n        });\n      });\n    }\n    return this;\n  },\n  // Exit modal mode (only available in inline mode)\n  exit: function exit() {\n    var _this10 = this;\n    var options = this.options,\n      viewer = this.viewer,\n      image = this.image,\n      list = this.list;\n    if (!this.isShown || this.played || !this.fulled || !options.inline) {\n      return this;\n    }\n    this.fulled = false;\n    this.close();\n    removeClass(this.button, CLASS_FULLSCREEN_EXIT);\n    if (options.transition) {\n      removeClass(list, CLASS_TRANSITION);\n      if (this.viewed) {\n        removeClass(image, CLASS_TRANSITION);\n      }\n    }\n    if (options.focus) {\n      this.clearEnforceFocus();\n    }\n    viewer.removeAttribute('role');\n    viewer.removeAttribute('aria-labelledby');\n    viewer.removeAttribute('aria-modal');\n    removeClass(viewer, CLASS_FIXED);\n    setStyle(viewer, {\n      zIndex: options.zIndexInline\n    });\n    this.viewerData = assign({}, this.parentData);\n    this.renderViewer();\n    this.renderList();\n    if (this.viewed) {\n      this.initImage(function () {\n        _this10.renderImage(function () {\n          if (options.transition) {\n            setTimeout(function () {\n              addClass(image, CLASS_TRANSITION);\n              addClass(list, CLASS_TRANSITION);\n            }, 0);\n          }\n        });\n      });\n    }\n    return this;\n  },\n  // Show the current ratio of the image with percentage\n  tooltip: function tooltip() {\n    var _this11 = this;\n    var options = this.options,\n      tooltipBox = this.tooltipBox,\n      imageData = this.imageData;\n    if (!this.viewed || this.played || !options.tooltip) {\n      return this;\n    }\n    tooltipBox.textContent = \"\".concat(Math.round(imageData.ratio * 100), \"%\");\n    if (!this.tooltipping) {\n      if (options.transition) {\n        if (this.fading) {\n          dispatchEvent(tooltipBox, EVENT_TRANSITION_END);\n        }\n        addClass(tooltipBox, CLASS_SHOW);\n        addClass(tooltipBox, CLASS_FADE);\n        addClass(tooltipBox, CLASS_TRANSITION);\n        tooltipBox.removeAttribute('aria-hidden');\n\n        // Force reflow to enable CSS3 transition\n        tooltipBox.initialOffsetWidth = tooltipBox.offsetWidth;\n        addClass(tooltipBox, CLASS_IN);\n      } else {\n        addClass(tooltipBox, CLASS_SHOW);\n        tooltipBox.removeAttribute('aria-hidden');\n      }\n    } else {\n      clearTimeout(this.tooltipping);\n    }\n    this.tooltipping = setTimeout(function () {\n      if (options.transition) {\n        addListener(tooltipBox, EVENT_TRANSITION_END, function () {\n          removeClass(tooltipBox, CLASS_SHOW);\n          removeClass(tooltipBox, CLASS_FADE);\n          removeClass(tooltipBox, CLASS_TRANSITION);\n          tooltipBox.setAttribute('aria-hidden', true);\n          _this11.fading = false;\n        }, {\n          once: true\n        });\n        removeClass(tooltipBox, CLASS_IN);\n        _this11.fading = true;\n      } else {\n        removeClass(tooltipBox, CLASS_SHOW);\n        tooltipBox.setAttribute('aria-hidden', true);\n      }\n      _this11.tooltipping = false;\n    }, 1000);\n    return this;\n  },\n  /**\n   * Toggle the image size between its current size and natural size\n   * @param {Event} [_originalEvent=null] - The original event if any.\n   * @returns {Viewer} this\n   */\n  toggle: function toggle() {\n    var _originalEvent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    if (this.imageData.ratio === 1) {\n      this.zoomTo(this.imageData.oldRatio, true, null, _originalEvent);\n    } else {\n      this.zoomTo(1, true, null, _originalEvent);\n    }\n    return this;\n  },\n  // Reset the image to its initial state\n  reset: function reset() {\n    if (this.viewed && !this.played) {\n      this.imageData = assign({}, this.initialImageData);\n      this.renderImage();\n    }\n    return this;\n  },\n  // Update viewer when images changed\n  update: function update() {\n    var _this12 = this;\n    var element = this.element,\n      options = this.options,\n      isImg = this.isImg;\n\n    // Destroy viewer if the target image was deleted\n    if (isImg && !element.parentNode) {\n      return this.destroy();\n    }\n    var images = [];\n    forEach(isImg ? [element] : element.querySelectorAll('img'), function (image) {\n      if (isFunction(options.filter)) {\n        if (options.filter.call(_this12, image)) {\n          images.push(image);\n        }\n      } else if (_this12.getImageURL(image)) {\n        images.push(image);\n      }\n    });\n    if (!images.length) {\n      return this;\n    }\n    this.images = images;\n    this.length = images.length;\n    if (this.ready) {\n      var changedIndexes = [];\n      forEach(this.items, function (item, i) {\n        var img = item.querySelector('img');\n        var image = images[i];\n        if (image && img) {\n          if (image.src !== img.src\n\n          // Title changed (#408)\n          || image.alt !== img.alt) {\n            changedIndexes.push(i);\n          }\n        } else {\n          changedIndexes.push(i);\n        }\n      });\n      setStyle(this.list, {\n        width: 'auto'\n      });\n      this.initList();\n      if (this.isShown) {\n        if (this.length) {\n          if (this.viewed) {\n            var changedIndex = changedIndexes.indexOf(this.index);\n            if (changedIndex >= 0) {\n              this.viewed = false;\n              this.view(Math.max(Math.min(this.index - changedIndex, this.length - 1), 0));\n            } else {\n              var activeItem = this.items[this.index];\n\n              // Reactivate the current viewing item after reset the list.\n              addClass(activeItem, CLASS_ACTIVE);\n              activeItem.setAttribute('aria-selected', true);\n            }\n          }\n        } else {\n          this.image = null;\n          this.viewed = false;\n          this.index = 0;\n          this.imageData = {};\n          this.canvas.innerHTML = '';\n          this.title.innerHTML = '';\n        }\n      }\n    } else {\n      this.build();\n    }\n    return this;\n  },\n  // Destroy the viewer\n  destroy: function destroy() {\n    var element = this.element,\n      options = this.options;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    this.destroyed = true;\n    if (this.ready) {\n      if (this.played) {\n        this.stop();\n      }\n      if (options.inline) {\n        if (this.fulled) {\n          this.exit();\n        }\n        this.unbind();\n      } else if (this.isShown) {\n        if (this.viewing) {\n          if (this.imageRendering) {\n            this.imageRendering.abort();\n          } else if (this.imageInitializing) {\n            this.imageInitializing.abort();\n          }\n        }\n        if (this.hiding) {\n          this.transitioning.abort();\n        }\n        this.hidden();\n      } else if (this.showing) {\n        this.transitioning.abort();\n        this.hidden();\n      }\n      this.ready = false;\n      this.viewer.parentNode.removeChild(this.viewer);\n    } else if (options.inline) {\n      if (this.delaying) {\n        this.delaying.abort();\n      } else if (this.initializing) {\n        this.initializing.abort();\n      }\n    }\n    if (!options.inline) {\n      removeListener(element, EVENT_CLICK, this.onStart);\n    }\n    element[NAMESPACE] = undefined;\n    return this;\n  }\n};\n\nvar others = {\n  getImageURL: function getImageURL(image) {\n    var url = this.options.url;\n    if (isString(url)) {\n      url = image.getAttribute(url);\n    } else if (isFunction(url)) {\n      url = url.call(this, image);\n    } else {\n      url = '';\n    }\n    return url;\n  },\n  enforceFocus: function enforceFocus() {\n    var _this = this;\n    this.clearEnforceFocus();\n    addListener(document, EVENT_FOCUSIN, this.onFocusin = function (event) {\n      var viewer = _this.viewer;\n      var target = event.target;\n      if (target === document || target === viewer || viewer.contains(target)) {\n        return;\n      }\n      while (target) {\n        // Avoid conflicts with other modals (#474, #540)\n        if (target.getAttribute('tabindex') !== null || target.getAttribute('aria-modal') === 'true') {\n          return;\n        }\n        target = target.parentElement;\n      }\n      viewer.focus();\n    });\n  },\n  clearEnforceFocus: function clearEnforceFocus() {\n    if (this.onFocusin) {\n      removeListener(document, EVENT_FOCUSIN, this.onFocusin);\n      this.onFocusin = null;\n    }\n  },\n  open: function open() {\n    var body = this.body;\n    addClass(body, CLASS_OPEN);\n    if (this.scrollbarWidth > 0) {\n      body.style.paddingRight = \"\".concat(this.scrollbarWidth + (parseFloat(this.initialBodyComputedPaddingRight) || 0), \"px\");\n    }\n  },\n  close: function close() {\n    var body = this.body;\n    removeClass(body, CLASS_OPEN);\n    if (this.scrollbarWidth > 0) {\n      body.style.paddingRight = this.initialBodyPaddingRight;\n    }\n  },\n  shown: function shown() {\n    var element = this.element,\n      options = this.options,\n      viewer = this.viewer;\n    this.fulled = true;\n    this.isShown = true;\n    this.render();\n    this.bind();\n    this.showing = false;\n    if (options.focus) {\n      viewer.focus();\n      this.enforceFocus();\n    }\n    if (isFunction(options.shown)) {\n      addListener(element, EVENT_SHOWN, options.shown, {\n        once: true\n      });\n    }\n    if (dispatchEvent(element, EVENT_SHOWN) === false) {\n      return;\n    }\n    if (this.ready && this.isShown && !this.hiding) {\n      this.view(this.index);\n    }\n  },\n  hidden: function hidden() {\n    var element = this.element,\n      options = this.options,\n      viewer = this.viewer;\n    if (options.fucus) {\n      this.clearEnforceFocus();\n    }\n    this.close();\n    this.unbind();\n    addClass(viewer, CLASS_HIDE);\n    viewer.removeAttribute('role');\n    viewer.removeAttribute('aria-labelledby');\n    viewer.removeAttribute('aria-modal');\n    viewer.setAttribute('aria-hidden', true);\n    this.resetList();\n    this.resetImage();\n    this.fulled = false;\n    this.viewed = false;\n    this.isShown = false;\n    this.hiding = false;\n    if (!this.destroyed) {\n      if (isFunction(options.hidden)) {\n        addListener(element, EVENT_HIDDEN, options.hidden, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_HIDDEN, null, {\n        cancelable: false\n      });\n    }\n  },\n  requestFullscreen: function requestFullscreen(options) {\n    var document = this.element.ownerDocument;\n    if (this.fulled && !(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n      var documentElement = document.documentElement;\n\n      // Element.requestFullscreen()\n      if (documentElement.requestFullscreen) {\n        // Avoid TypeError when convert `options` to dictionary\n        if (isPlainObject(options)) {\n          documentElement.requestFullscreen(options);\n        } else {\n          documentElement.requestFullscreen();\n        }\n      } else if (documentElement.webkitRequestFullscreen) {\n        documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);\n      } else if (documentElement.mozRequestFullScreen) {\n        documentElement.mozRequestFullScreen();\n      } else if (documentElement.msRequestFullscreen) {\n        documentElement.msRequestFullscreen();\n      }\n    }\n  },\n  exitFullscreen: function exitFullscreen() {\n    var document = this.element.ownerDocument;\n    if (this.fulled && (document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement)) {\n      // Document.exitFullscreen()\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen();\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen();\n      }\n    }\n  },\n  change: function change(event) {\n    var options = this.options,\n      pointers = this.pointers;\n    var pointer = pointers[Object.keys(pointers)[0]];\n\n    // In the case of the `pointers` object is empty (#421)\n    if (!pointer) {\n      return;\n    }\n    var offsetX = pointer.endX - pointer.startX;\n    var offsetY = pointer.endY - pointer.startY;\n    switch (this.action) {\n      // Move the current image\n      case ACTION_MOVE:\n        if (offsetX !== 0 || offsetY !== 0) {\n          this.pointerMoved = true;\n          this.move(offsetX, offsetY, event);\n        }\n        break;\n\n      // Zoom the current image\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), false, null, event);\n        break;\n      case ACTION_SWITCH:\n        {\n          this.action = 'switched';\n          var absoluteOffsetX = Math.abs(offsetX);\n          if (absoluteOffsetX > 1 && absoluteOffsetX > Math.abs(offsetY)) {\n            // Empty `pointers` as `touchend` event will not be fired after swiped in iOS browsers.\n            this.pointers = {};\n            if (offsetX > 1) {\n              this.prev(options.loop);\n            } else if (offsetX < -1) {\n              this.next(options.loop);\n            }\n          }\n          break;\n        }\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  },\n  isSwitchable: function isSwitchable() {\n    var imageData = this.imageData,\n      viewerData = this.viewerData;\n    return this.length > 1 && imageData.x >= 0 && imageData.y >= 0 && imageData.width <= viewerData.width && imageData.height <= viewerData.height;\n  }\n};\n\nvar AnotherViewer = WINDOW.Viewer;\nvar getUniqueID = function (id) {\n  return function () {\n    id += 1;\n    return id;\n  };\n}(-1);\nvar Viewer = /*#__PURE__*/function () {\n  /**\n   * Create a new Viewer.\n   * @param {Element} element - The target element for viewing.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Viewer(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Viewer);\n    if (!element || element.nodeType !== 1) {\n      throw new Error('The first argument is required and must be an element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.action = false;\n    this.fading = false;\n    this.fulled = false;\n    this.hiding = false;\n    this.imageClicked = false;\n    this.imageData = {};\n    this.index = this.options.initialViewIndex;\n    this.isImg = false;\n    this.isShown = false;\n    this.length = 0;\n    this.moving = false;\n    this.played = false;\n    this.playing = false;\n    this.pointers = {};\n    this.ready = false;\n    this.rotating = false;\n    this.scaling = false;\n    this.showing = false;\n    this.timeout = false;\n    this.tooltipping = false;\n    this.viewed = false;\n    this.viewing = false;\n    this.wheeling = false;\n    this.zooming = false;\n    this.pointerMoved = false;\n    this.id = getUniqueID();\n    this.init();\n  }\n  return _createClass(Viewer, [{\n    key: \"init\",\n    value: function init() {\n      var _this = this;\n      var element = this.element,\n        options = this.options;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n\n      // The `focus` option requires the `keyboard` option set to `true`.\n      if (options.focus && !options.keyboard) {\n        options.focus = false;\n      }\n      var isImg = element.localName === 'img';\n      var images = [];\n      forEach(isImg ? [element] : element.querySelectorAll('img'), function (image) {\n        if (isFunction(options.filter)) {\n          if (options.filter.call(_this, image)) {\n            images.push(image);\n          }\n        } else if (_this.getImageURL(image)) {\n          images.push(image);\n        }\n      });\n      this.isImg = isImg;\n      this.length = images.length;\n      this.images = images;\n      this.initBody();\n\n      // Override `transition` option if it is not supported\n      if (isUndefined(document.createElement(NAMESPACE).style.transition)) {\n        options.transition = false;\n      }\n      if (options.inline) {\n        var count = 0;\n        var progress = function progress() {\n          count += 1;\n          if (count === _this.length) {\n            var timeout;\n            _this.initializing = false;\n            _this.delaying = {\n              abort: function abort() {\n                clearTimeout(timeout);\n              }\n            };\n\n            // build asynchronously to keep `this.viewer` is accessible in `ready` event handler.\n            timeout = setTimeout(function () {\n              _this.delaying = false;\n              _this.build();\n            }, 0);\n          }\n        };\n        this.initializing = {\n          abort: function abort() {\n            forEach(images, function (image) {\n              if (!image.complete) {\n                removeListener(image, EVENT_LOAD, progress);\n                removeListener(image, EVENT_ERROR, progress);\n              }\n            });\n          }\n        };\n        forEach(images, function (image) {\n          if (image.complete) {\n            progress();\n          } else {\n            var onLoad;\n            var onError;\n            addListener(image, EVENT_LOAD, onLoad = function onLoad() {\n              removeListener(image, EVENT_ERROR, onError);\n              progress();\n            }, {\n              once: true\n            });\n            addListener(image, EVENT_ERROR, onError = function onError() {\n              removeListener(image, EVENT_LOAD, onLoad);\n              progress();\n            }, {\n              once: true\n            });\n          }\n        });\n      } else {\n        addListener(element, EVENT_CLICK, this.onStart = function (_ref) {\n          var target = _ref.target;\n          if (target.localName === 'img' && (!isFunction(options.filter) || options.filter.call(_this, target))) {\n            _this.view(_this.images.indexOf(target));\n          }\n        });\n      }\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options;\n      var parent = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var viewer = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var title = viewer.querySelector(\".\".concat(NAMESPACE, \"-title\"));\n      var toolbar = viewer.querySelector(\".\".concat(NAMESPACE, \"-toolbar\"));\n      var navbar = viewer.querySelector(\".\".concat(NAMESPACE, \"-navbar\"));\n      var button = viewer.querySelector(\".\".concat(NAMESPACE, \"-button\"));\n      var canvas = viewer.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      this.parent = parent;\n      this.viewer = viewer;\n      this.title = title;\n      this.toolbar = toolbar;\n      this.navbar = navbar;\n      this.button = button;\n      this.canvas = canvas;\n      this.footer = viewer.querySelector(\".\".concat(NAMESPACE, \"-footer\"));\n      this.tooltipBox = viewer.querySelector(\".\".concat(NAMESPACE, \"-tooltip\"));\n      this.player = viewer.querySelector(\".\".concat(NAMESPACE, \"-player\"));\n      this.list = viewer.querySelector(\".\".concat(NAMESPACE, \"-list\"));\n      viewer.id = \"\".concat(NAMESPACE).concat(this.id);\n      title.id = \"\".concat(NAMESPACE, \"Title\").concat(this.id);\n      addClass(title, !options.title ? CLASS_HIDE : getResponsiveClass(Array.isArray(options.title) ? options.title[0] : options.title));\n      addClass(navbar, !options.navbar ? CLASS_HIDE : getResponsiveClass(options.navbar));\n      toggleClass(button, CLASS_HIDE, !options.button);\n      if (options.keyboard) {\n        button.setAttribute('tabindex', 0);\n      }\n      if (options.backdrop) {\n        addClass(viewer, \"\".concat(NAMESPACE, \"-backdrop\"));\n        if (!options.inline && options.backdrop !== 'static') {\n          setData(canvas, DATA_ACTION, 'hide');\n        }\n      }\n      if (isString(options.className) && options.className) {\n        // In case there are multiple class names\n        options.className.split(REGEXP_SPACES).forEach(function (className) {\n          addClass(viewer, className);\n        });\n      }\n      if (options.toolbar) {\n        var list = document.createElement('ul');\n        var custom = isPlainObject(options.toolbar);\n        var zoomButtons = BUTTONS.slice(0, 3);\n        var rotateButtons = BUTTONS.slice(7, 9);\n        var scaleButtons = BUTTONS.slice(9);\n        if (!custom) {\n          addClass(toolbar, getResponsiveClass(options.toolbar));\n        }\n        forEach(custom ? options.toolbar : BUTTONS, function (value, index) {\n          var deep = custom && isPlainObject(value);\n          var name = custom ? hyphenate(index) : value;\n          var show = deep && !isUndefined(value.show) ? value.show : value;\n          if (!show || !options.zoomable && zoomButtons.indexOf(name) !== -1 || !options.rotatable && rotateButtons.indexOf(name) !== -1 || !options.scalable && scaleButtons.indexOf(name) !== -1) {\n            return;\n          }\n          var size = deep && !isUndefined(value.size) ? value.size : value;\n          var click = deep && !isUndefined(value.click) ? value.click : value;\n          var item = document.createElement('li');\n          if (options.keyboard) {\n            item.setAttribute('tabindex', 0);\n          }\n          item.setAttribute('role', 'button');\n          addClass(item, \"\".concat(NAMESPACE, \"-\").concat(name));\n          if (!isFunction(click)) {\n            setData(item, DATA_ACTION, name);\n          }\n          if (isNumber(show)) {\n            addClass(item, getResponsiveClass(show));\n          }\n          if (['small', 'large'].indexOf(size) !== -1) {\n            addClass(item, \"\".concat(NAMESPACE, \"-\").concat(size));\n          } else if (name === 'play') {\n            addClass(item, \"\".concat(NAMESPACE, \"-large\"));\n          }\n          if (isFunction(click)) {\n            addListener(item, EVENT_CLICK, click);\n          }\n          list.appendChild(item);\n        });\n        toolbar.appendChild(list);\n      } else {\n        addClass(toolbar, CLASS_HIDE);\n      }\n      if (!options.rotatable) {\n        var rotates = toolbar.querySelectorAll('li[class*=\"rotate\"]');\n        addClass(rotates, CLASS_INVISIBLE);\n        forEach(rotates, function (rotate) {\n          toolbar.appendChild(rotate);\n        });\n      }\n      if (options.inline) {\n        addClass(button, CLASS_FULLSCREEN);\n        setStyle(viewer, {\n          zIndex: options.zIndexInline\n        });\n        if (window.getComputedStyle(parent).position === 'static') {\n          setStyle(parent, {\n            position: 'relative'\n          });\n        }\n        parent.insertBefore(viewer, element.nextSibling);\n      } else {\n        addClass(button, CLASS_CLOSE);\n        addClass(viewer, CLASS_FIXED);\n        addClass(viewer, CLASS_FADE);\n        addClass(viewer, CLASS_HIDE);\n        setStyle(viewer, {\n          zIndex: options.zIndex\n        });\n        var container = options.container;\n        if (isString(container)) {\n          container = element.ownerDocument.querySelector(container);\n        }\n        if (!container) {\n          container = this.body;\n        }\n        container.appendChild(viewer);\n      }\n      if (options.inline) {\n        this.render();\n        this.bind();\n        this.isShown = true;\n      }\n      this.ready = true;\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      if (dispatchEvent(element, EVENT_READY) === false) {\n        this.ready = false;\n        return;\n      }\n      if (this.ready && options.inline) {\n        this.view(this.index);\n      }\n    }\n\n    /**\n     * Get the no conflict viewer class.\n     * @returns {Viewer} The viewer class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Viewer = AnotherViewer;\n      return Viewer;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n}();\nassign(Viewer.prototype, render, events, handlers, methods, others);\n\nexport { Viewer as default };\n", "import d from \"./node_modules/viewerjs/dist/viewer.esm.js\";export default d;"], "mappings": ";;;AAUA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa;AAAI,UAAM,IAAI,UAAU,mCAAmC;AAChF;AACA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE;AACV,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,eAAe,EAAE,GAAG,GAAG,CAAC;AAAA,EAC9I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;AACA,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG;AAAA,IAChE,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAI,EAAE,KAAK,GAAG;AACjB;AACA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUA,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAgB,GAAGA,IAAG,EAAEA,GAAE;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,YAAY,OAAO,KAAK,CAAC;AAAG,WAAO;AACvC,MAAI,IAAI,EAAE,OAAO;AACjB,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,OAAO;AAAG,aAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AACA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;AAEA,IAAI,WAAW;AAAA,EAMb,UAAU;AAAA,EAKV,QAAQ;AAAA,EAKR,QAAQ;AAAA,EAKR,OAAO;AAAA,EAKP,SAAS;AAAA,EAKT,WAAW;AAAA,EAKX,WAAW;AAAA,EAKX,QAAQ;AAAA,EAMR,YAAY;AAAA,EAKZ,qBAAqB,CAAC,eAAe,YAAY,SAAS,WAAW,kBAAkB,SAAS,UAAU,QAAQ;AAAA,EAKlH,iBAAiB;AAAA,EAKjB,kBAAkB;AAAA,EAKlB,QAAQ;AAAA,EAKR,UAAU;AAAA,EAKV,UAAU;AAAA,EAKV,OAAO;AAAA,EAKP,SAAS;AAAA,EAKT,MAAM;AAAA,EAKN,UAAU;AAAA,EAKV,WAAW;AAAA,EAKX,SAAS;AAAA,EAKT,WAAW;AAAA,EAKX,UAAU;AAAA,EAKV,UAAU;AAAA,EAKV,aAAa;AAAA,EAKb,aAAa;AAAA,EAKb,cAAc;AAAA,EAMd,kBAAkB;AAAA,EAKlB,SAAS;AAAA,EAKT,YAAY;AAAA,EAKZ,QAAQ;AAAA,EAKR,cAAc;AAAA,EAKd,WAAW;AAAA,EAKX,cAAc;AAAA,EAKd,cAAc;AAAA,EAKd,KAAK;AAAA,EAKL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAI,WAAW;AAEf,IAAI,aAAa,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC7E,IAAI,SAAS,aAAa,SAAS,CAAC;AACpC,IAAI,kBAAkB,cAAc,OAAO,SAAS,kBAAkB,kBAAkB,OAAO,SAAS,kBAAkB;AAC1H,IAAI,oBAAoB,aAAa,kBAAkB,SAAS;AAChE,IAAI,YAAY;AAGhB,IAAI,cAAc;AAClB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAGlB,IAAI,eAAe,GAAG,OAAO,WAAW,SAAS;AACjD,IAAI,cAAc,GAAG,OAAO,WAAW,QAAQ;AAC/C,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,cAAc,GAAG,OAAO,WAAW,QAAQ;AAC/C,IAAI,mBAAmB,GAAG,OAAO,WAAW,aAAa;AACzD,IAAI,wBAAwB,GAAG,OAAO,WAAW,kBAAkB;AACnE,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,qBAAqB,GAAG,OAAO,WAAW,eAAe;AAC7D,IAAI,qBAAqB,GAAG,OAAO,WAAW,eAAe;AAC7D,IAAI,qBAAqB,GAAG,OAAO,WAAW,eAAe;AAC7D,IAAI,WAAW,GAAG,OAAO,WAAW,KAAK;AACzC,IAAI,kBAAkB,GAAG,OAAO,WAAW,YAAY;AACvD,IAAI,gBAAgB,GAAG,OAAO,WAAW,UAAU;AACnD,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,mBAAmB,GAAG,OAAO,WAAW,aAAa;AAGzD,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,kBAAkB,kBAAkB,yBAAyB;AACjE,IAAI,mBAAmB,kBAAkB,cAAc;AACvD,IAAI,oBAAoB,kBAAkB,eAAe;AACzD,IAAI,qBAAqB,oBAAoB,gBAAgB;AAC7D,IAAI,qBAAqB,oBAAoB,gBAAgB;AAC7D,IAAI,mBAAmB,oBAAoB,4BAA4B;AACvE,IAAI,eAAe;AACnB,IAAI,uBAAuB;AAC3B,IAAI,cAAc;AAGlB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,aAAa;AAGjB,IAAI,cAAc,GAAG,OAAO,WAAW,QAAQ;AAG/C,IAAI,gBAAgB;AAGpB,IAAI,UAAU,CAAC,WAAW,YAAY,cAAc,SAAS,QAAQ,QAAQ,QAAQ,eAAe,gBAAgB,mBAAmB,eAAe;AAOtJ,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAKA,IAAI,QAAQ,OAAO,SAAS,OAAO;AAOnC,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK;AAClD;AAOA,SAAS,YAAY,OAAO;AAC1B,SAAO,OAAO,UAAU;AAC1B;AAOA,SAAS,SAAS,OAAO;AACvB,SAAO,QAAQ,KAAK,MAAM,YAAY,UAAU;AAClD;AACA,IAAI,iBAAiB,OAAO,UAAU;AAOtC,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,eAAe,MAAM;AACzB,QAAI,YAAY,aAAa;AAC7B,WAAO,gBAAgB,aAAa,eAAe,KAAK,WAAW,eAAe;AAAA,EACpF,SAAS,OAAP;AACA,WAAO;AAAA,EACT;AACF;AAOA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AAQA,SAAS,QAAQ,MAAM,UAAU;AAC/B,MAAI,QAAQ,WAAW,QAAQ,GAAG;AAChC,QAAI,MAAM,QAAQ,IAAI,KAAK,SAAS,KAAK,MAAM,GAAoB;AACjE,UAAI,SAAS,KAAK;AAClB,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC9B,YAAI,SAAS,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,OAAO;AACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,SAAS,IAAI,GAAG;AACzB,aAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,KAAK;AACvC,iBAAS,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AAQA,IAAI,SAAS,OAAO,UAAU,SAASC,QAAO,KAAK;AACjD,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,KAAK,UAAU;AAAA,EAC7B;AACA,MAAI,SAAS,GAAG,KAAK,KAAK,SAAS,GAAG;AACpC,SAAK,QAAQ,SAAU,KAAK;AAC1B,UAAI,SAAS,GAAG,GAAG;AACjB,eAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,KAAK;AACtC,cAAI,OAAO,IAAI;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAOpB,SAAS,SAAS,SAAS,QAAQ;AACjC,MAAI,QAAQ,QAAQ;AACpB,UAAQ,QAAQ,SAAU,OAAO,UAAU;AACzC,QAAI,cAAc,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG;AACnD,eAAS;AAAA,IACX;AACA,UAAM,YAAY;AAAA,EACpB,CAAC;AACH;AAOA,SAAS,mBAAmB,OAAO;AACjC,SAAO,SAAS,KAAK,IAAI,MAAM,QAAQ,iCAAiC,OAAO,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,IAAI;AAChL;AAQA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,CAAC,WAAW,CAAC,OAAO;AACtB,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,YAAY,QAAQ,UAAU,SAAS,KAAK,IAAI,QAAQ,UAAU,QAAQ,KAAK,IAAI;AACpG;AAOA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,CAAC,WAAW,CAAC,OAAO;AACtB;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,eAAS,MAAM,KAAK;AAAA,IACtB,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,IAAI,KAAK;AAC3B;AAAA,EACF;AACA,MAAI,YAAY,QAAQ,UAAU,KAAK;AACvC,MAAI,CAAC,WAAW;AACd,YAAQ,YAAY;AAAA,EACtB,WAAW,UAAU,QAAQ,KAAK,IAAI,GAAG;AACvC,YAAQ,YAAY,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,KAAK;AAAA,EAC5D;AACF;AAOA,SAAS,YAAY,SAAS,OAAO;AACnC,MAAI,CAAC,WAAW,CAAC,OAAO;AACtB;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,kBAAY,MAAM,KAAK;AAAA,IACzB,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,OAAO,KAAK;AAC9B;AAAA,EACF;AACA,MAAI,QAAQ,UAAU,QAAQ,KAAK,KAAK,GAAG;AACzC,YAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,EAAE;AAAA,EACzD;AACF;AAQA,SAAS,YAAY,SAAS,OAAO,OAAO;AAC1C,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,kBAAY,MAAM,OAAO,KAAK;AAAA,IAChC,CAAC;AACD;AAAA,EACF;AAGA,MAAI,OAAO;AACT,aAAS,SAAS,KAAK;AAAA,EACzB,OAAO;AACL,gBAAY,SAAS,KAAK;AAAA,EAC5B;AACF;AACA,IAAI,mBAAmB;AAOvB,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,QAAQ,kBAAkB,OAAO,EAAE,YAAY;AAC9D;AAQA,SAAS,QAAQ,SAAS,MAAM;AAC9B,MAAI,SAAS,QAAQ,KAAK,GAAG;AAC3B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,QAAQ,SAAS;AACnB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,SAAO,QAAQ,aAAa,QAAQ,OAAO,UAAU,IAAI,CAAC,CAAC;AAC7D;AAQA,SAAS,QAAQ,SAAS,MAAM,MAAM;AACpC,MAAI,SAAS,IAAI,GAAG;AAClB,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,SAAS;AAC1B,YAAQ,QAAQ,QAAQ;AAAA,EAC1B,OAAO;AACL,YAAQ,aAAa,QAAQ,OAAO,UAAU,IAAI,CAAC,GAAG,IAAI;AAAA,EAC5D;AACF;AACA,IAAI,gBAAgB,WAAY;AAC9B,MAAI,YAAY;AAChB,MAAI,YAAY;AACd,QAAI,OAAO;AACX,QAAI,WAAW,SAASC,YAAW;AAAA,IAAC;AACpC,QAAI,UAAU,OAAO,eAAe,CAAC,GAAG,QAAQ;AAAA,MAC9C,KAAK,SAAS,MAAM;AAClB,oBAAY;AACZ,eAAO;AAAA,MACT;AAAA,MAMA,KAAK,SAAS,IAAI,OAAO;AACvB,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,iBAAiB,QAAQ,UAAU,OAAO;AACjD,WAAO,oBAAoB,QAAQ,UAAU,OAAO;AAAA,EACtD;AACA,SAAO;AACT,EAAE;AASF,SAAS,eAAe,SAAS,MAAM,UAAU;AAC/C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK,CAAC;AACnF,MAAI,UAAU;AACd,OAAK,KAAK,EAAE,MAAM,aAAa,EAAE,QAAQ,SAAU,OAAO;AACxD,QAAI,CAAC,eAAe;AAClB,UAAI,YAAY,QAAQ;AACxB,UAAI,aAAa,UAAU,UAAU,UAAU,OAAO,WAAW;AAC/D,kBAAU,UAAU,OAAO;AAC3B,eAAO,UAAU,OAAO;AACxB,YAAI,OAAO,KAAK,UAAU,MAAM,EAAE,WAAW,GAAG;AAC9C,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,OAAO,KAAK,SAAS,EAAE,WAAW,GAAG;AACvC,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,YAAQ,oBAAoB,OAAO,SAAS,OAAO;AAAA,EACrD,CAAC;AACH;AASA,SAAS,YAAY,SAAS,MAAM,UAAU;AAC5C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK,CAAC;AACnF,MAAI,WAAW;AACf,OAAK,KAAK,EAAE,MAAM,aAAa,EAAE,QAAQ,SAAU,OAAO;AACxD,QAAI,QAAQ,QAAQ,CAAC,eAAe;AAClC,UAAI,qBAAqB,QAAQ,WAC/B,YAAY,uBAAuB,SAAS,CAAC,IAAI;AACnD,iBAAW,SAAS,UAAU;AAC5B,eAAO,UAAU,OAAO;AACxB,gBAAQ,oBAAoB,OAAO,UAAU,OAAO;AACpD,iBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,eAAK,SAAS,UAAU;AAAA,QAC1B;AACA,iBAAS,MAAM,SAAS,IAAI;AAAA,MAC9B;AACA,UAAI,CAAC,UAAU,QAAQ;AACrB,kBAAU,SAAS,CAAC;AAAA,MACtB;AACA,UAAI,UAAU,OAAO,WAAW;AAC9B,gBAAQ,oBAAoB,OAAO,UAAU,OAAO,WAAW,OAAO;AAAA,MACxE;AACA,gBAAU,OAAO,YAAY;AAC7B,cAAQ,YAAY;AAAA,IACtB;AACA,YAAQ,iBAAiB,OAAO,UAAU,OAAO;AAAA,EACnD,CAAC;AACH;AAUA,SAAS,cAAc,SAAS,MAAM,MAAM,SAAS;AACnD,MAAI;AAGJ,MAAI,WAAW,KAAK,KAAK,WAAW,WAAW,GAAG;AAChD,YAAQ,IAAI,YAAY,MAAM,eAAe;AAAA,MAC3C,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG,OAAO,CAAC;AAAA,EACb,OAAO;AACL,YAAQ,SAAS,YAAY,aAAa;AAC1C,UAAM,gBAAgB,MAAM,MAAM,MAAM,IAAI;AAAA,EAC9C;AACA,SAAO,QAAQ,cAAc,KAAK;AACpC;AAOA,SAAS,UAAU,SAAS;AAC1B,MAAI,MAAM,QAAQ,sBAAsB;AACxC,SAAO;AAAA,IACL,MAAM,IAAI,QAAQ,OAAO,cAAc,SAAS,gBAAgB;AAAA,IAChE,KAAK,IAAI,OAAO,OAAO,cAAc,SAAS,gBAAgB;AAAA,EAChE;AACF;AAOA,SAAS,cAAc,MAAM;AAC3B,MAAIC,UAAS,KAAK,QAChBC,UAAS,KAAK,QACdC,UAAS,KAAK,QACd,aAAa,KAAK,YAClB,aAAa,KAAK;AACpB,MAAI,SAAS,CAAC;AACd,MAAI,SAAS,UAAU,KAAK,eAAe,GAAG;AAC5C,WAAO,KAAK,cAAc,OAAO,YAAY,KAAK,CAAC;AAAA,EACrD;AACA,MAAI,SAAS,UAAU,KAAK,eAAe,GAAG;AAC5C,WAAO,KAAK,cAAc,OAAO,YAAY,KAAK,CAAC;AAAA,EACrD;AAGA,MAAI,SAASF,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,MAAM,CAAC;AAAA,EAC9C;AACA,MAAI,SAASC,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,GAAG,CAAC;AAAA,EAC3C;AACA,MAAI,SAASC,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,GAAG,CAAC;AAAA,EAC3C;AACA,MAAI,YAAY,OAAO,SAAS,OAAO,KAAK,GAAG,IAAI;AACnD,SAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb;AAAA,EACF;AACF;AAUA,SAAS,oBAAoB,KAAK;AAChC,SAAO,SAAS,GAAG,IAAI,mBAAmB,IAAI,QAAQ,SAAS,EAAE,EAAE,QAAQ,YAAY,EAAE,CAAC,IAAI;AAChG;AACA,IAAI,YAAY,OAAO,aAAa,kCAAkC,KAAK,OAAO,UAAU,SAAS;AASrG,SAAS,qBAAqB,OAAO,SAAS,UAAU;AACtD,MAAI,WAAW,SAAS,cAAc,KAAK;AAG3C,MAAI,MAAM,gBAAgB,CAAC,WAAW;AACpC,aAAS,MAAM,cAAc,MAAM,aAAa;AAChD,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,QAAQ,SAAS;AACrC,WAAS,SAAS,WAAY;AAC5B,aAAS,SAAS,OAAO,SAAS,MAAM;AACxC,QAAI,CAAC,WAAW;AACd,WAAK,YAAY,QAAQ;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,QAAQ,qBAAqB,SAAU,MAAM;AACnD,QAAI,QAAQ,MAAM,aAAa,IAAI;AACnC,QAAI,UAAU,MAAM;AAClB,eAAS,aAAa,MAAM,KAAK;AAAA,IACnC;AAAA,EACF,CAAC;AACD,WAAS,MAAM,MAAM;AAIrB,MAAI,CAAC,WAAW;AACd,aAAS,MAAM,UAAU;AACzB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AACA,SAAO;AACT;AAOA,SAAS,mBAAmB,MAAM;AAChC,UAAQ;AAAA,SACD;AACH,aAAO;AAAA,SACJ;AACH,aAAO;AAAA,SACJ;AACH,aAAO;AAAA;AAEP,aAAO;AAAA;AAEb;AAOA,SAAS,gBAAgB,UAAU;AACjC,MAAI,YAAY,eAAe,CAAC,GAAG,QAAQ;AAC3C,MAAI,SAAS,CAAC;AACd,UAAQ,UAAU,SAAU,SAAS,WAAW;AAC9C,WAAO,UAAU;AACjB,YAAQ,WAAW,SAAU,UAAU;AACrC,UAAI,KAAK,KAAK,IAAI,QAAQ,SAAS,SAAS,MAAM;AAClD,UAAI,KAAK,KAAK,IAAI,QAAQ,SAAS,SAAS,MAAM;AAClD,UAAI,KAAK,KAAK,IAAI,QAAQ,OAAO,SAAS,IAAI;AAC9C,UAAI,KAAK,KAAK,IAAI,QAAQ,OAAO,SAAS,IAAI;AAC9C,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACpC,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACpC,UAAI,SAAS,KAAK,MAAM;AACxB,aAAO,KAAK,KAAK;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACD,SAAO,KAAK,SAAU,GAAG,GAAG;AAC1B,WAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EACjC,CAAC;AACD,SAAO,OAAO;AAChB;AAQA,SAAS,WAAW,OAAO,SAAS;AAClC,MAAI,QAAQ,MAAM,OAChB,QAAQ,MAAM;AAChB,MAAI,MAAM;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACA,SAAO,UAAU,MAAM,eAAe;AAAA,IACpC,WAAW,KAAK,IAAI;AAAA,IACpB,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV,GAAG,GAAG;AACR;AAOA,SAAS,kBAAkB,UAAU;AACnC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,UAAQ,UAAU,SAAU,OAAO;AACjC,QAAI,SAAS,MAAM,QACjB,SAAS,MAAM;AACjB,aAAS;AACT,aAAS;AACT,aAAS;AAAA,EACX,CAAC;AACD,WAAS;AACT,WAAS;AACT,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,QAAQ,SAASC,UAAS;AACxB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,QAAI,gBAAgB,KAAK,QAAQ;AACjC,QAAI,OAAO,cAAc,QAAQ,cAAc;AAC/C,SAAK,OAAO;AACZ,SAAK,iBAAiB,OAAO,aAAa,cAAc,gBAAgB;AACxE,SAAK,0BAA0B,KAAK,MAAM;AAC1C,SAAK,kCAAkC,OAAO,iBAAiB,IAAI,EAAE;AAAA,EACvE;AAAA,EACA,eAAe,SAAS,gBAAgB;AACtC,SAAK,gBAAgB;AAAA,MACnB,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK;AAChB,QAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,mBAAa;AAAA,QACX,OAAO,KAAK,IAAI,OAAO,aAAa,QAAQ,QAAQ;AAAA,QACpD,QAAQ,KAAK,IAAI,OAAO,cAAc,QAAQ,SAAS;AAAA,MACzD;AACA,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,KAAK,UAAU,CAAC,YAAY;AAC9B,mBAAa,KAAK;AAAA,IACpB;AACA,SAAK,aAAa,OAAO,CAAC,GAAG,UAAU;AAAA,EACzC;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,QAAI,KAAK,QAAQ,UAAU,CAAC,KAAK,QAAQ;AACvC,eAAS,KAAK,QAAQ,KAAK,UAAU;AAAA,IACvC;AAAA,EACF;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,QAAI,QAAQ;AACZ,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,OAAO,KAAK;AACd,QAAI,QAAQ,CAAC;AAGb,SAAK,YAAY;AACjB,YAAQ,KAAK,QAAQ,SAAU,OAAO,OAAO;AAC3C,UAAI,MAAM,MAAM;AAChB,UAAI,MAAM,MAAM,OAAO,oBAAoB,GAAG;AAC9C,UAAI,MAAM,MAAM,YAAY,KAAK;AACjC,UAAI,OAAO,KAAK;AACd,YAAI,OAAO,SAAS,cAAc,IAAI;AACtC,YAAI,MAAM,SAAS,cAAc,KAAK;AACtC,gBAAQ,QAAQ,qBAAqB,SAAU,MAAM;AACnD,cAAI,QAAQ,MAAM,aAAa,IAAI;AACnC,cAAI,UAAU,MAAM;AAClB,gBAAI,aAAa,MAAM,KAAK;AAAA,UAC9B;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,QAAQ;AAClB,cAAI,MAAM,OAAO;AAAA,QACnB;AACA,YAAI,MAAM;AACV,YAAI,aAAa,qBAAqB,OAAO,GAAG;AAChD,aAAK,aAAa,cAAc,KAAK;AACrC,aAAK,aAAa,sBAAsB,MAAM;AAC9C,aAAK,aAAa,QAAQ,QAAQ;AAClC,YAAI,QAAQ,UAAU;AACpB,eAAK,aAAa,YAAY,CAAC;AAAA,QACjC;AACA,aAAK,YAAY,GAAG;AACpB,aAAK,YAAY,IAAI;AACrB,cAAM,KAAK,IAAI;AAAA,MACjB;AAAA,IACF,CAAC;AACD,SAAK,QAAQ;AACb,YAAQ,OAAO,SAAU,MAAM;AAC7B,UAAI,QAAQ,KAAK;AACjB,UAAI;AACJ,UAAI;AACJ,cAAQ,OAAO,UAAU,IAAI;AAC7B,UAAI,QAAQ,SAAS;AACnB,iBAAS,MAAM,aAAa;AAAA,MAC9B;AACA,kBAAY,OAAO,YAAY,SAAS,SAASC,QAAO,OAAO;AAC7D,uBAAe,OAAO,aAAa,OAAO;AAC1C,YAAI,QAAQ,SAAS;AACnB,sBAAY,MAAM,aAAa;AAAA,QACjC;AACA,cAAM,UAAU,KAAK;AAAA,MACvB,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AACD,kBAAY,OAAO,aAAa,UAAU,SAASC,WAAU;AAC3D,uBAAe,OAAO,YAAY,MAAM;AACxC,YAAI,QAAQ,SAAS;AACnB,sBAAY,MAAM,aAAa;AAAA,QACjC;AAAA,MACF,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,YAAY;AACtB,kBAAY,SAAS,cAAc,WAAY;AAC7C,iBAAS,MAAM,gBAAgB;AAAA,MACjC,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,KAAK,MAAM;AACtB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,QAAIC,QAAO,KAAK;AAChB,QAAI,SAAS,SAAS,OAAO,iBAAiBA,SAAQ,IAAI,EAAE,YAAY,EAAE;AAC1E,QAAI,cAAc,KAAK;AACvB,QAAI,aAAa,cAAc;AAG/B,aAAS,KAAK,MAAM,OAAO;AAAA,MACzB,OAAO,aAAa,KAAK,SAAS;AAAA,IACpC,GAAG,cAAc;AAAA,MACf,aAAa,KAAK,WAAW,QAAQ,eAAe,IAAI,aAAa;AAAA,IACvE,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,QAAI,OAAO,KAAK;AAChB,SAAK,YAAY;AACjB,gBAAY,MAAM,gBAAgB;AAClC,aAAS,MAAM,cAAc;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW,SAAS,UAAU,MAAM;AAClC,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,SACjB,QAAQ,KAAK,OACb,aAAa,KAAK;AACpB,QAAI,eAAe,KAAK,OAAO;AAC/B,QAAI,cAAc,WAAW;AAC7B,QAAI,eAAe,KAAK,IAAI,WAAW,SAAS,cAAc,YAAY;AAC1E,QAAI,eAAe,KAAK,aAAa,CAAC;AACtC,QAAI;AACJ,SAAK,oBAAoB;AAAA,MACvB,OAAO,SAAS,QAAQ;AACtB,oBAAY,SAAS;AAAA,MACvB;AAAA,IACF;AACA,kBAAc,qBAAqB,OAAO,SAAS,SAAU,cAAc,eAAe;AACxF,UAAI,cAAc,eAAe;AACjC,UAAI,kBAAkB,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,QAAQ,eAAe,CAAC;AACtE,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,aAAO,oBAAoB;AAC3B,UAAI,eAAe,cAAc,aAAa;AAC5C,iBAAS,cAAc;AAAA,MACzB,OAAO;AACL,gBAAQ,eAAe;AAAA,MACzB;AACA,wBAAkB,SAAS,eAAe,IAAI,kBAAkB;AAChE,cAAQ,KAAK,IAAI,QAAQ,iBAAiB,YAAY;AACtD,eAAS,KAAK,IAAI,SAAS,iBAAiB,aAAa;AACzD,UAAI,QAAQ,cAAc,SAAS;AACnC,UAAI,OAAO,eAAe,UAAU;AACpC,UAAI,YAAY;AAAA,QACd;AAAA,QACA;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,OAAO,QAAQ;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,UAAI,mBAAmB,OAAO,CAAC,GAAG,SAAS;AAC3C,UAAI,QAAQ,WAAW;AACrB,kBAAU,SAAS,aAAa,UAAU;AAC1C,yBAAiB,SAAS;AAAA,MAC5B;AACA,UAAI,QAAQ,UAAU;AACpB,kBAAU,SAAS,aAAa,UAAU;AAC1C,kBAAU,SAAS,aAAa,UAAU;AAC1C,yBAAiB,SAAS;AAC1B,yBAAiB,SAAS;AAAA,MAC5B;AACA,aAAO,YAAY;AACnB,aAAO,mBAAmB;AAC1B,UAAI,MAAM;AACR,aAAK;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,SAAS,YAAY,MAAM;AACtC,QAAI,SAAS;AACb,QAAI,QAAQ,KAAK,OACf,YAAY,KAAK;AACnB,aAAS,OAAO,OAAO;AAAA,MACrB,OAAO,UAAU;AAAA,MACjB,QAAQ,UAAU;AAAA,MAElB,YAAY,UAAU;AAAA,MACtB,WAAW,UAAU;AAAA,IACvB,GAAG,cAAc,SAAS,CAAC,CAAC;AAC5B,QAAI,MAAM;AACR,WAAK,KAAK,WAAW,KAAK,UAAU,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ,cAAc,SAAS,OAAO,gBAAgB,GAAG;AAClJ,YAAI,kBAAkB,SAASC,mBAAkB;AAC/C,iBAAO,iBAAiB;AACxB,eAAK;AAAA,QACP;AACA,aAAK,iBAAiB;AAAA,UACpB,OAAO,SAAS,QAAQ;AACtB,2BAAe,OAAO,sBAAsB,eAAe;AAAA,UAC7D;AAAA,QACF;AACA,oBAAY,OAAO,sBAAsB,iBAAiB;AAAA,UACxD,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO;AACT,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,MAAM;AAAA,MACrB;AACA,YAAM,WAAW,YAAY,KAAK;AAClC,WAAK,QAAQ;AACb,WAAK,MAAM,YAAY;AAAA,IACzB;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,MAAM,SAAS,OAAO;AACpB,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK,QACd,SAAS,KAAK;AAChB,QAAIC,YAAW,KAAK,QAAQ;AAC5B,gBAAY,QAAQ,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,CAAC;AACrE,gBAAY,QAAQ,kBAAkB,KAAK,cAAc,KAAK,UAAU,KAAK,IAAI,CAAC;AAClF,gBAAY,QAAQ,oBAAoB,KAAK,gBAAgB,KAAK,YAAY,KAAK,IAAI,CAAC;AACxF,gBAAYA,WAAU,oBAAoB,KAAK,gBAAgB,KAAK,YAAY,KAAK,IAAI,CAAC;AAC1F,gBAAYA,WAAU,kBAAkB,KAAK,cAAc,KAAK,UAAU,KAAK,IAAI,CAAC;AACpF,gBAAYA,WAAU,gBAAgB,KAAK,YAAY,KAAK,QAAQ,KAAK,IAAI,CAAC;AAC9E,gBAAY,QAAQ,cAAc,KAAK,WAAW,KAAK,OAAO,KAAK,IAAI,CAAC;AACxE,QAAI,QAAQ,YAAY,QAAQ,aAAa;AAC3C,kBAAY,QAAQ,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,GAAG;AAAA,QACrE,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,kBAAkB;AAC5B,kBAAY,QAAQ,gBAAgB,KAAK,aAAa,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,IAChF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK,QACd,SAAS,KAAK;AAChB,QAAIA,YAAW,KAAK,QAAQ;AAC5B,mBAAe,QAAQ,aAAa,KAAK,OAAO;AAChD,mBAAe,QAAQ,kBAAkB,KAAK,WAAW;AACzD,mBAAe,QAAQ,oBAAoB,KAAK,aAAa;AAC7D,mBAAeA,WAAU,oBAAoB,KAAK,aAAa;AAC/D,mBAAeA,WAAU,kBAAkB,KAAK,WAAW;AAC3D,mBAAeA,WAAU,gBAAgB,KAAK,SAAS;AACvD,mBAAe,QAAQ,cAAc,KAAK,QAAQ;AAClD,QAAI,QAAQ,YAAY,QAAQ,aAAa;AAC3C,qBAAe,QAAQ,aAAa,KAAK,SAAS;AAAA,QAChD,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,kBAAkB;AAC5B,qBAAe,QAAQ,gBAAgB,KAAK,UAAU;AAAA,IACxD;AAAA,EACF;AACF;AAEA,IAAI,WAAW;AAAA,EACb,OAAO,SAAS,MAAM,OAAO;AAC3B,QAAI,UAAU,KAAK,SACjB,YAAY,KAAK;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,QAAQ,QAAQ,WAAW;AACxC,QAAI,CAAC,UAAU,OAAO,cAAc,SAAS,OAAO,cAAc,cAAc,MAAM;AACpF,eAAS,OAAO;AAChB,eAAS,QAAQ,QAAQ,WAAW;AAAA,IACtC;AAGA,QAAI,mBAAmB,MAAM,aAAa,WAAW,KAAK,QAAQ;AAChE,mBAAa,KAAK,kBAAkB;AAAA,IACtC;AACA,YAAQ;AAAA,WACD;AACH,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ,WAAW,QAAQ,QAAQ;AACzB,cAAI,KAAK,QAAQ;AACf,iBAAK,KAAK;AAAA,UACZ,OAAO;AACL,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,OAAO;AACL,eAAK,KAAK;AAAA,QACZ;AACA;AAAA,WACG;AACH,YAAI,CAAC,KAAK,cAAc;AACtB,eAAK,KAAK;AAAA,QACZ;AACA;AAAA,WACG;AACH,aAAK,KAAK,QAAQ,QAAQ,OAAO,CAAC;AAClC;AAAA,WACG;AACH,aAAK,KAAK,KAAK,IAAI;AACnB;AAAA,WACG;AACH,aAAK,KAAK,MAAM,IAAI;AACpB;AAAA,WACG;AACH,aAAK,OAAO;AACZ;AAAA,WACG;AACH,aAAK,MAAM;AACX;AAAA,WACG;AACH,aAAK,KAAK,QAAQ,IAAI;AACtB;AAAA,WACG;AACH,aAAK,KAAK,QAAQ,UAAU;AAC5B;AAAA,WACG;AACH,aAAK,KAAK,QAAQ,IAAI;AACtB;AAAA,WACG;AACH,aAAK,OAAO,GAAG;AACf;AAAA,WACG;AACH,aAAK,OAAO,EAAE;AACd;AAAA,WACG;AACH,aAAK,OAAO,CAAC,UAAU,UAAU,EAAE;AACnC;AAAA,WACG;AACH,aAAK,OAAO,CAAC,UAAU,UAAU,EAAE;AACnC;AAAA;AAEA,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ;AAAA;AAAA,EAEN;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,UAAM,eAAe;AACrB,QAAI,KAAK,UAAU,MAAM,WAAW,KAAK,OAAO;AAE9C,UAAI,mBAAmB,MAAM,WAAW;AACtC,qBAAa,KAAK,uBAAuB;AAAA,MAC3C;AAGA,WAAK,OAAO,MAAM,YAAY,QAAQ,MAAM,UAAU,MAAM,OAAO,aAAa;AAAA,IAClF;AAAA,EACF;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,QAAI,QAAQ;AACZ,QAAI,KAAK,SAAS;AAChB,mBAAa,KAAK,OAAO;AACzB,WAAK,UAAU;AAAA,IACjB;AACA,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,QAAQ,KAAK,OACb,QAAQ,KAAK,OACb,aAAa,KAAK;AACpB,gBAAY,OAAO,eAAe;AAClC,QAAI,QAAQ,SAAS;AACnB,kBAAY,KAAK,QAAQ,aAAa;AAAA,IACxC;AACA,UAAM,MAAM,UAAU,cAAc,eAAe,OAAO,WAAW,QAAQ,GAAG,KAAK,IAAI,cAAc,OAAO,WAAW,SAAS,GAAG,KAAK,IAAI;AAC9I,SAAK,UAAU,WAAY;AACzB,kBAAY,OAAO,YAAY,QAAQ,OAAO;AAC9C,kBAAY,OAAO,kBAAkB,QAAQ,UAAU;AACvD,YAAM,YAAY,WAAY;AAC5B,cAAM,SAAS;AACf,cAAM,UAAU;AAChB,YAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,sBAAY,SAAS,cAAc,QAAQ,QAAQ;AAAA,YACjD,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,sBAAc,SAAS,cAAc;AAAA,UACnC,eAAe,MAAM,OAAO;AAAA,UAC5B;AAAA,UACA;AAAA,QACF,GAAG;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,cAAc,OAAO,eAAe;AACxC,QAAI,eAAe,OAAO,gBAAgB;AAC1C,QAAI,SAAS,CAAC,CAAC,QAAQ,OAAO,QAAQ;AACtC,yBAAqB,OAAO,KAAK,SAAS,SAAU,cAAc,eAAe;AAC/E,UAAI,cAAc,eAAe;AACjC,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,eAAe,cAAc,aAAa;AAC5C,YAAI,QAAQ;AACV,kBAAQ,eAAe;AAAA,QACzB,OAAO;AACL,mBAAS,cAAc;AAAA,QACzB;AAAA,MACF,WAAW,QAAQ;AACjB,iBAAS,cAAc;AAAA,MACzB,OAAO;AACL,gBAAQ,eAAe;AAAA,MACzB;AACA,eAAS,OAAO,OAAO;AAAA,QACrB;AAAA,QACA;AAAA,MACF,GAAG,cAAc;AAAA,QACf,aAAa,cAAc,SAAS;AAAA,QACpC,aAAa,eAAe,UAAU;AAAA,MACxC,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAI,UAAU,KAAK;AACnB,QAAI,CAAC,QAAQ,UAAU;AACrB;AAAA,IACF;AACA,QAAI,UAAU,MAAM,WAAW,MAAM,SAAS,MAAM;AACpD,YAAQ;AAAA,WAED;AACH,YAAI,KAAK,OAAO,SAAS,MAAM,MAAM,GAAG;AACtC,eAAK,MAAM,KAAK;AAAA,QAClB;AACA;AAAA;AAEJ,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,YAAQ;AAAA,WAED;AACH,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ,WAAW,QAAQ,QAAQ;AACzB,cAAI,KAAK,QAAQ;AACf,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,OAAO;AACL,eAAK,KAAK;AAAA,QACZ;AACA;AAAA,WAGG;AACH,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ;AACA;AAAA,WAGG;AACH,YAAI,KAAK,UAAU,KAAK,SAAS;AAC/B,eAAK,QAAQ,KAAK;AAAA,QACpB,OAAO;AACL,eAAK,KAAK,QAAQ,IAAI;AAAA,QACxB;AACA;AAAA,WAGG;AAEH,cAAM,eAAe;AAGrB,aAAK,KAAK,QAAQ,WAAW,IAAI;AACjC;AAAA,WAGG;AACH,YAAI,KAAK,UAAU,KAAK,SAAS;AAC/B,eAAK,QAAQ,KAAK;AAAA,QACpB,OAAO;AACL,eAAK,KAAK,QAAQ,IAAI;AAAA,QACxB;AACA;AAAA,WAGG;AAEH,cAAM,eAAe;AAGrB,aAAK,KAAK,CAAC,QAAQ,WAAW,IAAI;AAClC;AAAA,WAGG;AAAA,WAKA;AACH,YAAI,MAAM,SAAS;AACjB,gBAAM,eAAe;AACrB,eAAK,OAAO;AAAA,QACd;AACA;AAAA;AAAA,EAEN;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,QAAI,MAAM,OAAO,cAAc,OAAO;AACpC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,UAAU,KAAK,SACjB,WAAW,KAAK;AAClB,QAAI,UAAU,MAAM,SAClB,SAAS,MAAM;AACjB,SAAK,eAAe;AACpB,QAAI,CAAC,KAAK,UAAU,KAAK,WAAW,KAAK,WAAW,KAAK,WAGrD,MAAM,SAAS,eAAe,MAAM,SAAS,iBAAiB,MAAM,gBAAgB,aAExF,SAAS,OAAO,KAAK,YAAY,KAAK,SAAS,MAAM,KAAK,WAAW,KAGlE,MAAM,UAAU;AACjB;AAAA,IACF;AAGA,UAAM,eAAe;AACrB,QAAI,MAAM,gBAAgB;AACxB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAC7C,iBAAS,MAAM,cAAc,WAAW,KAAK;AAAA,MAC/C,CAAC;AAAA,IACH,OAAO;AACL,eAAS,MAAM,aAAa,KAAK,WAAW,KAAK;AAAA,IACnD;AACA,QAAI,SAAS,QAAQ,UAAU,cAAc;AAC7C,QAAI,QAAQ,eAAe,QAAQ,YAAY,OAAO,KAAK,QAAQ,EAAE,SAAS,GAAG;AAC/E,eAAS;AAAA,IACX,WAAW,QAAQ,iBAAiB,MAAM,gBAAgB,WAAW,MAAM,SAAS,iBAAiB,KAAK,aAAa,GAAG;AACxH,eAAS;AAAA,IACX;AACA,QAAI,QAAQ,eAAe,WAAW,eAAe,WAAW,cAAc;AAC5E,kBAAY,KAAK,OAAO,gBAAgB;AAAA,IAC1C;AACA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,WAAW,KAAK,UAClB,SAAS,KAAK;AAChB,QAAI,CAAC,KAAK,UAAU,CAAC,QAAQ;AAC3B;AAAA,IACF;AACA,UAAM,eAAe;AACrB,QAAI,MAAM,gBAAgB;AACxB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAC7C,eAAO,SAAS,MAAM,eAAe,CAAC,GAAG,WAAW,OAAO,IAAI,CAAC;AAAA,MAClE,CAAC;AAAA,IACH,OAAO;AACL,aAAO,SAAS,MAAM,aAAa,MAAM,CAAC,GAAG,WAAW,OAAO,IAAI,CAAC;AAAA,IACtE;AACA,SAAK,OAAO,KAAK;AAAA,EACnB;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK,QACd,WAAW,KAAK;AAClB,QAAI;AACJ,QAAI,MAAM,gBAAgB;AACxB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAC7C,kBAAU,SAAS,MAAM;AACzB,eAAO,SAAS,MAAM;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,gBAAU,SAAS,MAAM,aAAa;AACtC,aAAO,SAAS,MAAM,aAAa;AAAA,IACrC;AACA,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,eAAe;AACrB,QAAI,QAAQ,eAAe,WAAW,eAAe,WAAW,cAAc;AAC5E,eAAS,KAAK,OAAO,gBAAgB;AAAA,IACvC;AACA,SAAK,SAAS;AAGd,QAAI,mBAAmB,WAAW,eAAe,WAAW,KAAK,IAAI,IAAI,QAAQ,YAAY,KAAK;AAChG,mBAAa,KAAK,kBAAkB;AACpC,mBAAa,KAAK,uBAAuB;AACzC,UAAI,QAAQ,oBAAoB,KAAK,UAAU,MAAM,WAAW,KAAK,OAAO;AAC1E,YAAI,KAAK,cAAc;AACrB,eAAK,eAAe;AAGpB,eAAK,0BAA0B,WAAW,WAAY;AACpD,0BAAc,OAAO,OAAO,gBAAgB;AAAA,cAC1C,eAAe;AAAA,YACjB,CAAC;AAAA,UACH,GAAG,EAAE;AAAA,QACP,OAAO;AACL,eAAK,eAAe;AAGpB,eAAK,0BAA0B,WAAW,WAAY;AACpD,mBAAO,eAAe;AAAA,UACxB,GAAG,GAAG;AAAA,QACR;AAAA,MACF,OAAO;AACL,aAAK,eAAe;AACpB,YAAI,QAAQ,YAAY,QAAQ,aAAa,YAAY,MAAM,WAAW,KAAK,QAAQ;AAErF,eAAK,qBAAqB,WAAW,WAAY;AAC/C,0BAAc,OAAO,QAAQ,aAAa;AAAA,cACxC,eAAe;AAAA,YACjB,CAAC;AAAA,UACH,GAAG,EAAE;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,SAAS;AACb,QAAI,CAAC,KAAK,WAAW,KAAK,QAAQ;AAChC;AAAA,IACF;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,MAAM;AACX,WAAK,SAAS;AACd,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,UAAU,WAAY;AACzB,eAAO,YAAY;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,QAAQ;AACf,UAAI,KAAK,QAAQ,cAAc,KAAK,UAAU,EAAE,SAAS,qBAAqB,SAAS,2BAA2B,SAAS,wBAAwB,SAAS,sBAAsB;AAChL,aAAK,KAAK;AACV;AAAA,MACF;AACA,cAAQ,KAAK,OAAO,qBAAqB,KAAK,GAAG,SAAU,OAAO;AAChE,oBAAY,OAAO,YAAY,OAAO,UAAU,KAAK,MAAM,GAAG;AAAA,UAC5D,MAAM;AAAA,QACR,CAAC;AACD,sBAAc,OAAO,UAAU;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,SAAS,MAAM,OAAO;AAC3B,QAAI,SAAS;AACb,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,eAAe;AAGrB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,eAAW,WAAY;AACrB,aAAO,WAAW;AAAA,IACpB,GAAG,EAAE;AACL,QAAI,QAAQ,OAAO,KAAK,QAAQ,SAAS,KAAK;AAC9C,QAAI,QAAQ;AACZ,QAAI,MAAM,QAAQ;AAChB,cAAQ,MAAM,SAAS,IAAI,IAAI;AAAA,IACjC,WAAW,MAAM,YAAY;AAC3B,cAAQ,CAAC,MAAM,aAAa;AAAA,IAC9B,WAAW,MAAM,QAAQ;AACvB,cAAQ,MAAM,SAAS,IAAI,IAAI;AAAA,IACjC;AACA,SAAK,KAAK,CAAC,QAAQ,OAAO,MAAM,MAAM,KAAK;AAAA,EAC7C;AACF;AAEA,IAAI,UAAU;AAAA,EAKZ,MAAM,SAAS,OAAO;AACpB,QAAI,YAAY,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACpF,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,QAAI,QAAQ,UAAU,KAAK,WAAW,KAAK,WAAW,KAAK,SAAS;AAClE,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,MAAM;AACX,UAAI,KAAK,OAAO;AACd,aAAK,KAAK,SAAS;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,UAAU,MAAM,SAAS,CAAC,KAAK,OAAO;AAC/D,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,cAAc,MAAM;AAAA,IAC3B;AACA,SAAK,UAAU;AACf,SAAK,KAAK;AACV,QAAI,SAAS,KAAK;AAClB,gBAAY,QAAQ,UAAU;AAC9B,WAAO,aAAa,QAAQ,QAAQ;AACpC,WAAO,aAAa,mBAAmB,KAAK,MAAM,EAAE;AACpD,WAAO,aAAa,cAAc,IAAI;AACtC,WAAO,gBAAgB,aAAa;AACpC,QAAI,QAAQ,cAAc,CAAC,WAAW;AACpC,UAAIC,SAAQ,KAAK,MAAM,KAAK,IAAI;AAChC,WAAK,gBAAgB;AAAA,QACnB,OAAO,SAAS,QAAQ;AACtB,yBAAe,QAAQ,sBAAsBA,MAAK;AAClD,sBAAY,QAAQ,QAAQ;AAAA,QAC9B;AAAA,MACF;AACA,eAAS,QAAQ,gBAAgB;AAGjC,aAAO,qBAAqB,OAAO;AACnC,kBAAY,QAAQ,sBAAsBA,QAAO;AAAA,QAC/C,MAAM;AAAA,MACR,CAAC;AACD,eAAS,QAAQ,QAAQ;AAAA,IAC3B,OAAO;AACL,eAAS,QAAQ,QAAQ;AACzB,WAAK,MAAM;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EAMA,MAAM,SAAS,OAAO;AACpB,QAAI,QAAQ;AACZ,QAAI,YAAY,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACpF,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,QAAI,QAAQ,UAAU,KAAK,UAAU,EAAE,KAAK,WAAW,KAAK,UAAU;AACpE,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,UAAU,MAAM,OAAO;AAChD,aAAO;AAAA,IACT;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,MAAM;AAAA,IAC3B;AACA,SAAK,SAAS;AACd,QAAI,KAAK,QAAQ;AACf,WAAK,KAAK;AAAA,IACZ,WAAW,KAAK,SAAS;AACvB,WAAK,QAAQ,MAAM;AAAA,IACrB;AACA,QAAI,SAAS,KAAK,QAChB,QAAQ,KAAK;AACf,QAAI,kBAAkB,SAASC,mBAAkB;AAC/C,kBAAY,QAAQ,QAAQ;AAC5B,YAAM,OAAO;AAAA,IACf;AACA,QAAI,QAAQ,cAAc,CAAC,WAAW;AACpC,UAAI,yBAAyB,SAAS,sBAAsB,OAAO;AAEjE,YAAI,SAAS,MAAM,WAAW,QAAQ;AACpC,yBAAe,QAAQ,sBAAsB,sBAAsB;AACnE,gBAAM,OAAO;AAAA,QACf;AAAA,MACF;AACA,UAAI,uBAAuB,SAASC,wBAAuB;AAEzD,YAAI,SAAS,QAAQ,gBAAgB,GAAG;AACtC,sBAAY,QAAQ,sBAAsB,sBAAsB;AAChE,sBAAY,QAAQ,QAAQ;AAAA,QAC9B,OAAO;AACL,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,WAAK,gBAAgB;AAAA,QACnB,OAAO,SAAS,QAAQ;AACtB,cAAI,MAAM,UAAU,SAAS,OAAO,gBAAgB,GAAG;AACrD,2BAAe,OAAO,sBAAsB,oBAAoB;AAAA,UAClE,WAAW,SAAS,QAAQ,gBAAgB,GAAG;AAC7C,2BAAe,QAAQ,sBAAsB,sBAAsB;AAAA,UACrE;AAAA,QACF;AAAA,MACF;AAIA,UAAI,KAAK,UAAU,SAAS,OAAO,gBAAgB,GAAG;AACpD,oBAAY,OAAO,sBAAsB,sBAAsB;AAAA,UAC7D,MAAM;AAAA,QACR,CAAC;AACD,aAAK,OAAO,GAAG,OAAO,MAAM,MAAM,IAAI;AAAA,MACxC,OAAO;AACL,6BAAqB;AAAA,MACvB;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA,EAMA,MAAM,SAAS,OAAO;AACpB,QAAI,SAAS;AACb,QAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK,KAAK,QAAQ;AAC7F,YAAQ,OAAO,KAAK,KAAK;AACzB,QAAI,KAAK,UAAU,KAAK,UAAU,QAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,UAAU,KAAK,OAAO;AAC1G,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,QAAQ;AACb,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,MAAM;AAAA,IACrB;AACA,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,QAAQ,KAAK,OACb,SAAS,KAAK;AAChB,QAAI,OAAO,KAAK,MAAM;AACtB,QAAI,MAAM,KAAK,cAAc,KAAK;AAClC,QAAI,MAAM,QAAQ,KAAK,aAAa;AACpC,QAAI,MAAM,IAAI,aAAa,KAAK;AAChC,QAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,YAAQ,QAAQ,qBAAqB,SAAU,MAAM;AACnD,UAAI,QAAQ,IAAI,aAAa,IAAI;AACjC,UAAI,UAAU,MAAM;AAClB,cAAM,aAAa,MAAM,KAAK;AAAA,MAChC;AAAA,IACF,CAAC;AACD,UAAM,MAAM;AACZ,UAAM,MAAM;AACZ,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,YAAY;AAAA,MACrC,eAAe,KAAK,OAAO;AAAA,MAC3B;AAAA,MACA;AAAA,IACF,CAAC,MAAM,SAAS,CAAC,KAAK,WAAW,KAAK,UAAU,KAAK,QAAQ;AAC3D,aAAO;AAAA,IACT;AACA,QAAI,aAAa,KAAK,MAAM,KAAK;AACjC,QAAI,YAAY;AACd,kBAAY,YAAY,YAAY;AACpC,iBAAW,gBAAgB,eAAe;AAAA,IAC5C;AACA,aAAS,MAAM,YAAY;AAC3B,SAAK,aAAa,iBAAiB,IAAI;AACvC,QAAI,QAAQ,OAAO;AACjB,WAAK,MAAM;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,YAAY,CAAC;AAClB,aAAS,OAAO,eAAe;AAC/B,QAAI,QAAQ,SAAS;AACnB,eAAS,QAAQ,aAAa;AAAA,IAChC;AACA,WAAO,YAAY;AACnB,WAAO,YAAY,KAAK;AAGxB,SAAK,WAAW;AAGhB,UAAM,YAAY;AAGlB,QAAI,WAAW,SAASC,YAAW;AACjC,UAAI,YAAY,OAAO;AACvB,UAAIT,UAAS,MAAM,QAAQ,QAAQ,KAAK,IAAI,QAAQ,MAAM,KAAK,QAAQ;AACvE,YAAM,YAAY,mBAAmB,WAAWA,OAAM,IAAIA,QAAO,KAAK,QAAQ,OAAO,SAAS,IAAI,GAAG,OAAO,KAAK,IAAI,EAAE,OAAO,UAAU,cAAc,QAAQ,EAAE,OAAO,UAAU,eAAe,GAAG,CAAC;AAAA,IACtM;AACA,QAAI;AACJ,QAAI;AACJ,gBAAY,SAAS,cAAc,UAAU;AAAA,MAC3C,MAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU;AAAA,MACb,OAAO,SAAS,QAAQ;AACtB,uBAAe,SAAS,cAAc,QAAQ;AAC9C,YAAI,MAAM,UAAU;AAClB,cAAI,OAAO,gBAAgB;AACzB,mBAAO,eAAe,MAAM;AAAA,UAC9B,WAAW,OAAO,mBAAmB;AACnC,mBAAO,kBAAkB,MAAM;AAAA,UACjC;AAAA,QACF,OAAO;AAEL,gBAAM,MAAM;AACZ,yBAAe,OAAO,YAAY,MAAM;AACxC,cAAI,OAAO,SAAS;AAClB,yBAAa,OAAO,OAAO;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,UAAU;AAClB,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,kBAAY,OAAO,YAAY,SAAS,SAASC,UAAS;AACxD,uBAAe,OAAO,aAAa,OAAO;AAC1C,eAAO,KAAK;AAAA,MACd,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AACD,kBAAY,OAAO,aAAa,UAAU,SAASC,WAAU;AAC3D,uBAAe,OAAO,YAAY,MAAM;AACxC,YAAI,OAAO,SAAS;AAClB,uBAAa,OAAO,OAAO;AAC3B,iBAAO,UAAU;AAAA,QACnB;AACA,oBAAY,OAAO,eAAe;AAClC,YAAI,QAAQ,SAAS;AACnB,sBAAY,OAAO,QAAQ,aAAa;AAAA,QAC1C;AAAA,MACF,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AACD,UAAI,KAAK,SAAS;AAChB,qBAAa,KAAK,OAAO;AAAA,MAC3B;AAGA,WAAK,UAAU,WAAW,WAAY;AACpC,oBAAY,OAAO,eAAe;AAClC,eAAO,UAAU;AAAA,MACnB,GAAG,GAAI;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAOA,MAAM,SAAS,OAAO;AACpB,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAC/E,QAAI,QAAQ,KAAK,QAAQ;AACzB,QAAI,QAAQ,GAAG;AACb,cAAQ,OAAO,KAAK,SAAS,IAAI;AAAA,IACnC;AACA,SAAK,KAAK,KAAK;AACf,WAAO;AAAA,EACT;AAAA,EAOA,MAAM,SAAS,OAAO;AACpB,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAC/E,QAAI,WAAW,KAAK,SAAS;AAC7B,QAAI,QAAQ,KAAK,QAAQ;AACzB,QAAI,QAAQ,UAAU;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB;AACA,SAAK,KAAK,KAAK;AACf,WAAO;AAAA,EACT;AAAA,EAOA,MAAM,SAAS,KAAK,GAAG;AACrB,QAAI,IAAI,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAC5E,QAAI,YAAY,KAAK;AACrB,SAAK,OAAO,YAAY,CAAC,IAAI,IAAI,UAAU,IAAI,OAAO,CAAC,GAAG,YAAY,CAAC,IAAI,IAAI,UAAU,IAAI,OAAO,CAAC,CAAC;AACtG,WAAO;AAAA,EACT;AAAA,EAQA,QAAQ,SAAS,OAAO,GAAG;AACzB,QAAI,SAAS;AACb,QAAI,IAAI,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAC5E,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACzF,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACnB,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,KAAK,UAAU,CAAC,KAAK,UAAU,QAAQ,SAAS;AAClD,UAAI,OAAO,UAAU;AACrB,UAAI,OAAO,UAAU;AACrB,UAAI,UAAU;AACd,UAAI,SAAS,CAAC,GAAG;AACf,kBAAU;AAAA,MACZ,OAAO;AACL,YAAI;AAAA,MACN;AACA,UAAI,SAAS,CAAC,GAAG;AACf,kBAAU;AAAA,MACZ,OAAO;AACL,YAAI;AAAA,MACN;AACA,UAAI,SAAS;AACX,YAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,sBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,YAC7C,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,YAAI,cAAc,SAAS,YAAY;AAAA,UACrC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,eAAe;AAAA,QACjB,CAAC,MAAM,OAAO;AACZ,iBAAO;AAAA,QACT;AACA,kBAAU,IAAI;AACd,kBAAU,IAAI;AACd,kBAAU,OAAO;AACjB,kBAAU,MAAM;AAChB,aAAK,SAAS;AACd,aAAK,YAAY,WAAY;AAC3B,iBAAO,SAAS;AAChB,cAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,wBAAY,SAAS,aAAa,QAAQ,OAAO;AAAA,cAC/C,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA,wBAAc,SAAS,aAAa;AAAA,YAClC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UACjB,GAAG;AAAA,YACD,YAAY;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAMA,QAAQ,SAAS,OAAO,QAAQ;AAC9B,SAAK,UAAU,KAAK,UAAU,UAAU,KAAK,OAAO,MAAM,CAAC;AAC3D,WAAO;AAAA,EACT;AAAA,EAMA,UAAU,SAAS,SAAS,QAAQ;AAClC,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACnB,aAAS,OAAO,MAAM;AACtB,QAAI,SAAS,MAAM,KAAK,KAAK,UAAU,CAAC,KAAK,UAAU,QAAQ,WAAW;AACxE,UAAI,YAAY,UAAU;AAC1B,UAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,oBAAY,SAAS,cAAc,QAAQ,QAAQ;AAAA,UACjD,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,cAAc,SAAS,cAAc;AAAA,QACvC;AAAA,QACA;AAAA,MACF,CAAC,MAAM,OAAO;AACZ,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,WAAK,WAAW;AAChB,WAAK,YAAY,WAAY;AAC3B,eAAO,WAAW;AAClB,YAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,sBAAY,SAAS,eAAe,QAAQ,SAAS;AAAA,YACnD,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,sBAAc,SAAS,eAAe;AAAA,UACpC;AAAA,UACA;AAAA,QACF,GAAG;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAMA,QAAQ,SAAS,OAAO,SAAS;AAC/B,SAAK,MAAM,SAAS,KAAK,UAAU,MAAM;AACzC,WAAO;AAAA,EACT;AAAA,EAMA,QAAQ,SAAS,OAAO,SAAS;AAC/B,SAAK,MAAM,KAAK,UAAU,QAAQ,OAAO;AACzC,WAAO;AAAA,EACT;AAAA,EAOA,OAAO,SAAS,MAAMJ,SAAQ;AAC5B,QAAI,SAAS;AACb,QAAIC,UAAS,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAKD;AACjF,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACnB,IAAAA,UAAS,OAAOA,OAAM;AACtB,IAAAC,UAAS,OAAOA,OAAM;AACtB,QAAI,KAAK,UAAU,CAAC,KAAK,UAAU,QAAQ,UAAU;AACnD,UAAI,YAAY,UAAU;AAC1B,UAAI,YAAY,UAAU;AAC1B,UAAI,UAAU;AACd,UAAI,SAASD,OAAM,GAAG;AACpB,kBAAU;AAAA,MACZ,OAAO;AACL,QAAAA,UAAS;AAAA,MACX;AACA,UAAI,SAASC,OAAM,GAAG;AACpB,kBAAU;AAAA,MACZ,OAAO;AACL,QAAAA,UAAS;AAAA,MACX;AACA,UAAI,SAAS;AACX,YAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,sBAAY,SAAS,aAAa,QAAQ,OAAO;AAAA,YAC/C,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,YAAI,cAAc,SAAS,aAAa;AAAA,UACtC,QAAQD;AAAA,UACR,QAAQC;AAAA,UACR;AAAA,UACA;AAAA,QACF,CAAC,MAAM,OAAO;AACZ,iBAAO;AAAA,QACT;AACA,kBAAU,SAASD;AACnB,kBAAU,SAASC;AACnB,aAAK,UAAU;AACf,aAAK,YAAY,WAAY;AAC3B,iBAAO,UAAU;AACjB,cAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,wBAAY,SAAS,cAAc,QAAQ,QAAQ;AAAA,cACjD,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA,wBAAc,SAAS,cAAc;AAAA,YACnC,QAAQD;AAAA,YACR,QAAQC;AAAA,YACR;AAAA,YACA;AAAA,UACF,GAAG;AAAA,YACD,YAAY;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EASA,MAAM,SAAS,KAAK,OAAO;AACzB,QAAI,cAAc,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACtF,QAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAChF,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACzF,QAAI,YAAY,KAAK;AACrB,YAAQ,OAAO,KAAK;AACpB,QAAI,QAAQ,GAAG;AACb,cAAQ,KAAK,IAAI;AAAA,IACnB,OAAO;AACL,cAAQ,IAAI;AAAA,IACd;AACA,SAAK,OAAO,UAAU,QAAQ,QAAQ,UAAU,cAAc,aAAa,OAAO,cAAc;AAChG,WAAO;AAAA,EACT;AAAA,EAUA,QAAQ,SAAS,OAAO,OAAO;AAC7B,QAAI,SAAS;AACb,QAAI,cAAc,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACtF,QAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AAChF,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACzF,QAAI,YAAY,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACpF,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,YAAY,KAAK;AACnB,QAAI,IAAI,UAAU,GAChB,IAAI,UAAU,GACd,QAAQ,UAAU,OAClB,SAAS,UAAU,QACnB,eAAe,UAAU,cACzB,gBAAgB,UAAU;AAC5B,YAAQ,KAAK,IAAI,GAAG,KAAK;AACzB,QAAI,SAAS,KAAK,KAAK,KAAK,UAAU,CAAC,KAAK,WAAW,aAAa,QAAQ,WAAW;AACrF,UAAI,CAAC,WAAW;AACd,YAAI,eAAe,KAAK,IAAI,MAAM,QAAQ,YAAY;AACtD,YAAI,eAAe,KAAK,IAAI,KAAK,QAAQ,YAAY;AACrD,gBAAQ,KAAK,IAAI,KAAK,IAAI,OAAO,YAAY,GAAG,YAAY;AAAA,MAC9D;AACA,UAAI,gBAAgB;AAClB,gBAAQ,eAAe;AAAA,eAChB;AACH,gBAAI,QAAQ,aAAa,SAAS,QAAQ,QAAQ,QAAQ,MAAM;AAC9D,sBAAQ;AAAA,YACV;AACA;AAAA,eACG;AAAA,eACA;AAAA,eACA;AACH,gBAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,sBAAQ;AAAA,YACV;AACA;AAAA;AAAA,MAEN;AACA,UAAI,WAAW,eAAe;AAC9B,UAAI,YAAY,gBAAgB;AAChC,UAAI,cAAc,WAAW;AAC7B,UAAI,eAAe,YAAY;AAC/B,UAAI,WAAW,UAAU;AACzB,UAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,oBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,UAC7C,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,cAAc,SAAS,YAAY;AAAA,QACrC;AAAA,QACA;AAAA,QACA,eAAe;AAAA,MACjB,CAAC,MAAM,OAAO;AACZ,eAAO;AAAA,MACT;AACA,WAAK,UAAU;AACf,UAAI,gBAAgB;AAClB,YAAI,SAAS,UAAU,KAAK,MAAM;AAClC,YAAI,SAAS,YAAY,OAAO,KAAK,QAAQ,EAAE,SAAS,IAAI,kBAAkB,QAAQ,IAAI;AAAA,UACxF,OAAO,eAAe;AAAA,UACtB,OAAO,eAAe;AAAA,QACxB;AAGA,kBAAU,KAAK,gBAAgB,OAAO,QAAQ,OAAO,OAAO,KAAK;AACjE,kBAAU,KAAK,iBAAiB,OAAO,QAAQ,OAAO,MAAM,KAAK;AAAA,MACnE,WAAW,cAAc,KAAK,KAAK,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,GAAG;AACzE,kBAAU,KAAK,gBAAgB,MAAM,IAAI,KAAK;AAC9C,kBAAU,KAAK,iBAAiB,MAAM,IAAI,KAAK;AAAA,MACjD,OAAO;AAEL,kBAAU,KAAK,cAAc;AAC7B,kBAAU,KAAK,eAAe;AAAA,MAChC;AACA,gBAAU,OAAO,UAAU;AAC3B,gBAAU,MAAM,UAAU;AAC1B,gBAAU,QAAQ;AAClB,gBAAU,SAAS;AACnB,gBAAU,WAAW;AACrB,gBAAU,QAAQ;AAClB,WAAK,YAAY,WAAY;AAC3B,eAAO,UAAU;AACjB,YAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,sBAAY,SAAS,cAAc,QAAQ,QAAQ;AAAA,YACjD,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,sBAAc,SAAS,cAAc;AAAA,UACnC;AAAA,UACA;AAAA,UACA,eAAe;AAAA,QACjB,GAAG;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AACD,UAAI,aAAa;AACf,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAMA,MAAM,SAAS,OAAO;AACpB,QAAI,SAAS;AACb,QAAI,aAAa,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACrF,QAAI,CAAC,KAAK,WAAW,KAAK,QAAQ;AAChC,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,UAAU,MAAM,OAAO;AAChD,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,SAAS,KAAK,UAAU,KAAK,IAAI;AACrC,QAAI,OAAO,CAAC;AACZ,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,QAAI,YAAY;AACd,WAAK,kBAAkB,UAAU;AAAA,IACnC;AACA,aAAS,QAAQ,UAAU;AAC3B,YAAQ,KAAK,OAAO,SAAU,MAAM,GAAG;AACrC,UAAI,MAAM,KAAK,cAAc,KAAK;AAClC,UAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,YAAM,MAAM,QAAQ,KAAK,aAAa;AACtC,YAAM,MAAM,IAAI,aAAa,KAAK;AAClC,YAAM,iBAAiB,IAAI;AAC3B,eAAS;AACT,eAAS,OAAO,UAAU;AAC1B,kBAAY,OAAO,kBAAkB,QAAQ,UAAU;AACvD,UAAI,SAAS,MAAM,YAAY,GAAG;AAChC,iBAAS,OAAO,QAAQ;AACxB,gBAAQ;AAAA,MACV;AACA,WAAK,KAAK,KAAK;AACf,kBAAY,OAAO,YAAY,QAAQ;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B,CAAC;AACD,QAAI,SAAS,QAAQ,QAAQ,KAAK,QAAQ,WAAW,GAAG;AACtD,UAAI,QAAQ,SAASW,QAAO;AAC1B,qBAAa,OAAO,QAAQ,OAAO;AACnC,oBAAY,KAAK,QAAQ,QAAQ;AACjC,iBAAS;AACT,gBAAQ,SAAS,IAAI,QAAQ,QAAQ;AACrC,iBAAS,KAAK,QAAQ,QAAQ;AAC9B,eAAO,QAAQ,UAAU,WAAW,OAAO,QAAQ,QAAQ;AAAA,MAC7D;AACA,UAAI,QAAQ,SAASP,QAAO;AAC1B,qBAAa,OAAO,QAAQ,OAAO;AACnC,oBAAY,KAAK,QAAQ,QAAQ;AACjC,iBAAS;AACT,gBAAQ,QAAQ,QAAQ,QAAQ;AAChC,iBAAS,KAAK,QAAQ,QAAQ;AAC9B,eAAO,QAAQ,UAAU,WAAW,OAAO,QAAQ,QAAQ;AAAA,MAC7D;AACA,UAAI,QAAQ,GAAG;AACb,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,WAAW,OAAO,QAAQ,QAAQ;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS,OAAO;AACpB,QAAI,SAAS;AACb,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,MAAM;AAAA,QAC7C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,UAAU,MAAM,OAAO;AAChD,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK;AAClB,iBAAa,KAAK,QAAQ,OAAO;AACjC,SAAK,UAAU;AACf,SAAK,SAAS;AACd,YAAQ,OAAO,qBAAqB,KAAK,GAAG,SAAU,OAAO;AAC3D,qBAAe,OAAO,YAAY,OAAO,cAAc;AAAA,IACzD,CAAC;AACD,gBAAY,QAAQ,UAAU;AAC9B,WAAO,YAAY;AACnB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS,OAAO;AACpB,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK,QACd,QAAQ,KAAK,OACb,OAAO,KAAK;AACd,QAAI,CAAC,KAAK,WAAW,KAAK,UAAU,KAAK,UAAU,CAAC,QAAQ,QAAQ;AAClE,aAAO;AAAA,IACT;AACA,SAAK,SAAS;AACd,SAAK,KAAK;AACV,aAAS,KAAK,QAAQ,qBAAqB;AAC3C,QAAI,QAAQ,YAAY;AACtB,kBAAY,MAAM,gBAAgB;AAClC,UAAI,KAAK,QAAQ;AACf,oBAAY,OAAO,gBAAgB;AAAA,MACrC;AAAA,IACF;AACA,aAAS,QAAQ,WAAW;AAC5B,WAAO,aAAa,QAAQ,QAAQ;AACpC,WAAO,aAAa,mBAAmB,KAAK,MAAM,EAAE;AACpD,WAAO,aAAa,cAAc,IAAI;AACtC,WAAO,gBAAgB,OAAO;AAC9B,aAAS,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,QAAQ,OAAO;AACjB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,cAAc;AACnB,SAAK,aAAa,OAAO,CAAC,GAAG,KAAK,aAAa;AAC/C,SAAK,WAAW;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,UAAU,WAAY;AACzB,eAAO,YAAY,WAAY;AAC7B,cAAI,QAAQ,YAAY;AACtB,uBAAW,WAAY;AACrB,uBAAS,OAAO,gBAAgB;AAChC,uBAAS,MAAM,gBAAgB;AAAA,YACjC,GAAG,CAAC;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS,OAAO;AACpB,QAAI,UAAU;AACd,QAAI,UAAU,KAAK,SACjB,SAAS,KAAK,QACd,QAAQ,KAAK,OACb,OAAO,KAAK;AACd,QAAI,CAAC,KAAK,WAAW,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC,QAAQ,QAAQ;AACnE,aAAO;AAAA,IACT;AACA,SAAK,SAAS;AACd,SAAK,MAAM;AACX,gBAAY,KAAK,QAAQ,qBAAqB;AAC9C,QAAI,QAAQ,YAAY;AACtB,kBAAY,MAAM,gBAAgB;AAClC,UAAI,KAAK,QAAQ;AACf,oBAAY,OAAO,gBAAgB;AAAA,MACrC;AAAA,IACF;AACA,QAAI,QAAQ,OAAO;AACjB,WAAK,kBAAkB;AAAA,IACzB;AACA,WAAO,gBAAgB,MAAM;AAC7B,WAAO,gBAAgB,iBAAiB;AACxC,WAAO,gBAAgB,YAAY;AACnC,gBAAY,QAAQ,WAAW;AAC/B,aAAS,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,aAAa,OAAO,CAAC,GAAG,KAAK,UAAU;AAC5C,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,UAAU,WAAY;AACzB,gBAAQ,YAAY,WAAY;AAC9B,cAAI,QAAQ,YAAY;AACtB,uBAAW,WAAY;AACrB,uBAAS,OAAO,gBAAgB;AAChC,uBAAS,MAAM,gBAAgB;AAAA,YACjC,GAAG,CAAC;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,SAAS,UAAU;AAC1B,QAAI,UAAU;AACd,QAAI,UAAU,KAAK,SACjB,aAAa,KAAK,YAClB,YAAY,KAAK;AACnB,QAAI,CAAC,KAAK,UAAU,KAAK,UAAU,CAAC,QAAQ,SAAS;AACnD,aAAO;AAAA,IACT;AACA,eAAW,cAAc,GAAG,OAAO,KAAK,MAAM,UAAU,QAAQ,GAAG,GAAG,GAAG;AACzE,QAAI,CAAC,KAAK,aAAa;AACrB,UAAI,QAAQ,YAAY;AACtB,YAAI,KAAK,QAAQ;AACf,wBAAc,YAAY,oBAAoB;AAAA,QAChD;AACA,iBAAS,YAAY,UAAU;AAC/B,iBAAS,YAAY,UAAU;AAC/B,iBAAS,YAAY,gBAAgB;AACrC,mBAAW,gBAAgB,aAAa;AAGxC,mBAAW,qBAAqB,WAAW;AAC3C,iBAAS,YAAY,QAAQ;AAAA,MAC/B,OAAO;AACL,iBAAS,YAAY,UAAU;AAC/B,mBAAW,gBAAgB,aAAa;AAAA,MAC1C;AAAA,IACF,OAAO;AACL,mBAAa,KAAK,WAAW;AAAA,IAC/B;AACA,SAAK,cAAc,WAAW,WAAY;AACxC,UAAI,QAAQ,YAAY;AACtB,oBAAY,YAAY,sBAAsB,WAAY;AACxD,sBAAY,YAAY,UAAU;AAClC,sBAAY,YAAY,UAAU;AAClC,sBAAY,YAAY,gBAAgB;AACxC,qBAAW,aAAa,eAAe,IAAI;AAC3C,kBAAQ,SAAS;AAAA,QACnB,GAAG;AAAA,UACD,MAAM;AAAA,QACR,CAAC;AACD,oBAAY,YAAY,QAAQ;AAChC,gBAAQ,SAAS;AAAA,MACnB,OAAO;AACL,oBAAY,YAAY,UAAU;AAClC,mBAAW,aAAa,eAAe,IAAI;AAAA,MAC7C;AACA,cAAQ,cAAc;AAAA,IACxB,GAAG,GAAI;AACP,WAAO;AAAA,EACT;AAAA,EAMA,QAAQ,SAAS,SAAS;AACxB,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK;AACzF,QAAI,KAAK,UAAU,UAAU,GAAG;AAC9B,WAAK,OAAO,KAAK,UAAU,UAAU,MAAM,MAAM,cAAc;AAAA,IACjE,OAAO;AACL,WAAK,OAAO,GAAG,MAAM,MAAM,cAAc;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAS,QAAQ;AACtB,QAAI,KAAK,UAAU,CAAC,KAAK,QAAQ;AAC/B,WAAK,YAAY,OAAO,CAAC,GAAG,KAAK,gBAAgB;AACjD,WAAK,YAAY;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,SAAS;AACxB,QAAI,UAAU;AACd,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,QAAQ,KAAK;AAGf,QAAI,SAAS,CAAC,QAAQ,YAAY;AAChC,aAAO,KAAK,QAAQ;AAAA,IACtB;AACA,QAAI,SAAS,CAAC;AACd,YAAQ,QAAQ,CAAC,OAAO,IAAI,QAAQ,iBAAiB,KAAK,GAAG,SAAU,OAAO;AAC5E,UAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,YAAI,QAAQ,OAAO,KAAK,SAAS,KAAK,GAAG;AACvC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,WAAW,QAAQ,YAAY,KAAK,GAAG;AACrC,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF,CAAC;AACD,QAAI,CAAC,OAAO,QAAQ;AAClB,aAAO;AAAA,IACT;AACA,SAAK,SAAS;AACd,SAAK,SAAS,OAAO;AACrB,QAAI,KAAK,OAAO;AACd,UAAI,iBAAiB,CAAC;AACtB,cAAQ,KAAK,OAAO,SAAU,MAAM,GAAG;AACrC,YAAI,MAAM,KAAK,cAAc,KAAK;AAClC,YAAI,QAAQ,OAAO;AACnB,YAAI,SAAS,KAAK;AAChB,cAAI,MAAM,QAAQ,IAAI,OAGnB,MAAM,QAAQ,IAAI,KAAK;AACxB,2BAAe,KAAK,CAAC;AAAA,UACvB;AAAA,QACF,OAAO;AACL,yBAAe,KAAK,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AACD,eAAS,KAAK,MAAM;AAAA,QAClB,OAAO;AAAA,MACT,CAAC;AACD,WAAK,SAAS;AACd,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,QAAQ;AACf,cAAI,KAAK,QAAQ;AACf,gBAAI,eAAe,eAAe,QAAQ,KAAK,KAAK;AACpD,gBAAI,gBAAgB,GAAG;AACrB,mBAAK,SAAS;AACd,mBAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,cAAc,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC;AAAA,YAC7E,OAAO;AACL,kBAAI,aAAa,KAAK,MAAM,KAAK;AAGjC,uBAAS,YAAY,YAAY;AACjC,yBAAW,aAAa,iBAAiB,IAAI;AAAA,YAC/C;AAAA,UACF;AAAA,QACF,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AACb,eAAK,YAAY,CAAC;AAClB,eAAK,OAAO,YAAY;AACxB,eAAK,MAAM,YAAY;AAAA,QACzB;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,SAAS,UAAU;AAC1B,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,QAAI,CAAC,QAAQ,YAAY;AACvB,aAAO;AAAA,IACT;AACA,SAAK,YAAY;AACjB,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,QAAQ;AACf,aAAK,KAAK;AAAA,MACZ;AACA,UAAI,QAAQ,QAAQ;AAClB,YAAI,KAAK,QAAQ;AACf,eAAK,KAAK;AAAA,QACZ;AACA,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,SAAS;AACvB,YAAI,KAAK,SAAS;AAChB,cAAI,KAAK,gBAAgB;AACvB,iBAAK,eAAe,MAAM;AAAA,UAC5B,WAAW,KAAK,mBAAmB;AACjC,iBAAK,kBAAkB,MAAM;AAAA,UAC/B;AAAA,QACF;AACA,YAAI,KAAK,QAAQ;AACf,eAAK,cAAc,MAAM;AAAA,QAC3B;AACA,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,SAAS;AACvB,aAAK,cAAc,MAAM;AACzB,aAAK,OAAO;AAAA,MACd;AACA,WAAK,QAAQ;AACb,WAAK,OAAO,WAAW,YAAY,KAAK,MAAM;AAAA,IAChD,WAAW,QAAQ,QAAQ;AACzB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,MAAM;AAAA,MACtB,WAAW,KAAK,cAAc;AAC5B,aAAK,aAAa,MAAM;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,QAAQ;AACnB,qBAAe,SAAS,aAAa,KAAK,OAAO;AAAA,IACnD;AACA,YAAQ,aAAa;AACrB,WAAO;AAAA,EACT;AACF;AAEA,IAAI,SAAS;AAAA,EACX,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,MAAM,KAAK,QAAQ;AACvB,QAAI,SAAS,GAAG,GAAG;AACjB,YAAM,MAAM,aAAa,GAAG;AAAA,IAC9B,WAAW,WAAW,GAAG,GAAG;AAC1B,YAAM,IAAI,KAAK,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,QAAI,QAAQ;AACZ,SAAK,kBAAkB;AACvB,gBAAY,UAAU,eAAe,KAAK,YAAY,SAAU,OAAO;AACrE,UAAI,SAAS,MAAM;AACnB,UAAI,SAAS,MAAM;AACnB,UAAI,WAAW,YAAY,WAAW,UAAU,OAAO,SAAS,MAAM,GAAG;AACvE;AAAA,MACF;AACA,aAAO,QAAQ;AAEb,YAAI,OAAO,aAAa,UAAU,MAAM,QAAQ,OAAO,aAAa,YAAY,MAAM,QAAQ;AAC5F;AAAA,QACF;AACA,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,oBAAoB;AAC9C,QAAI,KAAK,WAAW;AAClB,qBAAe,UAAU,eAAe,KAAK,SAAS;AACtD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,QAAI,OAAO,KAAK;AAChB,aAAS,MAAM,UAAU;AACzB,QAAI,KAAK,iBAAiB,GAAG;AAC3B,WAAK,MAAM,eAAe,GAAG,OAAO,KAAK,kBAAkB,WAAW,KAAK,+BAA+B,KAAK,IAAI,IAAI;AAAA,IACzH;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,OAAO,KAAK;AAChB,gBAAY,MAAM,UAAU;AAC5B,QAAI,KAAK,iBAAiB,GAAG;AAC3B,WAAK,MAAM,eAAe,KAAK;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,SAAS,KAAK;AAChB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AACf,QAAI,QAAQ,OAAO;AACjB,aAAO,MAAM;AACb,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,kBAAY,SAAS,aAAa,QAAQ,OAAO;AAAA,QAC/C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,cAAc,SAAS,WAAW,MAAM,OAAO;AACjD;AAAA,IACF;AACA,QAAI,KAAK,SAAS,KAAK,WAAW,CAAC,KAAK,QAAQ;AAC9C,WAAK,KAAK,KAAK,KAAK;AAAA,IACtB;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,SAAS,KAAK;AAChB,QAAI,QAAQ,OAAO;AACjB,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,aAAS,QAAQ,UAAU;AAC3B,WAAO,gBAAgB,MAAM;AAC7B,WAAO,gBAAgB,iBAAiB;AACxC,WAAO,gBAAgB,YAAY;AACnC,WAAO,aAAa,eAAe,IAAI;AACvC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,QAAI,CAAC,KAAK,WAAW;AACnB,UAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,oBAAY,SAAS,cAAc,QAAQ,QAAQ;AAAA,UACjD,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,oBAAc,SAAS,cAAc,MAAM;AAAA,QACzC,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmB,SAAS,kBAAkB,SAAS;AACrD,QAAIE,YAAW,KAAK,QAAQ;AAC5B,QAAI,KAAK,UAAU,EAAEA,UAAS,qBAAqBA,UAAS,2BAA2BA,UAAS,wBAAwBA,UAAS,sBAAsB;AACrJ,UAAI,kBAAkBA,UAAS;AAG/B,UAAI,gBAAgB,mBAAmB;AAErC,YAAI,cAAc,OAAO,GAAG;AAC1B,0BAAgB,kBAAkB,OAAO;AAAA,QAC3C,OAAO;AACL,0BAAgB,kBAAkB;AAAA,QACpC;AAAA,MACF,WAAW,gBAAgB,yBAAyB;AAClD,wBAAgB,wBAAwB,QAAQ,oBAAoB;AAAA,MACtE,WAAW,gBAAgB,sBAAsB;AAC/C,wBAAgB,qBAAqB;AAAA,MACvC,WAAW,gBAAgB,qBAAqB;AAC9C,wBAAgB,oBAAoB;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,SAAS,iBAAiB;AACxC,QAAIA,YAAW,KAAK,QAAQ;AAC5B,QAAI,KAAK,WAAWA,UAAS,qBAAqBA,UAAS,2BAA2BA,UAAS,wBAAwBA,UAAS,sBAAsB;AAEpJ,UAAIA,UAAS,gBAAgB;AAC3B,QAAAA,UAAS,eAAe;AAAA,MAC1B,WAAWA,UAAS,sBAAsB;AACxC,QAAAA,UAAS,qBAAqB;AAAA,MAChC,WAAWA,UAAS,qBAAqB;AACvC,QAAAA,UAAS,oBAAoB;AAAA,MAC/B,WAAWA,UAAS,kBAAkB;AACpC,QAAAA,UAAS,iBAAiB;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,QAAI,UAAU,KAAK,SACjB,WAAW,KAAK;AAClB,QAAI,UAAU,SAAS,OAAO,KAAK,QAAQ,EAAE;AAG7C,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,UAAU,QAAQ,OAAO,QAAQ;AACrC,QAAI,UAAU,QAAQ,OAAO,QAAQ;AACrC,YAAQ,KAAK;AAAA,WAEN;AACH,YAAI,YAAY,KAAK,YAAY,GAAG;AAClC,eAAK,eAAe;AACpB,eAAK,KAAK,SAAS,SAAS,KAAK;AAAA,QACnC;AACA;AAAA,WAGG;AACH,aAAK,KAAK,gBAAgB,QAAQ,GAAG,OAAO,MAAM,KAAK;AACvD;AAAA,WACG,eACH;AACE,aAAK,SAAS;AACd,YAAI,kBAAkB,KAAK,IAAI,OAAO;AACtC,YAAI,kBAAkB,KAAK,kBAAkB,KAAK,IAAI,OAAO,GAAG;AAE9D,eAAK,WAAW,CAAC;AACjB,cAAI,UAAU,GAAG;AACf,iBAAK,KAAK,QAAQ,IAAI;AAAA,UACxB,WAAW,UAAU,IAAI;AACvB,iBAAK,KAAK,QAAQ,IAAI;AAAA,UACxB;AAAA,QACF;AACA;AAAA,MACF;AAAA;AAIJ,YAAQ,UAAU,SAAU,GAAG;AAC7B,QAAE,SAAS,EAAE;AACb,QAAE,SAAS,EAAE;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,QAAI,YAAY,KAAK,WACnB,aAAa,KAAK;AACpB,WAAO,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,KAAK,UAAU,SAAS,WAAW,SAAS,UAAU,UAAU,WAAW;AAAA,EAC1I;AACF;AAEA,IAAI,gBAAgB,OAAO;AAC3B,IAAI,cAAc,SAAU,IAAI;AAC9B,SAAO,WAAY;AACjB,UAAM;AACN,WAAO;AAAA,EACT;AACF,EAAE,EAAE;AACJ,IAAI,SAAsB,WAAY;AAMpC,WAASM,QAAO,SAAS;AACvB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,OAAO,SAAY,UAAU,KAAK,CAAC;AACnF,oBAAgB,MAAMA,OAAM;AAC5B,QAAI,CAAC,WAAW,QAAQ,aAAa,GAAG;AACtC,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,SAAK,UAAU;AACf,SAAK,UAAU,OAAO,CAAC,GAAG,UAAU,cAAc,OAAO,KAAK,OAAO;AACrE,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,YAAY,CAAC;AAClB,SAAK,QAAQ,KAAK,QAAQ;AAC1B,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,KAAK,YAAY;AACtB,SAAK,KAAK;AAAA,EACZ;AACA,SAAO,aAAaA,SAAQ,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,UAAI,QAAQ;AACZ,UAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,UAAI,QAAQ,YAAY;AACtB;AAAA,MACF;AACA,cAAQ,aAAa;AAGrB,UAAI,QAAQ,SAAS,CAAC,QAAQ,UAAU;AACtC,gBAAQ,QAAQ;AAAA,MAClB;AACA,UAAI,QAAQ,QAAQ,cAAc;AAClC,UAAI,SAAS,CAAC;AACd,cAAQ,QAAQ,CAAC,OAAO,IAAI,QAAQ,iBAAiB,KAAK,GAAG,SAAU,OAAO;AAC5E,YAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,cAAI,QAAQ,OAAO,KAAK,OAAO,KAAK,GAAG;AACrC,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF,WAAW,MAAM,YAAY,KAAK,GAAG;AACnC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AACD,WAAK,QAAQ;AACb,WAAK,SAAS,OAAO;AACrB,WAAK,SAAS;AACd,WAAK,SAAS;AAGd,UAAI,YAAY,SAAS,cAAc,SAAS,EAAE,MAAM,UAAU,GAAG;AACnE,gBAAQ,aAAa;AAAA,MACvB;AACA,UAAI,QAAQ,QAAQ;AAClB,YAAI,QAAQ;AACZ,YAAI,WAAW,SAASC,YAAW;AACjC,mBAAS;AACT,cAAI,UAAU,MAAM,QAAQ;AAC1B,gBAAI;AACJ,kBAAM,eAAe;AACrB,kBAAM,WAAW;AAAA,cACf,OAAO,SAAS,QAAQ;AACtB,6BAAa,OAAO;AAAA,cACtB;AAAA,YACF;AAGA,sBAAU,WAAW,WAAY;AAC/B,oBAAM,WAAW;AACjB,oBAAM,MAAM;AAAA,YACd,GAAG,CAAC;AAAA,UACN;AAAA,QACF;AACA,aAAK,eAAe;AAAA,UAClB,OAAO,SAAS,QAAQ;AACtB,oBAAQ,QAAQ,SAAU,OAAO;AAC/B,kBAAI,CAAC,MAAM,UAAU;AACnB,+BAAe,OAAO,YAAY,QAAQ;AAC1C,+BAAe,OAAO,aAAa,QAAQ;AAAA,cAC7C;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,gBAAQ,QAAQ,SAAU,OAAO;AAC/B,cAAI,MAAM,UAAU;AAClB,qBAAS;AAAA,UACX,OAAO;AACL,gBAAI;AACJ,gBAAI;AACJ,wBAAY,OAAO,YAAY,SAAS,SAASX,UAAS;AACxD,6BAAe,OAAO,aAAa,OAAO;AAC1C,uBAAS;AAAA,YACX,GAAG;AAAA,cACD,MAAM;AAAA,YACR,CAAC;AACD,wBAAY,OAAO,aAAa,UAAU,SAASC,WAAU;AAC3D,6BAAe,OAAO,YAAY,MAAM;AACxC,uBAAS;AAAA,YACX,GAAG;AAAA,cACD,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,oBAAY,SAAS,aAAa,KAAK,UAAU,SAAU,MAAM;AAC/D,cAAI,SAAS,KAAK;AAClB,cAAI,OAAO,cAAc,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK,QAAQ,OAAO,KAAK,OAAO,MAAM,IAAI;AACrG,kBAAM,KAAK,MAAM,OAAO,QAAQ,MAAM,CAAC;AAAA,UACzC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,KAAK,OAAO;AACd;AAAA,MACF;AACA,UAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,UAAI,SAAS,QAAQ;AACrB,UAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,eAAS,YAAY;AACrB,UAAI,SAAS,SAAS,cAAc,IAAI,OAAO,WAAW,YAAY,CAAC;AACvE,UAAI,QAAQ,OAAO,cAAc,IAAI,OAAO,WAAW,QAAQ,CAAC;AAChE,UAAI,UAAU,OAAO,cAAc,IAAI,OAAO,WAAW,UAAU,CAAC;AACpE,UAAI,SAAS,OAAO,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AAClE,UAAI,SAAS,OAAO,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AAClE,UAAI,SAAS,OAAO,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AAClE,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,SAAS,OAAO,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AACnE,WAAK,aAAa,OAAO,cAAc,IAAI,OAAO,WAAW,UAAU,CAAC;AACxE,WAAK,SAAS,OAAO,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AACnE,WAAK,OAAO,OAAO,cAAc,IAAI,OAAO,WAAW,OAAO,CAAC;AAC/D,aAAO,KAAK,GAAG,OAAO,SAAS,EAAE,OAAO,KAAK,EAAE;AAC/C,YAAM,KAAK,GAAG,OAAO,WAAW,OAAO,EAAE,OAAO,KAAK,EAAE;AACvD,eAAS,OAAO,CAAC,QAAQ,QAAQ,aAAa,mBAAmB,MAAM,QAAQ,QAAQ,KAAK,IAAI,QAAQ,MAAM,KAAK,QAAQ,KAAK,CAAC;AACjI,eAAS,QAAQ,CAAC,QAAQ,SAAS,aAAa,mBAAmB,QAAQ,MAAM,CAAC;AAClF,kBAAY,QAAQ,YAAY,CAAC,QAAQ,MAAM;AAC/C,UAAI,QAAQ,UAAU;AACpB,eAAO,aAAa,YAAY,CAAC;AAAA,MACnC;AACA,UAAI,QAAQ,UAAU;AACpB,iBAAS,QAAQ,GAAG,OAAO,WAAW,WAAW,CAAC;AAClD,YAAI,CAAC,QAAQ,UAAU,QAAQ,aAAa,UAAU;AACpD,kBAAQ,QAAQ,aAAa,MAAM;AAAA,QACrC;AAAA,MACF;AACA,UAAI,SAAS,QAAQ,SAAS,KAAK,QAAQ,WAAW;AAEpD,gBAAQ,UAAU,MAAM,aAAa,EAAE,QAAQ,SAAU,WAAW;AAClE,mBAAS,QAAQ,SAAS;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,QAAQ,SAAS;AACnB,YAAI,OAAO,SAAS,cAAc,IAAI;AACtC,YAAI,SAAS,cAAc,QAAQ,OAAO;AAC1C,YAAI,cAAc,QAAQ,MAAM,GAAG,CAAC;AACpC,YAAI,gBAAgB,QAAQ,MAAM,GAAG,CAAC;AACtC,YAAI,eAAe,QAAQ,MAAM,CAAC;AAClC,YAAI,CAAC,QAAQ;AACX,mBAAS,SAAS,mBAAmB,QAAQ,OAAO,CAAC;AAAA,QACvD;AACA,gBAAQ,SAAS,QAAQ,UAAU,SAAS,SAAU,OAAO,OAAO;AAClE,cAAI,OAAO,UAAU,cAAc,KAAK;AACxC,cAAI,OAAO,SAAS,UAAU,KAAK,IAAI;AACvC,cAAIW,QAAO,QAAQ,CAAC,YAAY,MAAM,IAAI,IAAI,MAAM,OAAO;AAC3D,cAAI,CAACA,SAAQ,CAAC,QAAQ,YAAY,YAAY,QAAQ,IAAI,MAAM,MAAM,CAAC,QAAQ,aAAa,cAAc,QAAQ,IAAI,MAAM,MAAM,CAAC,QAAQ,YAAY,aAAa,QAAQ,IAAI,MAAM,IAAI;AACxL;AAAA,UACF;AACA,cAAI,OAAO,QAAQ,CAAC,YAAY,MAAM,IAAI,IAAI,MAAM,OAAO;AAC3D,cAAIC,SAAQ,QAAQ,CAAC,YAAY,MAAM,KAAK,IAAI,MAAM,QAAQ;AAC9D,cAAI,OAAO,SAAS,cAAc,IAAI;AACtC,cAAI,QAAQ,UAAU;AACpB,iBAAK,aAAa,YAAY,CAAC;AAAA,UACjC;AACA,eAAK,aAAa,QAAQ,QAAQ;AAClC,mBAAS,MAAM,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,IAAI,CAAC;AACrD,cAAI,CAAC,WAAWA,MAAK,GAAG;AACtB,oBAAQ,MAAM,aAAa,IAAI;AAAA,UACjC;AACA,cAAI,SAASD,KAAI,GAAG;AAClB,qBAAS,MAAM,mBAAmBA,KAAI,CAAC;AAAA,UACzC;AACA,cAAI,CAAC,SAAS,OAAO,EAAE,QAAQ,IAAI,MAAM,IAAI;AAC3C,qBAAS,MAAM,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,IAAI,CAAC;AAAA,UACvD,WAAW,SAAS,QAAQ;AAC1B,qBAAS,MAAM,GAAG,OAAO,WAAW,QAAQ,CAAC;AAAA,UAC/C;AACA,cAAI,WAAWC,MAAK,GAAG;AACrB,wBAAY,MAAM,aAAaA,MAAK;AAAA,UACtC;AACA,eAAK,YAAY,IAAI;AAAA,QACvB,CAAC;AACD,gBAAQ,YAAY,IAAI;AAAA,MAC1B,OAAO;AACL,iBAAS,SAAS,UAAU;AAAA,MAC9B;AACA,UAAI,CAAC,QAAQ,WAAW;AACtB,YAAI,UAAU,QAAQ,iBAAiB,qBAAqB;AAC5D,iBAAS,SAAS,eAAe;AACjC,gBAAQ,SAAS,SAAUjB,SAAQ;AACjC,kBAAQ,YAAYA,OAAM;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,QAAQ,QAAQ;AAClB,iBAAS,QAAQ,gBAAgB;AACjC,iBAAS,QAAQ;AAAA,UACf,QAAQ,QAAQ;AAAA,QAClB,CAAC;AACD,YAAI,OAAO,iBAAiB,MAAM,EAAE,aAAa,UAAU;AACzD,mBAAS,QAAQ;AAAA,YACf,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,eAAO,aAAa,QAAQ,QAAQ,WAAW;AAAA,MACjD,OAAO;AACL,iBAAS,QAAQ,WAAW;AAC5B,iBAAS,QAAQ,WAAW;AAC5B,iBAAS,QAAQ,UAAU;AAC3B,iBAAS,QAAQ,UAAU;AAC3B,iBAAS,QAAQ;AAAA,UACf,QAAQ,QAAQ;AAAA,QAClB,CAAC;AACD,YAAI,YAAY,QAAQ;AACxB,YAAI,SAAS,SAAS,GAAG;AACvB,sBAAY,QAAQ,cAAc,cAAc,SAAS;AAAA,QAC3D;AACA,YAAI,CAAC,WAAW;AACd,sBAAY,KAAK;AAAA,QACnB;AACA,kBAAU,YAAY,MAAM;AAAA,MAC9B;AACA,UAAI,QAAQ,QAAQ;AAClB,aAAK,OAAO;AACZ,aAAK,KAAK;AACV,aAAK,UAAU;AAAA,MACjB;AACA,WAAK,QAAQ;AACb,UAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,oBAAY,SAAS,aAAa,QAAQ,OAAO;AAAA,UAC/C,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,cAAc,SAAS,WAAW,MAAM,OAAO;AACjD,aAAK,QAAQ;AACb;AAAA,MACF;AACA,UAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,aAAK,KAAK,KAAK,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EAMF,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,SAAS;AAChB,aAAOc;AAAA,IACT;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,SAAS;AACnC,aAAO,UAAU,cAAc,OAAO,KAAK,OAAO;AAAA,IACpD;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,OAAO,OAAO,WAAW,QAAQ,QAAQ,UAAU,SAAS,MAAM;;;ACjpGP,IAAO,mBAAQ;", "names": ["r", "o", "assign", "listener", "rotate", "scaleX", "scaleY", "render", "onLoad", "onError", "next", "onTransitionEnd", "document", "shown", "hideImmediately", "onImageTransitionEnd", "onViewed", "prev", "Viewer", "progress", "show", "click"]}