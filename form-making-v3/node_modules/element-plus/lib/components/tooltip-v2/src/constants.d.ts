import type { InjectionK<PERSON>, Ref } from 'vue';
import type { UseNamespaceReturn } from 'element-plus/es/hooks';
export type TooltipV2Context = {
    onClose: () => void;
    onDelayOpen: () => void;
    onOpen: () => void;
    contentId: Ref<string>;
    triggerRef: Ref<HTMLElement | null>;
    ns: UseNamespaceReturn;
};
export type TooltipV2ContentContext = {
    arrowRef: Ref<HTMLElement | null>;
};
export declare const tooltipV2RootKey: InjectionKey<TooltipV2Context>;
export declare const tooltipV2ContentKey: InjectionKey<TooltipV2ContentContext>;
export declare const TOOLTIP_V2_OPEN = "tooltip_v2.open";
