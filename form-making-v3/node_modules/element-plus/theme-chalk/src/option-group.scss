@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(select-group) {
  $gap: 20px;

  margin: 0;
  padding: 0;

  @include e(wrap) {
    position: relative;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  @include e(title) {
    box-sizing: border-box;
    padding: 0 $gap;
    font-size: map.get($select-group, 'font-size');
    color: map.get($select-group, 'text-color');
    line-height: map.get($select-group, 'height');
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  & .#{$namespace}-select-dropdown__item {
    padding-left: $gap;
  }
}
