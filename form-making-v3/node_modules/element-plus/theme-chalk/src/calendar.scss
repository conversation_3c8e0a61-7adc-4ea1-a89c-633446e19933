@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(calendar) {
  @include set-component-css-var('calendar', $calendar);

  background-color: getCssVar('fill-color', 'blank');

  @include e(header) {
    display: flex;
    justify-content: space-between;
    padding: 12px 20px;
    border-bottom: getCssVar('calendar', 'header-border-bottom');
  }

  @include e(title) {
    color: getCssVar('text-color');
    align-self: center;
  }

  @include e(body) {
    padding: 12px 20px 35px;
  }
}

@include b(calendar-table) {
  table-layout: fixed;
  width: 100%;

  thead th {
    padding: 12px 0;
    color: getCssVar('text-color', 'regular');
    font-weight: normal;
  }

  &:not(.is-range) {
    td.prev,
    td.next {
      color: getCssVar('text-color', 'placeholder');
    }
  }

  td {
    border-bottom: getCssVar('calendar', 'border');
    border-right: getCssVar('calendar', 'border');
    vertical-align: top;
    transition: background-color getCssVar('transition-duration', 'fast') ease;

    @include when(selected) {
      background-color: getCssVar('calendar', 'selected-bg-color');
    }

    @include when(today) {
      color: getCssVar('color-primary');
    }
  }

  tr:first-child td {
    border-top: getCssVar('calendar', 'border');
  }

  tr td:first-child {
    border-left: getCssVar('calendar', 'border');
  }

  tr.#{$namespace}-calendar-table__row--hide-border td {
    border-top: none;
  }

  @include b(calendar-day) {
    box-sizing: border-box;
    padding: 8px;
    height: getCssVar('calendar', 'cell-width');
    &:hover {
      cursor: pointer;
      background-color: getCssVar('calendar', 'selected-bg-color');
    }
  }
}
