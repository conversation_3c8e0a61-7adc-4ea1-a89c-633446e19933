/*! Element Plus v2.10.3 */

var sk = {
  name: "sk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "<PERSON><PERSON>za\u0165"
    },
    datepicker: {
      now: "Teraz",
      today: "Dnes",
      cancel: "<PERSON>ru\u0161i\u0165",
      clear: "Zmaza\u0165",
      confirm: "OK",
      selectDate: "V<PERSON>bra\u0165 d\xE1tum",
      selectTime: "Vybra\u0165 \u010Das",
      startDate: "D\xE1tum za\u010Diatku",
      startTime: "\u010Cas za\u010Diatku",
      endDate: "D\xE1tum konca",
      endTime: "\u010Cas konca",
      prevYear: "Predo\u0161l\xFD rok",
      nextYear: "\u010Eal\u0161\xED rok",
      prevMonth: "Predo\u0161l\xFD mesiac",
      nextMonth: "\u010Eal\u0161\xED mesiac",
      day: "De\u0148",
      week: "T\xFD\u017Ede\u0148",
      month: "Mesiac",
      year: "Rok",
      month1: "Janu\xE1r",
      month2: "Febru\xE1r",
      month3: "Marec",
      month4: "Apr\xEDl",
      month5: "M\xE1j",
      month6: "J\xFAn",
      month7: "J\xFAl",
      month8: "August",
      month9: "September",
      month10: "Okt\xF3ber",
      month11: "November",
      month12: "December",
      weeks: {
        sun: "Ne",
        mon: "Po",
        tue: "Ut",
        wed: "St",
        thu: "\u0160t",
        fri: "Pi",
        sat: "So"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "M\xE1j",
        jun: "J\xFAn",
        jul: "J\xFAl",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Na\u010D\xEDtavanie",
      noMatch: "\u017Diadna zhoda",
      noData: "\u017Diadne d\xE1ta",
      placeholder: "Vybra\u0165"
    },
    mention: {
      loading: "Na\u010D\xEDtavanie"
    },
    cascader: {
      noMatch: "\u017Diadna zhoda",
      loading: "Na\u010D\xEDtavanie",
      placeholder: "Vybra\u0165",
      noData: "\u017Diadne d\xE1ta"
    },
    pagination: {
      goto: "Cho\u010F na",
      pagesize: "na stranu",
      total: "V\u0161etko {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Spr\xE1va",
      confirm: "OK",
      cancel: "Zru\u0161i\u0165",
      error: "Neplatn\xFD vstup"
    },
    upload: {
      deleteTip: "pre odstr\xE1nenie stisni kl\xE1vesu Delete",
      delete: "Vymaza\u0165",
      preview: "Prehliada\u0165",
      continue: "Pokra\u010Dova\u0165"
    },
    table: {
      emptyText: "\u017Diadne d\xE1ta",
      confirmFilter: "Potvrdi\u0165",
      resetFilter: "Zresetova\u0165",
      clearFilter: "V\u0161etko",
      sumText: "Spolu"
    },
    tree: {
      emptyText: "\u017Diadne d\xE1ta"
    },
    transfer: {
      noMatch: "\u017Diadna zhoda",
      noData: "\u017Diadne d\xE1ta",
      titles: ["Zoznam 1", "Zoznam 2"],
      filterPlaceholder: "Filtrova\u0165 pod\u013Ea",
      noCheckedFormat: "{total} polo\u017Eiek",
      hasCheckedFormat: "{checked}/{total} ozna\u010Den\xFDch"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { sk as default };
