/*! Element Plus v2.10.3 */

var ru = {
  name: "ru",
  el: {
    breadcrumb: {
      label: "\u0425\u043B\u0435\u0431\u043D\u044B\u0435 \u043A\u0440\u043E\u0448\u043A\u0438"
    },
    colorpicker: {
      confirm: "\u041E\u043A",
      clear: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C"
    },
    datepicker: {
      now: "\u0421\u0435\u0439\u0447\u0430\u0441",
      today: "\u0421\u0435\u0433\u043E\u0434\u043D\u044F",
      cancel: "\u041E\u0442\u043C\u0435\u043D\u0430",
      clear: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
      confirm: "\u041E\u043A",
      selectDate: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0434\u0430\u0442\u0443",
      selectTime: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0432\u0440\u0435\u043C\u044F",
      startDate: "\u0414\u0430\u0442\u0430 \u043D\u0430\u0447\u0430\u043B\u0430",
      startTime: "\u0412\u0440\u0435\u043C\u044F \u043D\u0430\u0447\u0430\u043B\u0430",
      endDate: "\u0414\u0430\u0442\u0430 \u043E\u043A\u043E\u043D\u0447\u0430\u043D\u0438\u044F",
      endTime: "\u0412\u0440\u0435\u043C\u044F \u043E\u043A\u043E\u043D\u0447\u0430\u043D\u0438\u044F",
      prevYear: "\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439 \u0433\u043E\u0434",
      nextYear: "\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439 \u0433\u043E\u0434",
      prevMonth: "\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439 \u043C\u0435\u0441\u044F\u0446",
      nextMonth: "\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439 \u043C\u0435\u0441\u044F\u0446",
      year: "",
      month1: "\u042F\u043D\u0432\u0430\u0440\u044C",
      month2: "\u0424\u0435\u0432\u0440\u0430\u043B\u044C",
      month3: "\u041C\u0430\u0440\u0442",
      month4: "\u0410\u043F\u0440\u0435\u043B\u044C",
      month5: "\u041C\u0430\u0439",
      month6: "\u0418\u044E\u043D\u044C",
      month7: "\u0418\u044E\u043B\u044C",
      month8: "\u0410\u0432\u0433\u0443\u0441\u0442",
      month9: "\u0421\u0435\u043D\u0442\u044F\u0431\u0440\u044C",
      month10: "\u041E\u043A\u0442\u044F\u0431\u0440\u044C",
      month11: "\u041D\u043E\u044F\u0431\u0440\u044C",
      month12: "\u0414\u0435\u043A\u0430\u0431\u0440\u044C",
      week: "\u043D\u0435\u0434\u0435\u043B\u044F",
      weeks: {
        sun: "\u0412\u0441",
        mon: "\u041F\u043D",
        tue: "\u0412\u0442",
        wed: "\u0421\u0440",
        thu: "\u0427\u0442",
        fri: "\u041F\u0442",
        sat: "\u0421\u0431"
      },
      months: {
        jan: "\u042F\u043D\u0432",
        feb: "\u0424\u0435\u0432",
        mar: "\u041C\u0430\u0440",
        apr: "\u0410\u043F\u0440",
        may: "\u041C\u0430\u0439",
        jun: "\u0418\u044E\u043D",
        jul: "\u0418\u044E\u043B",
        aug: "\u0410\u0432\u0433",
        sep: "\u0421\u0435\u043D",
        oct: "\u041E\u043A\u0442",
        nov: "\u041D\u043E\u044F",
        dec: "\u0414\u0435\u043A"
      }
    },
    select: {
      loading: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430",
      noMatch: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0439 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u043E",
      noData: "\u041D\u0435\u0442 \u0434\u0430\u043D\u043D\u044B\u0445",
      placeholder: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C"
    },
    mention: {
      loading: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430"
    },
    cascader: {
      noMatch: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0439 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u043E",
      loading: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430",
      placeholder: "\u0412\u044B\u0431\u0440\u0430\u0442\u044C",
      noData: "\u041D\u0435\u0442 \u0434\u0430\u043D\u043D\u044B\u0445"
    },
    pagination: {
      goto: "\u041F\u0435\u0440\u0435\u0439\u0442\u0438",
      pagesize: " \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0435",
      total: "\u0412\u0441\u0435\u0433\u043E {total}",
      pageClassifier: "",
      page: "\u0421\u0442\u0440\u0430\u043D\u0438\u0446\u0430",
      prev: "\u041F\u0435\u0440\u0435\u0439\u0442\u0438 \u043D\u0430 \u043F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0443\u044E \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443",
      next: "\u041F\u0435\u0440\u0435\u0439\u0442\u0438 \u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443",
      currentPage: "\u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430 {pager}",
      prevPages: "\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0435 {pager} \u0441\u0442\u0440\u0430\u043D\u0438\u0446",
      nextPages: "\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0435 {pager} \u0441\u0442\u0440\u0430\u043D\u0438\u0446"
    },
    messagebox: {
      title: "\u0421\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u0435",
      confirm: "\u041E\u043A",
      cancel: "\u041E\u0442\u043C\u0435\u043D\u0430",
      error: "\u041D\u0435\u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0439 \u0432\u0432\u043E\u0434 \u0434\u0430\u043D\u043D\u044B\u0445"
    },
    upload: {
      deleteTip: "\u041D\u0430\u0436\u043C\u0438\u0442\u0435 [\u0423\u0434\u0430\u043B\u0438\u0442\u044C] \u0434\u043B\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
      delete: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
      preview: "\u041F\u0440\u0435\u0432\u044C\u044E",
      continue: "\u041F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C"
    },
    table: {
      emptyText: "\u041D\u0435\u0442 \u0434\u0430\u043D\u043D\u044B\u0445",
      confirmFilter: "\u041E\u043A",
      resetFilter: "\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C",
      clearFilter: "\u0412\u0441\u0435",
      sumText: "\u0421\u0443\u043C\u043C\u0430"
    },
    tour: {
      next: "\u0414\u0430\u043B\u0435\u0435",
      previous: "\u041D\u0430\u0437\u0430\u0434",
      finish: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C"
    },
    tree: {
      emptyText: "\u041D\u0435\u0442 \u0434\u0430\u043D\u043D\u044B\u0445"
    },
    transfer: {
      noMatch: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0439 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u043E",
      noData: "\u041D\u0435\u0442 \u0434\u0430\u043D\u043D\u044B\u0445",
      titles: ["\u0421\u043F\u0438\u0441\u043E\u043A 1", "\u0421\u043F\u0438\u0441\u043E\u043A 2"],
      filterPlaceholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043A\u043B\u044E\u0447\u0435\u0432\u043E\u0435 \u0441\u043B\u043E\u0432\u043E",
      noCheckedFormat: "{total} \u043F\u0443\u043D\u043A\u0442\u043E\u0432",
      hasCheckedFormat: "{checked}/{total} \u0432\u044B\u0431\u0440\u0430\u043D\u043E"
    },
    image: {
      error: "\u041E\u0428\u0418\u0411\u041A\u0410"
    },
    pageHeader: {
      title: "\u041D\u0430\u0437\u0430\u0434"
    },
    popconfirm: {
      confirmButtonText: "\u041E\u043A",
      cancelButtonText: "\u041E\u0442\u043C\u0435\u043D\u0430"
    },
    carousel: {
      leftArrow: "\u0421\u043B\u0430\u0439\u0434\u0435\u0440 \u0441\u0442\u0440\u0435\u043B\u043A\u0430 \u0432\u043B\u0435\u0432\u043E",
      rightArrow: "\u0421\u043B\u0430\u0439\u0434\u0435\u0440 \u0441\u0442\u0440\u0435\u043B\u043A\u0430 \u0432\u043F\u0440\u0430\u0432\u043E",
      indicator: "\u0421\u043B\u0430\u0439\u0434\u0435\u0440 \u043F\u0435\u0440\u0435\u0439\u0442\u0438 \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443 \u043F\u043E\u0434 \u043D\u043E\u043C\u0435\u0440\u043E\u043C {index}"
    }
  }
};

export { ru as default };
