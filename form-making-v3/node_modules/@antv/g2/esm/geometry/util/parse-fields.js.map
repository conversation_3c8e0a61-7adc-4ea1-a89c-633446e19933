{"version": 3, "file": "parse-fields.js", "sourceRoot": "", "sources": ["../../../src/geometry/util/parse-fields.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,cAAc;AACd,MAAM,UAAU,WAAW,CAAC,KAAwB;IAClD,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC", "sourcesContent": ["import { isArray } from '@antv/util';\n\n/** @ignore */\nexport function parseFields(field: string | string[]): string[] {\n  if (isArray(field)) {\n    return field;\n  }\n\n  return field.split('*');\n}\n"]}