{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../../src/geometry/label/layout/pie/util.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,UAAU,aAAa,CAC3B,KAAuB,EACvB,WAAmB,EACnB,SAAqE;IAErE,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,IAAI,CAAC,SAAS,EAAf,CAAe,CAAC,CAAC;IAEvD,uBAAuB;IACvB,MAAM,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC;IACjC,mDAAmD;IACnD,IAAI,WAAW,GAAG,IAAI,CAAC;IACvB,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC;IAC9B,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;IAC5B,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAC1C,IAAI,CAAC,CAAC;IAEN,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;IAC5B,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK;QAC7B,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;SAChB;QACD,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YAClB,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;SAChB;QACD,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC;YAC3B,GAAG,EAAE,IAAI;SACV,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,IAAI,MAAM,CAAC;IACf,IAAI,IAAI,GAAG,MAAM,GAAG,WAAW,EAAE;QAC/B,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC;KAC7B;IAED,OAAO,WAAW,EAAE;QAClB,4BAA4B;QAC5B,KAAK,CAAC,OAAO,CAAC,UAAC,GAAG;YAChB,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3F,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;YAClF,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,WAAW,GAAG,KAAK,CAAC;QACpB,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE,EAAE;YACV,IAAI,CAAC,GAAG,CAAC,EAAE;gBACT,IAAM,WAAW,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjC,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAI,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE;oBAChD,cAAc;oBACd,WAAW,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;oBAC7B,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAE9D,qBAAqB;oBACrB,IAAI,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,GAAG,WAAW,EAAE;wBACpD,WAAW,CAAC,GAAG,GAAG,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;qBAClD;oBACD,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe;oBACnC,WAAW,GAAG,IAAI,CAAC;iBACpB;aACF;SACF;KACF;IAED,CAAC,GAAG,CAAC,CAAC;IACN,mCAAmC;IACnC,KAAK,CAAC,OAAO,CAAC,UAAC,CAAC;QACd,IAAI,iBAAiB,GAAG,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;QACxE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAChB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,iBAAiB,CAAC;YACxC,iBAAiB,IAAI,WAAW,CAAC;YACjC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { PolarLabelItem } from '../../interface';\n\n/**\n * 碰撞检测算法\n */\nexport function antiCollision(\n  items: PolarLabelItem[],\n  labelHeight: number,\n  plotRange: { minY: number; maxY: number; minX: number; maxX: number }\n) {\n  const labels = items.filter((item) => !item.invisible);\n\n  // sorted by y, mutable\n  labels.sort((a, b) => a.y - b.y);\n  // adjust y position of labels to avoid overlapping\n  let overlapping = true;\n  const startY = plotRange.minY;\n  const endY = plotRange.maxY;\n  let totalHeight = Math.abs(startY - endY);\n  let i;\n\n  let maxY = 0;\n  let minY = Number.MIN_VALUE;\n  const boxes = labels.map((label) => {\n    if (label.y > maxY) {\n      maxY = label.y;\n    }\n    if (label.y < minY) {\n      minY = label.y;\n    }\n    return {\n      content: label.content,\n      size: labelHeight,\n      targets: [label.y - startY],\n      pos: null,\n    };\n  });\n\n  minY -= startY;\n  if (maxY - startY > totalHeight) {\n    totalHeight = maxY - startY;\n  }\n\n  while (overlapping) {\n    /* eslint no-loop-func: 0 */\n    boxes.forEach((box) => {\n      const target = (Math.min.apply(minY, box.targets) + Math.max.apply(minY, box.targets)) / 2;\n      box.pos = Math.min(Math.max(minY, target - box.size / 2), totalHeight - box.size);\n      box.pos = Math.max(0, box.pos);\n    });\n\n    // detect overlapping and join boxes\n    overlapping = false;\n    i = boxes.length;\n    while (i--) {\n      if (i > 0) {\n        const previousBox = boxes[i - 1];\n        const box = boxes[i];\n        if (previousBox.pos + previousBox.size > box.pos) {\n          // overlapping\n          previousBox.size += box.size;\n          previousBox.targets = previousBox.targets.concat(box.targets);\n\n          // overflow, shift up\n          if (previousBox.pos + previousBox.size > totalHeight) {\n            previousBox.pos = totalHeight - previousBox.size;\n          }\n          boxes.splice(i, 1); // removing box\n          overlapping = true;\n        }\n      }\n    }\n  }\n\n  i = 0;\n  // step 4: normalize y and adjust x\n  boxes.forEach((b) => {\n    let posInCompositeBox = startY + labelHeight / 2; // middle of the label\n    b.targets.forEach(() => {\n      labels[i].y = b.pos + posInCompositeBox;\n      posInCompositeBox += labelHeight;\n      i++;\n    });\n  });\n}\n"]}