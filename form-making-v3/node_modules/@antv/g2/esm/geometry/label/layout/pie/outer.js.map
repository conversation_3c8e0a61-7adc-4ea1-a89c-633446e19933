{"version": 3, "file": "outer.js", "sourceRoot": "", "sources": ["../../../../../src/geometry/label/layout/pie/outer.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D,OAAO,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AAEvC,4BAA4B;AAC5B,IAAM,MAAM,GAAG,CAAC,CAAC;AAEjB;;;GAGG;AACH,SAAS,aAAa,CAAC,IAAS,CAAC,qBAAqB,EAAE,UAAsB;IAC5E,WAAW;IACX,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,UAAU;IACV,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IAEtC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;QAClB,IAAA,KAAK,GAA0B,IAAI,MAA9B,EAAU,WAAW,GAAK,IAAI,OAAT,CAAU;QAC5C,OAAO;QACP,IAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvE,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhF,IAAM,QAAQ,GAAG;YACf,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;YACnC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;SACpC,CAAC;QAEF,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9C,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAClC,cAAc;QACd,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC;SACrB;QAED,iCAAiC;QACjC,IAAI,eAAe,KAAK,KAAK,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBAC7B,kBAAkB;gBAClB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;aACrB;YAED,oCAAoC;YACpC,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,OAAO;YACP,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE;gBAChE,IAAI,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE;oBAC7B,SAAS,GAAG,CAAC,CAAC;iBACf;aACF;YAED,OAAO;YACP,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;gBACrC,IAAI,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE;oBAC7B,SAAS,GAAG,CAAC,CAAC;iBACf;aACF;YAED,OAAO;YACP,IAAI,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE;gBAC3C,IAAI,UAAU,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE;oBAC7B,SAAS,GAAG,CAAC,CAAC;iBACf;aACF;YAED,OAAO;YACP,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;gBACvE,IAAI,UAAU,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE;oBAC7B,SAAS,GAAG,CAAC,CAAC;iBACf;aACF;YAED,IAAM,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5E,IAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,KAAK,CAAC,CAAC;YAClF,SAAS;YACT,IAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,WAAW,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;YAE7F;;;;;;eAMG;YACH,OAAO;YACP,IAAM,YAAY,GAAG,CAAC,CAAC;YACvB,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,YAAK,UAAU,CAAC,CAAC,cAAI,UAAU,CAAC,CAAC,CAAE,CAAC,CAAC;YAC/C,cAAc;YACd,IAAI,CAAC,IAAI,CAAC,YAAK,UAAU,CAAC,CAAC,cAAI,UAAU,CAAC,CAAC,CAAE,CAAC,CAAC;YAC/C,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,YAAK,MAAM,CAAC,CAAC,cAAI,MAAM,CAAC,CAAC,gBAAM,YAAY,cAAI,SAAS,cAAI,WAAW,CAAC,CAAC,cAAI,WAAW,CAAC,CAAC,CAAE,CAAC,CAAC;YACxG,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,YAAK,QAAQ,CAAC,CAAC,cAAI,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC;SAC5C;aAAM;YACL,IAAM,UAAU,GAAG,gBAAgB,CACjC,MAAM,CAAC,CAAC,EACR,MAAM,CAAC,CAAC,EACR,MAAM,GAAG,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EACrE,KAAK,CACN,CAAC;YACF,UAAU;YACV,0FAA0F;YAC1F,IAAM,KAAK,GAAG,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,YAAK,QAAQ,CAAC,CAAC,cAAI,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC;YAC3C,IAAM,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrE,IAAM,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrD,2DAA2D;gBAC3D,IAAI,CAAC,IAAI,OAAT,IAAI,EACC;oBACD,GAAG;oBACH,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;oBACtB,QAAQ,CAAC,CAAC;oBACV,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;oBAC/B,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;oBAC/B,UAAU,CAAC,CAAC;oBACZ,UAAU,CAAC,CAAC;iBACb,EACD;aACH;YACD,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,YAAK,UAAU,CAAC,CAAC,cAAI,UAAU,CAAC,CAAC,CAAE,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACtC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CACjC,aAA+B,EAC/B,MAAgB,EAChB,MAA2B,EAC3B,MAAY;;IAEZ,IAAM,KAAK,GAAG,MAAM,CAAC,aAAa,EAAE,UAAC,IAAI,IAAK,OAAA,CAAC,KAAK,CAAC,IAAI,CAAC,EAAZ,CAAY,CAAC,CAAC;IAC5D,UAAU;IACV,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC5D,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IAED,WAAW;IACX,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,UAAU;IACV,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,mBAAmB;IACnB,IAAM,SAAS,GAAqC,EAAE,CAAC;;QACvD,KAAyB,IAAA,WAAA,SAAA,MAAM,CAAA,8BAAA,kDAAE;YAA5B,IAAM,UAAU,mBAAA;YACnB,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC;SAC9C;;;;;;;;;IAED,kCAAkC;IAClC,IAAM,WAAW,GAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IAC7D,IAAM,WAAW,GAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IAEvD,IAAI,WAAW,IAAI,CAAC,EAAE;QACpB,OAAO;KACR;IAED,IAAM,aAAa,GAAG,MAAM,CAAC;IAC7B,IAAM,cAAc,GAAG,OAAO,CAAC;IAC/B,0BAA0B;IAC1B,IAAM,cAAc,GAAG,OAAO,CAAC,KAAK,EAAE,UAAC,IAAI,IAAK,OAAA,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,EAApD,CAAoD,CAAC,CAAC;IAE9F,IAAA,KAAK,GAAU,UAAU,MAApB,EAAE,GAAG,GAAK,UAAU,IAAf,CAAgB;IAClC,+BAA+B;IAC/B,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;IAC/F,IAAM,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC;IAE/B,oCAAoC;IACpC,IAAM,oBAAoB,GAAG;QAC3B,IAAI,EAAE,KAAK,CAAC,CAAC;QACb,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM;QACvB,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM;KACxB,CAAC;IAEF,wBAAwB;IACxB,IAAI,CAAC,cAAc,EAAE,UAAC,IAAI,EAAE,GAAG;QAC7B,IAAM,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,MAAM,GAAG,wBAAwB,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACb,0BAA0B;gBAC1B,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,UAAC,SAAyB,EAAE,GAAG;gBACxC,IAAI,GAAG,GAAG,CAAC,GAAG,wBAAwB,EAAE;oBACtC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;oBAC9C,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;iBAC5B;YACH,CAAC,CAAC,CAAC;SACJ;QACD,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,oBAAoB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE,UAAC,IAAsB,EAAE,GAAW;QACvD,IAAI,CAAC,IAAI,EAAE,UAAC,IAAoB;YAC9B,IAAM,OAAO,GAAG,GAAG,KAAK,cAAc,CAAC;YACvC,IAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEtC,2FAA2F;YAC3F,IAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAa,CAAC;YAE1D,kBAAkB;YAClB,IAAI,OAAO,EAAE;gBACX,IAAM,CAAC,GAAG,MAAM,GAAG,WAAW,CAAC;gBAC/B,qCAAqC;gBACrC,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBAE7B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAM,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE7B,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO;oBACP,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;iBAC7C;qBAAM;oBACL,QAAQ;oBACR,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;iBAC7C;aACF;YAED,oBAAoB;YACpB,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAC3B;YAED,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { BBox, IGroup, IShape, IElement } from '@antv/g-base';\nimport { isObject, each, get, groupBy, isNil, filter } from '@antv/util';\nimport { polarToCartesian } from '../../../../util/graphics';\nimport { PolarLabelItem } from '../../interface';\nimport { antiCollision } from './util';\n\n/** label text和line距离 4px */\nconst MARGIN = 4;\n\n/**\n * 配置 labelline\n * @param item PolarLabelItem\n */\nfunction drawLabelline(item: any /** PolarLabelItem */, coordinate: Coordinate) {\n  /** 坐标圆心 */\n  const center = coordinate.getCenter();\n  /** 圆半径 */\n  const radius = coordinate.getRadius();\n\n  if (item && item.labelLine) {\n    const { angle, offset: labelOffset } = item;\n    // 贴近圆周\n    const startPoint = polarToCartesian(center.x, center.y, radius, angle);\n    const itemX = item.x + get(item, 'offsetX', 0) * (Math.cos(angle) > 0 ? 1 : -1);\n    const itemY = item.y + get(item, 'offsetY', 0) * (Math.sin(angle) > 0 ? 1 : -1);\n\n    const endPoint = {\n      x: itemX - Math.cos(angle) * MARGIN,\n      y: itemY - Math.sin(angle) * MARGIN,\n    };\n\n    const smoothConnector = item.labelLine.smooth;\n    const path = [];\n    const dx = endPoint.x - center.x;\n    const dy = endPoint.y - center.y;\n    let endAngle = Math.atan(dy / dx);\n    // 第三象限 & 第四象限\n    if (dx < 0) {\n      endAngle += Math.PI;\n    }\n\n    // 默认 smooth, undefined 也为 smooth\n    if (smoothConnector === false) {\n      if (!isObject(item.labelLine)) {\n        // labelLine: true\n        item.labelLine = {};\n      }\n\n      // 表示弧线的方向，0 表示从起点到终点沿逆时针画弧, 1 表示顺时针\n      let sweepFlag = 0;\n\n      // 第一象限\n      if ((angle < 0 && angle > -Math.PI / 2) || angle > Math.PI * 1.5) {\n        if (endPoint.y > startPoint.y) {\n          sweepFlag = 1;\n        }\n      }\n\n      // 第二象限\n      if (angle >= 0 && angle < Math.PI / 2) {\n        if (endPoint.y > startPoint.y) {\n          sweepFlag = 1;\n        }\n      }\n\n      // 第三象限\n      if (angle >= Math.PI / 2 && angle < Math.PI) {\n        if (startPoint.y > endPoint.y) {\n          sweepFlag = 1;\n        }\n      }\n\n      // 第四象限\n      if (angle < -Math.PI / 2 || (angle >= Math.PI && angle < Math.PI * 1.5)) {\n        if (startPoint.y > endPoint.y) {\n          sweepFlag = 1;\n        }\n      }\n\n      const distance = labelOffset / 2 > 4 ? 4 : Math.max(labelOffset / 2 - 1, 0);\n      const breakPoint = polarToCartesian(center.x, center.y, radius + distance, angle);\n      // 圆弧的结束点\n      const breakPoint3 = polarToCartesian(center.x, center.y, radius + labelOffset / 2, endAngle);\n\n      /**\n       * @example\n       * M 100 100 L100 90 A 50 50 0 0 0 150 50\n       * 移动至 (100, 100), 连接到 (100, 90), 以 (50, 50) 为圆心，绘制圆弧至 (150, 50);\n       * A 命令的第 4 个参数 large-arc-flag, 决定弧线是大于还是小于 180 度: 0 表示小角度弧，1 表示大角\n       * 第 5 个参数: 是否顺时针绘制\n       */\n      // 默认小弧\n      const largeArcFlag = 0;\n      // step1: 移动至起点\n      path.push(`M ${startPoint.x} ${startPoint.y}`);\n      // step2: 连接拐点\n      path.push(`L ${breakPoint.x} ${breakPoint.y}`);\n      // step3: 绘制圆弧 至 结束点\n      path.push(`A ${center.x} ${center.y} 0 ${largeArcFlag} ${sweepFlag} ${breakPoint3.x} ${breakPoint3.y}`);\n      // step4: 连接结束点\n      path.push(`L ${endPoint.x} ${endPoint.y}`);\n    } else {\n      const breakPoint = polarToCartesian(\n        center.x,\n        center.y,\n        radius + (labelOffset / 2 > 4 ? 4 : Math.max(labelOffset / 2 - 1, 0)),\n        angle\n      );\n      // G2 旧的拉线\n      // path.push('Q', `${breakPoint.x}`, `${breakPoint.y}`, `${endPoint.x}`, `${endPoint.y}`);\n      const xSign = startPoint.x < center.x ? 1 : -1;\n      // step1: 连接结束点\n      path.push(`M ${endPoint.x} ${endPoint.y}`);\n      const slope1 = (startPoint.y - center.y) / (startPoint.x - center.x);\n      const slope2 = (endPoint.y - center.y) / (endPoint.x - center.x);\n      if (Math.abs(slope1 - slope2) > Math.pow(Math.E, -16)) {\n        // step2: 绘制 curve line (起点 & 结合点与圆心的斜率不等时, 由于存在误差, 使用近似处理)\n        path.push(\n          ...[\n            'C',\n            endPoint.x + xSign * 4,\n            endPoint.y,\n            2 * breakPoint.x - startPoint.x,\n            2 * breakPoint.y - startPoint.y,\n            startPoint.x,\n            startPoint.y,\n          ]\n        );\n      }\n      // step3: 连接至起点\n      path.push(`L ${startPoint.x} ${startPoint.y}`);\n    }\n    item.labelLine.path = path.join(' ');\n  }\n}\n\n/**\n * 饼图 outer-label 布局, 适用于 type = pie 且 label offset > 0 的标签\n */\nexport function pieOuterLabelLayout(\n  originalItems: PolarLabelItem[],\n  labels: IGroup[],\n  shapes: IShape[] | IGroup[],\n  region: BBox\n) {\n  const items = filter(originalItems, (item) => !isNil(item));\n  /** 坐标系 */\n  const coordinate = labels[0] && labels[0].get('coordinate');\n  if (!coordinate) {\n    return;\n  }\n\n  /** 坐标圆心 */\n  const center = coordinate.getCenter();\n  /** 圆半径 */\n  const radius = coordinate.getRadius();\n  /** label shapes */\n  const labelsMap: Record<string /** id */, IGroup> = {};\n  for (const labelShape of labels) {\n    labelsMap[labelShape.get('id')] = labelShape;\n  }\n\n  // note labelHeight 可以控制 label 的行高\n  const labelHeight: number = get(items[0], 'labelHeight', 14);\n  const labelOffset: number = get(items[0], 'offset', 0);\n\n  if (labelOffset <= 0) {\n    return;\n  }\n\n  const LEFT_HALF_KEY = 'left';\n  const RIGHT_HALF_KEY = 'right';\n  // step 1: separate labels\n  const separateLabels = groupBy(items, (item) => (item.x < center.x ? LEFT_HALF_KEY : RIGHT_HALF_KEY));\n\n  const { start, end } = coordinate;\n  // step2: calculate totalHeight\n  const totalHeight = Math.min((radius + labelOffset + labelHeight) * 2, coordinate.getHeight());\n  const totalR = totalHeight / 2;\n\n  /** labels 容器的范围(后续根据组件的布局设计进行调整) */\n  const labelsContainerRange = {\n    minX: start.x,\n    maxX: end.x,\n    minY: center.y - totalR,\n    maxY: center.y + totalR,\n  };\n\n  // step 3: antiCollision\n  each(separateLabels, (half, key) => {\n    const maxLabelsCountForOneSide = Math.floor(totalHeight / labelHeight);\n    if (half.length > maxLabelsCountForOneSide) {\n      half.sort((a, b) => {\n        // sort by percentage DESC\n        return b.percent - a.percent;\n      });\n\n      each(half, (labelItem: PolarLabelItem, idx) => {\n        if (idx + 1 > maxLabelsCountForOneSide) {\n          labelsMap[labelItem.id].set('visible', false);\n          labelItem.invisible = true;\n        }\n      });\n    }\n    antiCollision(half, labelHeight, labelsContainerRange);\n  });\n\n  each(separateLabels, (half: PolarLabelItem[], key: string) => {\n    each(half, (item: PolarLabelItem) => {\n      const isRight = key === RIGHT_HALF_KEY;\n      const labelShape = labelsMap[item.id];\n\n      // because group could not effect content-shape, should set content-shape position manually\n      const content = labelShape.getChildByIndex(0) as IElement;\n\n      // textShape 发生过调整\n      if (content) {\n        const r = radius + labelOffset;\n        // (x - cx)^2 + (y - cy)^2 = totalR^2\n        const dy = item.y - center.y;\n\n        const rPow2 = Math.pow(r, 2);\n        const dyPow2 = Math.pow(dy, 2);\n        const dxPow2 = rPow2 - dyPow2 > 0 ? rPow2 - dyPow2 : 0;\n        const dx = Math.sqrt(dxPow2);\n\n        const dx_offset = Math.abs(Math.cos(item.angle) * r);\n        if (!isRight) {\n          // left\n          item.x = center.x - Math.max(dx, dx_offset);\n        } else {\n          // right\n          item.x = center.x + Math.max(dx, dx_offset);\n        }\n      }\n\n      // adjust labelShape\n      if (content) {\n        content.attr('y', item.y);\n        content.attr('x', item.x);\n      }\n\n      drawLabelline(item, coordinate);\n    });\n  });\n}\n"]}