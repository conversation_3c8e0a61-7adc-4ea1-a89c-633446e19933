{"version": 3, "file": "pyramid.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/interval/pyramid.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AAEtD,iBAAiB;AACjB,aAAa,CAAC,UAAU,EAAE,SAAS,EAAE;IACnC,SAAS,YAAC,UAAsB;QAC9B,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,oBAAoB;QAC3D,OAAO,aAAa,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,YAAC,GAAc,EAAE,SAAiB;QACpC,IAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACzC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,MAAiB,EAAE,GAAG,CAAC,UAAqB,EAAE,IAAI,CAAC,CAAC,CAAC;QACnG,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvC,KAAK,wBACA,KAAK,KACR,IAAI,MAAA,GACL;YACD,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IACD,SAAS,YAAC,SAAyB;QACzB,IAAA,KAAK,GAAK,SAAS,MAAd,CAAe;QAC5B,OAAO;YACL,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE;gBACL,CAAC,EAAE,CAAC;gBACJ,IAAI,EAAE,KAAK;aACZ;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IGroup } from '../../../dependents';\nimport { Point, ShapeInfo, ShapeMarkerCfg, ShapePoint } from '../../../interface';\n\nimport { registerShape } from '../base';\nimport { getStyle } from '../util/get-style';\nimport { getFunnelPath, getRectPoints } from './util';\n\n/** 金字塔图，尖底漏斗图 */\nregisterShape('interval', 'pyramid', {\n  getPoints(shapePoint: ShapePoint) {\n    shapePoint.size = shapePoint.size * 2; // 漏斗图的 size 是柱状图的两倍\n    return getRectPoints(shapePoint);\n  },\n  draw(cfg: ShapeInfo, container: IGroup) {\n    const style = getStyle(cfg, false, true);\n    const path = this.parsePath(getFunnelPath(cfg.points as Point[], cfg.nextPoints as Point[], true));\n    const shape = container.addShape('path', {\n      attrs: {\n        ...style,\n        path,\n      },\n      name: 'interval',\n    });\n\n    return shape;\n  },\n  getMarker(markerCfg: ShapeMarkerCfg) {\n    const { color } = markerCfg;\n    return {\n      symbol: 'square',\n      style: {\n        r: 4,\n        fill: color,\n      },\n    };\n  },\n});\n"]}