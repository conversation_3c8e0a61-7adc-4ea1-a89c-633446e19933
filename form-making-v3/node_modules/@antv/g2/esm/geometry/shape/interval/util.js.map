{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/interval/util.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAIjE;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,SAAqB;;IACzC,IAAA,CAAC,GAAkB,SAAS,EAA3B,EAAE,CAAC,GAAe,SAAS,EAAxB,EAAE,EAAE,GAAW,SAAS,GAApB,EAAE,IAAI,GAAK,SAAS,KAAd,CAAe;IACrC,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,eAAe;IACf,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC;IACT,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;QACd,KAAA,OAAe,CAAC,IAAA,EAAf,IAAI,QAAA,EAAE,IAAI,QAAA,CAAM;KAClB;SAAM;QACL,IAAI,GAAG,EAAE,CAAC;QACV,IAAI,GAAG,CAAC,CAAC;KACV;IAED,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC;IACT,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;QACd,KAAA,OAAe,CAAC,IAAA,EAAf,IAAI,QAAA,EAAE,IAAI,QAAA,CAAM;KAClB;SAAM;QACL,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACpB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;KACrB;IAED,IAAM,MAAM,GAAG;QACb,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;QACpB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;KACrB,CAAC;IAEF,0BAA0B;IAC1B,WAAW;IACX,WAAW;IACX,WAAW;IACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAExD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAAC,MAAe,EAAE,QAAwB;IAAxB,yBAAA,EAAA,eAAwB;IACnE,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QACjD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5C;IACD,kDAAkD;IAClD,IAAI,QAAQ,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACrD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAAyB,EAAE,SAAiB;IACtE,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;QACnB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAC/B;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACrB;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAChB;aAAM;YACL,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;SAAM;QACL,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;KAC5B;IAED,SAAS;IACT,IAAI,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;QACvB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,EAAE,GAAG,SAAS,GAAG,EAAE,CAAC;KACrB;IAED,IAAI,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE;QACvB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,EAAE,GAAG,SAAS,GAAG,EAAE,CAAC;KACrB;IAED,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,qBAAqB,CAAC,GAAc,EAAE,MAAe,EAAE,UAAsB;IAC3F,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,IAAM,EAAE,GAAG,UAAU,CAAC,YAAY;YAChC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9C,IAAM,EAAE,GAAG,UAAU,CAAC,YAAY;YAChC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAE5C,yDAAyD;QACzD,IAAM,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3D,IAAI,MAAM,EAAE;YACV,IAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxG,IAAM,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAClF,IAAA,KAAA,OAAmB,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,IAAA,EAA9D,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgD,CAAC;YAEtE,2BAA2B;YAC3B,IAAM,sBAAoB,GAAG,CAAC,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YACpF,IAAM,IAAI,GAAG,sBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAM,QAAQ,GAAG,UAAC,CAAS,IAAK,OAAA,sBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA7B,CAA6B,CAAC;YAE9D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5C,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5C,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7E;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;IAED,IAAI,UAAU,CAAC,OAAO,EAAE;QACtB,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QAChC,IAAA,KAA2B,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,EAAlD,UAAU,gBAAA,EAAE,QAAQ,cAA8B,CAAC;QAC3D,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YAC3D,YAAY;YACZ,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACxF;aAAM;YACL,IAAM,GAAG,GAAG,UAAC,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAc,CAAC;YAClC,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,6CAA6C;YAC7C,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SAC9F;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAe,EAAE,OAAsB,EAAE,IAAgB;IAC3F,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAChC,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;IACpC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;IAC3E,IAAI,OAAO,KAAK,OAAO,EAAE;QACvB,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SAChE;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;SAAM;QACL,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;KAC5B;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAAC,MAAe,EAAE,UAAmB,EAAE,SAAkB;IACpF,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI,CAAC,IAAI,CACP,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACvC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACvC,CAAC,GAAG,CAAC,CACN,CAAC;KACH;SAAM,IAAI,SAAS,EAAE;QACpB,SAAS;QACT,IAAI,CAAC,IAAI,CACP,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EACvE,CAAC,GAAG,CAAC,CACN,CAAC;KACH;SAAM;QACL,SAAS;QACT,IAAI,CAAC,IAAI,CACP,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAG,CAAC,CACN,CAAC;KACH;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,IAAI,CAAI,EAAK,EAAE,EAAK;IAC3B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CAAC,MAAe,EAAE,UAAsB,EAAE,MAA0B;;IACzG,WAAW;IACP,IAAA,KAAA,gCAAuB,MAAM,aAAC,EAA7B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAe,CAAC;IAC/B,IAAA,KAAA,OAAmB,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,IAAA,EAA7E,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAA+D,CAAC;IAEnF,IAAI,UAAU,CAAC,YAAY,EAAE;QAC3B,KAAA,OAAW,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,EAAtB,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiB;KACzB;IAED;;OAEG;IACH,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;QAC7B,KAAA,OAAW,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,EAAtB,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiB;QACxB,KAAA,OAAW,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,EAAtB,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiB;KACzB;IACD,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;QAC7B,KAAA,OAAW,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,EAAtB,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiB;QACxB,KAAA,OAAW,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,EAAtB,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiB;KACzB;IAED,IAAM,IAAI,GAAG,EAAE,CAAC;IAGhB;;;;;;;;;;OAUG;IACH,IAAM,GAAG,GAAG,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAX,CAAW,CAAC;IAC7B,KAAA,OAAmB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,EAAN,CAAM,CAAC,IAAA,EAA9G,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,CAAiG;IAEhH,IAAI,UAAU,CAAC,YAAY,EAAE;QAC3B,KAAA,OAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,IAAA,EAAlC,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,CAAoB;KACpC;IAED,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;QAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA,UAAU,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;SAAM,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;SAAM;QACL,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClB;IAED,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { isArray, isNil, get } from '@antv/util';\nimport { getAngle, getSectorPath } from '../../../util/graphics';\nimport { PathCommand } from '../../../dependents';\nimport { Point, ShapeInfo, ShapePoint } from '../../../interface';\n\n/**\n * @ignore\n * 根据数据点生成矩形的四个关键点\n * @param pointInfo 数据点信息\n * @param [isPyramid] 是否为尖底漏斗图\n * @returns rect points 返回矩形四个顶点信息\n */\nexport function getRectPoints(pointInfo: ShapePoint): Point[] {\n  const { x, y, y0, size } = pointInfo;\n  // 有 4 种情况，\n  // 1. x, y 都不是数组\n  // 2. y是数组，x不是\n  // 3. x是数组，y不是\n  // 4. x, y 都是数组\n  let yMin;\n  let yMax;\n  if (isArray(y)) {\n    [yMin, yMax] = y;\n  } else {\n    yMin = y0;\n    yMax = y;\n  }\n\n  let xMin;\n  let xMax;\n  if (isArray(x)) {\n    [xMin, xMax] = x;\n  } else {\n    xMin = x - size / 2;\n    xMax = x + size / 2;\n  }\n\n  const points = [\n    { x: xMin, y: yMin },\n    { x: xMin, y: yMax },\n  ];\n\n  // 矩形的四个关键点，结构如下（左下角顺时针连接）\n  // 1 ---- 2\n  // |      |\n  // 0 ---- 3\n  points.push({ x: xMax, y: yMax }, { x: xMax, y: yMin });\n\n  return points;\n}\n\n/**\n * @ignore\n * 根据矩形关键点绘制 path\n * @param points 关键点数组\n * @param isClosed path 是否需要闭合\n * @returns 返回矩形的 path\n */\nexport function getRectPath(points: Point[], isClosed: boolean = true): PathCommand[] {\n  const path = [];\n  const firstPoint = points[0];\n  path.push(['M', firstPoint.x, firstPoint.y]);\n  for (let i = 1, len = points.length; i < len; i++) {\n    path.push(['L', points[i].x, points[i].y]);\n  }\n  // 对于 shape=\"line\" path 不应该闭合，否则会造成 lineCap 绘图属性失效\n  if (isClosed) {\n    path.push(['L', firstPoint.x, firstPoint.y]); // 需要闭合\n    path.push(['z']);\n  }\n  return path;\n}\n\n/**\n * 处理 rect path 的 radius\n * @returns 返回矩形 path 的四个角的 arc 半径\n */\nexport function parseRadius(radius: number | number[], minLength: number): number[] {\n  let r1 = 0;\n  let r2 = 0;\n  let r3 = 0;\n  let r4 = 0;\n  if (isArray(radius)) {\n    if (radius.length === 1) {\n      r1 = r2 = r3 = r4 = radius[0];\n    } else if (radius.length === 2) {\n      r1 = r3 = radius[0];\n      r2 = r4 = radius[1];\n    } else if (radius.length === 3) {\n      r1 = radius[0];\n      r2 = r4 = radius[1];\n      r3 = radius[2];\n    } else {\n      r1 = radius[0];\n      r2 = radius[1];\n      r3 = radius[2];\n      r4 = radius[3];\n    }\n  } else {\n    r1 = r2 = r3 = r4 = radius;\n  }\n\n  // 处理 边界值\n  if (r1 + r2 > minLength) {\n    r1 = r1 ? minLength / (1 + r2 / r1) : 0;\n    r2 = minLength - r1;\n  }\n\n  if (r3 + r4 > minLength) {\n    r3 = r3 ? minLength / (1 + r4 / r3) : 0;\n    r4 = minLength - r3;\n  }\n\n  return [r1 || 0, r2 || 0, r3 || 0, r4 || 0];\n}\n\n/**\n * 获取 interval 矩形背景的 path\n * @param cfg 关键点的信息\n * @param points 已转化为画布坐标的 4 个关键点\n * @param coordinate 坐标系\n * @returns 返回矩形背景的 path\n */\nexport function getBackgroundRectPath(cfg: ShapeInfo, points: Point[], coordinate: Coordinate): PathCommand[] {\n  let path = [];\n  if (coordinate.isRect) {\n    const p0 = coordinate.isTransposed\n      ? { x: coordinate.start.x, y: points[0].y }\n      : { x: points[0].x, y: coordinate.start.y };\n    const p1 = coordinate.isTransposed\n      ? { x: coordinate.end.x, y: points[2].y }\n      : { x: points[3].x, y: coordinate.end.y };\n\n    // corner radius of background shape works only in 笛卡尔坐标系\n    const radius = get(cfg, ['background', 'style', 'radius']);\n    if (radius) {\n      const width = coordinate.isTransposed ? Math.abs(points[0].y - points[2].y) : points[2].x - points[1].x;\n      const height = coordinate.isTransposed ? coordinate.getWidth() : coordinate.getHeight();\n      const [r1, r2, r3, r4] = parseRadius(radius, Math.min(width, height));\n\n      // 同时存在 坐标系是否发生转置 和 y 镜像的时候\n      const isReflectYTransposed = (coordinate.isTransposed && coordinate.isReflect('y'));\n      const bump = isReflectYTransposed ? 0 : 1;\n      const opposite = (r: number) => isReflectYTransposed ? -r : r;\n\n      path.push(['M', p0.x, p1.y + opposite(r1)]);\n      r1 !== 0 && path.push(['A', r1, r1, 0, 0, bump, p0.x + r1, p1.y]);\n      path.push(['L', p1.x - r2, p1.y]);\n      r2 !== 0 && path.push(['A', r2, r2, 0, 0, bump, p1.x, p1.y + opposite(r2)]);\n      path.push(['L', p1.x, p0.y - opposite(r3)]);\n      r3 !== 0 && path.push(['A', r3, r3, 0, 0, bump, p1.x - r3, p0.y]);\n      path.push(['L', p0.x + r4, p0.y]);\n      r4 !== 0 && path.push(['A', r4, r4, 0, 0, bump, p0.x, p0.y - opposite(r4)]);\n    } else {\n      path.push(['M', p0.x, p0.y]);\n      path.push(['L', p1.x, p0.y]);\n      path.push(['L', p1.x, p1.y]);\n      path.push(['L', p0.x, p1.y]);\n      path.push(['L', p0.x, p0.y]);\n    }\n\n    path.push(['z']);\n  }\n\n  if (coordinate.isPolar) {\n    const center = coordinate.getCenter();\n    const { startAngle, endAngle } = getAngle(cfg, coordinate);\n    if (coordinate.type !== 'theta' && !coordinate.isTransposed) {\n      // 获取扇形 path\n      path = getSectorPath(center.x, center.y, coordinate.getRadius(), startAngle, endAngle);\n    } else {\n      const pow = (v) => Math.pow(v, 2);\n      const r1 = Math.sqrt(pow(center.x - points[0].x) + pow(center.y - points[0].y));\n      const r2 = Math.sqrt(pow(center.x - points[2].x) + pow(center.y - points[2].y));\n      // 获取扇形 path（其实是一个圆环，从 coordinate 的起始角度到结束角度）\n      path = getSectorPath(center.x, center.y, r1, coordinate.startAngle, coordinate.endAngle, r2);\n    }\n  }\n  return path;\n}\n\n/**\n * @ignore\n * 根据矩形关键点绘制 path\n * @param points 关键点数组\n * @param lineCap 'round'圆角样式\n * @param coor 坐标\n * @returns 返回矩形的 path\n */\nexport function getIntervalRectPath(points: Point[], lineCap: CanvasLineCap, coor: Coordinate): PathCommand[] {\n  const width = coor.getWidth();\n  const height = coor.getHeight();\n  const isRect = coor.type === 'rect';\n  let path = [];\n  const r = (points[2].x - points[1].x) / 2;\n  const ry = coor.isTransposed ? (r * height) / width : (r * width) / height;\n  if (lineCap === 'round') {\n    if (isRect) {\n      path.push(['M', points[0].x, points[0].y + ry]);\n      path.push(['L', points[1].x, points[1].y - ry]);\n      path.push(['A', r, r, 0, 0, 1, points[2].x, points[2].y - ry]);\n      path.push(['L', points[3].x, points[3].y + ry]);\n      path.push(['A', r, r, 0, 0, 1, points[0].x, points[0].y + ry]);\n    } else {\n      path.push(['M', points[0].x, points[0].y]);\n      path.push(['L', points[1].x, points[1].y]);\n      path.push(['A', r, r, 0, 0, 1, points[2].x, points[2].y]);\n      path.push(['L', points[3].x, points[3].y]);\n      path.push(['A', r, r, 0, 0, 1, points[0].x, points[0].y]);\n    }\n    path.push(['z']);\n  } else {\n    path = getRectPath(points);\n  }\n  return path;\n}\n\n/**\n * @ignore\n * 根据 funnel 关键点绘制漏斗图的 path\n * @param points 图形关键点信息\n * @param nextPoints 下一个数据的图形关键点信息\n * @param isPyramid 是否为尖底漏斗图\n * @returns 返回漏斗图的图形 path\n */\nexport function getFunnelPath(points: Point[], nextPoints: Point[], isPyramid: boolean) {\n  const path = [];\n  if (!isNil(nextPoints)) {\n    path.push(\n      ['M', points[0].x, points[0].y],\n      ['L', points[1].x, points[1].y],\n      ['L', nextPoints[1].x, nextPoints[1].y],\n      ['L', nextPoints[0].x, nextPoints[0].y],\n      ['Z']\n    );\n  } else if (isPyramid) {\n    // 金字塔最底部\n    path.push(\n      ['M', points[0].x, points[0].y],\n      ['L', points[1].x, points[1].y],\n      ['L', (points[2].x + points[3].x) / 2, (points[2].y + points[3].y) / 2],\n      ['Z']\n    );\n  } else {\n    // 漏斗图最底部\n    path.push(\n      ['M', points[0].x, points[0].y],\n      ['L', points[1].x, points[1].y],\n      ['L', points[2].x, points[2].y],\n      ['L', points[3].x, points[3].y],\n      ['Z']\n    );\n  }\n\n  return path;\n}\n\n/**\n * 交换两个对象\n */\nfunction swap<T>(p0: T, p1: T) {\n  return [p1, p0];\n}\n\n/**\n * 获取 倒角 矩形\n * - 目前只适用于笛卡尔坐标系下\n */\nexport function getRectWithCornerRadius(points: Point[], coordinate: Coordinate, radius?: number | number[]) {\n  // 获取 四个关键点\n  let [p0, p1, p2, p3] = [...points];\n  let [r1, r2, r3, r4] = typeof radius === 'number' ? Array(4).fill(radius) : radius;\n\n  if (coordinate.isTransposed) {\n    [p1, p3] = swap(p1, p3);\n  }\n\n  /**\n   * 存在镜像\n   */\n  if (coordinate.isReflect('y')) {\n    [p0, p1] = swap(p0, p1);\n    [p2, p3] = swap(p2, p3);\n  }\n  if (coordinate.isReflect('x')) {\n    [p0, p3] = swap(p0, p3);\n    [p1, p2] = swap(p1, p2);\n  }\n\n  const path = [];\n\n\n  /**\n   *  p1 → p2\n   *  ↑    ↓\n   *  p0 ← p3\n   *\n   *  负数的情况，关键点会变成下面的形式\n   *\n   *  p0 ← p3               p2 ← p1\n   *  ↓    ↑                ↓     ↑\n   *  p1 → p2  --> (转置下)  p3 → p0\n   */\n  const abs = v => Math.abs(v);\n  [r1, r2, r3, r4] = parseRadius([r1, r2, r3, r4], Math.min(abs(p3.x - p0.x), abs(p1.y - p0.y))).map(d => abs(d));\n\n  if (coordinate.isTransposed) {\n    [r1, r2, r3, r4] = [r4, r1, r2, r3]\n  }\n\n  if (p0.y < p1.y /** 负数情况 */) {\n    path.push(['M', p3.x, p3.y + r3]);\n    r3 !== 0 && path.push(['A', r3, r3, 0, 0, 0, p3.x - r3, p3.y]);\n    path.push(['L', p0.x + r4, p0.y]);\n    r4 !== 0 && path.push(['A', r4, r4, 0, 0, 0, p0.x, p0.y + r4]);\n    path.push(['L', p1.x, p1.y - r1]);\n    r1 !== 0 && path.push(['A', r1, r1, 0, 0, 0/** 逆时针 */, p1.x + r1, p1.y]);\n    path.push(['L', p2.x - r2, p2.y]);\n    r2 !== 0 && path.push(['A', r2, r2, 0, 0, 0, p2.x, p2.y - r2]);\n    path.push(['L', p3.x, p3.y + r3]);\n    path.push(['z']);\n  } else if (p3.x < p0.x) {\n    path.push(['M', p2.x + r2, p2.y]);\n    r2 !== 0 && path.push(['A', r2, r2, 0, 0, 0, p2.x, p2.y + r2]);\n    path.push(['L', p3.x, p3.y - r3]);\n    r3 !== 0 && path.push(['A', r3, r3, 0, 0, 0, p3.x + r3, p3.y]);\n    path.push(['L', p0.x - r4, p0.y]);\n    r4 !== 0 && path.push(['A', r4, r4, 0, 0, 0, p0.x, p0.y - r4]);\n    path.push(['L', p1.x, p1.y + r1]);\n    r1 !== 0 && path.push(['A', r1, r1, 0, 0, 0, p1.x - r1, p1.y]);\n    path.push(['L', p2.x + r2, p2.y]);\n    path.push(['z']);\n  } else {\n    path.push(['M', p1.x, p1.y + r1]);\n    r1 !== 0 && path.push(['A', r1, r1, 0, 0, 1, p1.x + r1, p1.y]);\n    path.push(['L', p2.x - r2, p2.y]);\n    r2 !== 0 && path.push(['A', r2, r2, 0, 0, 1, p2.x, p2.y + r2]);\n    path.push(['L', p3.x, p3.y - r3]);\n    r3 !== 0 && path.push(['A', r3, r3, 0, 0, 1, p3.x - r3, p3.y]);\n    path.push(['L', p0.x + r4, p0.y]);\n    r4 !== 0 && path.push(['A', r4, r4, 0, 0, 1, p0.x, p0.y - r4]);\n    path.push(['L', p1.x, p1.y + r1]);\n    path.push(['z']);\n  }\n\n  return path;\n}\n"]}