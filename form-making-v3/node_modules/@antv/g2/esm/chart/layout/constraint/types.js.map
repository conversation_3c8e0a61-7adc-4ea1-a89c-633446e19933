{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../src/chart/layout/constraint/types.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,CAAN,IAAY,QAOX;AAPD,WAAY,QAAQ;IAClB,qBAAS,CAAA;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,eAAe;AACjB,CAAC,EAPW,QAAQ,KAAR,QAAQ,QAOnB", "sourcesContent": ["import type { Variable } from './variable';\n\n/**\n * 操作符的枚举值\n */\nexport enum Operator {\n  EQ = 'eq',\n  // 暂时不支持不等式组\n  // GT = 'gt',\n  // LT = 'lt',\n  // GTE = 'gte',\n  // LTE = 'lte',\n}\n\nexport type BoxObject = {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n};\n\n/**\n * 几种情况\n * - 200\n * - variable\n * - [200, 2]\n * - [2, variable]\n */\nexport type Element = Variable | number | (Variable | number)[];\n"]}