{"version": 3, "file": "solver.js", "sourceRoot": "", "sources": ["../../../../src/chart/layout/constraint/solver.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAElC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAGhC;;;;;GAKG;AACH;IAAA;QACE;;WAEG;QACI,gBAAW,GAAiB,EAAE,CAAC;QAE/B,cAAS,GAAe,EAAE,CAAC;IA+EpC,CAAC;IAnEC;;;OAGG;IACI,8BAAa,GAApB;;QAAqB,qBAA4B;aAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;YAA5B,gCAA4B;;QAC/C,CAAA,KAAA,IAAI,CAAC,WAAW,CAAA,CAAC,IAAI,oCAAI,WAAW,WAAE;IACxC,CAAC;IAED;;OAEG;IACI,qBAAI,GAAX;QACE,UAAU;QACV,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAErC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAE/B,YAAY;QACZ,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAErC,QAAQ;QACR,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,GAAG;YAC5B,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,6BAAY,GAApB;QACE,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;SAC/C;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,+BAAc,GAAtB;QACE,IAAM,WAAW,GAAG,IAAI,GAAG,EAAoB,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC9B;QAED,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAM,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAEhD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,aAAC;AAAD,CAAC,AArFD,IAqFC", "sourcesContent": ["import { uniq } from '@antv/util';\nimport type { Constraint } from './constraint';\nimport { gauss } from './gauss';\nimport type { Variable } from './variable';\n\n/**\n * 约束布局构建和计算，目前针对 G2 的场景：\n * - 一定都是等式\n * - 一定有解\n * 所以将算法退化成多元一次方程组的解法，直接使用高斯消元法（o(n^2)）\n */\nexport class Solver {\n  /**\n   * 存在的约束\n   */\n  public constraints: Constraint[] = [];\n\n  public variables: Variable[] = [];\n\n  /**\n   * 条件数量\n   */\n  private m: number;\n\n  /**\n   * 变量（元）数量\n   */\n  private n: number;\n\n  /**\n   * 添加多条约束\n   * @param constraint\n   */\n  public addConstraint(...constraints: Constraint[]) {\n    this.constraints.push(...constraints);\n  }\n\n  /**\n   * 计算返回布局\n   */\n  public calc() {\n    // 1. 拿到变量\n    this.variables = this.getVariables();\n\n    this.m = this.constraints.length;\n    this.n = this.variables.length;\n\n    // 2. 获取消元矩阵\n    const matrix = this.getGaussMatrix();\n\n    // 3. 求解\n    const result = gauss(matrix);\n\n    this.variables.forEach((v, idx) => {\n      v.value = result[idx];\n    });\n\n    return this.variables;\n  }\n\n  /**\n   * 获取约束中所有的变量\n   */\n  private getVariables() {\n    let vars = [];\n    for (let i = 0; i < this.constraints.length; i++) {\n      const constraint = this.constraints[i];\n\n      vars = vars.concat(constraint.getVariables());\n    }\n\n    return uniq(vars);\n  }\n\n  /**\n   * 获得高斯消元的矩阵\n   */\n  private getGaussMatrix() {\n    const variableMap = new Map<Variable, number>();\n\n    for (let i = 0; i < this.n; i++) {\n      const variable = this.variables[i];\n      variableMap.set(variable, i);\n    }\n\n    const matrix = [];\n    for (let i = 0; i < this.m; i++) {\n      const constraint = this.constraints[i];\n\n      const arr = constraint.getGaussArr(variableMap);\n\n      matrix.push(arr);\n    }\n\n    return matrix;\n  }\n}\n"]}