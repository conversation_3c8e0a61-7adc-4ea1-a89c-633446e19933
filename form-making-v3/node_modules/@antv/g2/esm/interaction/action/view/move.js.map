{"version": 3, "file": "move.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/move.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC;AAC5B,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEnC,IAAM,YAAY,GAAG,CAAC,CAAC;AAEvB;;;GAGG;AACH;IAAmB,wBAAM;IAAzB;QAAA,qEAkEC;QAjES,cAAQ,GAAG,KAAK,CAAC;QACjB,cAAQ,GAAG,KAAK,CAAC;QACzB,6BAA6B;QACrB,gBAAU,GAAG,IAAI,CAAC;QAClB,iBAAW,GAAG,IAAI,CAAC;;IA6D7B,CAAC;IA5DC;;OAEG;IACI,oBAAK,GAAZ;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACjD,kBAAkB;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,mBAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACpD,IAAM,CAAC,GAAG,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAC/B,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC7C,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;aACpE,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACpC;IACH,CAAC;IAED;;OAEG;IACI,kBAAG,GAAV;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,oBAAK,GAAZ;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IACH,WAAC;AAAD,CAAC,AAlED,CAAmB,MAAM,GAkExB;AAED,eAAe,IAAI,CAAC", "sourcesContent": ["import { ext } from '@antv/matrix-util';\nimport { Action } from '..';\nimport { distance } from '../util';\n\nconst MIN_DISTANCE = 5;\n\n/**\n * @ignore\n * View 移动的 Action\n */\nclass Move extends Action {\n  private starting = false;\n  private isMoving = false;\n  // private cacheRange = null;\n  private startPoint = null;\n  private startMatrix = null;\n  /**\n   * 开始移动\n   */\n  public start() {\n    this.starting = true;\n    this.startPoint = this.context.getCurrentPoint();\n    // 缓存开始时的矩阵，防止反复拖拽\n    this.startMatrix = this.context.view.middleGroup.getMatrix();\n  }\n\n  /**\n   * 移动\n   */\n  public move() {\n    if (!this.starting) {\n      return;\n    }\n    const startPoint = this.startPoint;\n    const currentPoint = this.context.getCurrentPoint();\n    const d = distance(startPoint, currentPoint);\n    if (d > MIN_DISTANCE && !this.isMoving) {\n      this.isMoving = true;\n    }\n    if (this.isMoving) {\n      const view = this.context.view;\n      const matrix = ext.transform(this.startMatrix, [\n        ['t', currentPoint.x - startPoint.x, currentPoint.y - startPoint.y],\n      ]);\n      view.backgroundGroup.setMatrix(matrix);\n      view.foregroundGroup.setMatrix(matrix);\n      view.middleGroup.setMatrix(matrix);\n    }\n  }\n\n  /**\n   * 结束移动\n   */\n  public end() {\n    if (this.isMoving) {\n      this.isMoving = false;\n    }\n    this.startMatrix = null;\n    this.starting = false;\n    this.startPoint = null;\n  }\n\n  /**\n   * 回滚\n   */\n  public reset() {\n    this.starting = false;\n    this.startPoint = null;\n    this.isMoving = false;\n\n    const view = this.context.view;\n    view.backgroundGroup.resetMatrix();\n    view.foregroundGroup.resetMatrix();\n    view.middleGroup.resetMatrix();\n    this.isMoving = false;\n  }\n}\n\nexport default Move;\n"]}