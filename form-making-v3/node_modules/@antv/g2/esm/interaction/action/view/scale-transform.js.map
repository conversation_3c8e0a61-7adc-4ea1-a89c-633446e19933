{"version": 3, "file": "scale-transform.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/scale-transform.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC;AAE5B,IAAM,KAAK,GAAG,GAAG,CAAC;AAClB,IAAM,KAAK,GAAG,GAAG,CAAC;AAElB;;;GAGG;AACH;IAA6B,kCAAM;IAAnC;QAAA,qEAqCC;QApCW,UAAI,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtB,eAAS,GAAG,CAAC,MAAM,CAAC,CAAC;QACrB,oBAAc,GAAG,EAAE,CAAC;;IAkChC,CAAC;IAhCC,cAAc;IACJ,+BAAM,GAAhB,UAAiB,GAAG;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAES,iCAAQ,GAAlB,UAAmB,GAAG;QACpB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,GAAG,KAAK,GAAG,EAAE;YACf,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;SACzB;aAAM;YACL,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7B;IACH,CAAC;IAEO,iCAAQ,GAAhB,UAAiB,GAAG;QAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAChD,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;SACjC;IACH,CAAC;IAED;;OAEG;IACI,8BAAK,GAAZ;QACE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IACH,qBAAC;AAAD,CAAC,AArCD,CAA6B,MAAM,GAqClC;AAED,eAAe,cAAc,CAAC", "sourcesContent": ["import { Action } from '..';\n\nconst DIM_X = 'x';\nconst DIM_Y = 'y';\n\n/**\n * Scale translate\n * @ignore\n */\nclass ScaleTranslate extends Action {\n  protected dims = [DIM_X, DIM_Y];\n  protected cfgFields = ['dims'];\n  protected cacheScaleDefs = {};\n\n  // 是否支持对应字段的平移\n  protected hasDim(dim) {\n    return this.dims.includes(dim);\n  }\n\n  protected getScale(dim) {\n    const view = this.context.view;\n    if (dim === 'x') {\n      return view.getXScale();\n    } else {\n      return view.getYScales()[0];\n    }\n  }\n\n  private resetDim(dim) {\n    const view = this.context.view;\n    if (this.hasDim(dim) && this.cacheScaleDefs[dim]) {\n      const scale = this.getScale(dim);\n      view.scale(scale.field, this.cacheScaleDefs[dim]);\n      this.cacheScaleDefs[dim] = null;\n    }\n  }\n\n  /**\n   * 回滚\n   */\n  public reset() {\n    this.resetDim(DIM_X);\n    this.resetDim(DIM_Y);\n    const view = this.context.view;\n    view.render(true);\n  }\n}\n\nexport default ScaleTranslate;\n"]}