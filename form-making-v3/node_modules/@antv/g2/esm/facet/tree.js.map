{"version": 3, "file": "tree.js", "sourceRoot": "", "sources": ["../../src/facet/tree.ts"], "names": [], "mappings": ";AAAA;;;GAGG;AACH,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAExD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAE1D,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC;;;GAGG;AACH;IAAkC,wBAAwB;IAA1D;QAAA,qEA6SC;QAtHS,sBAAgB,GAAG;YACzB,IAAI,KAAI,CAAC,MAAM,IAAI,KAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBAChC,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBACvB,KAAI,CAAC,SAAS,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC;;IAiHJ,CAAC;IA5SW,4BAAa,GAAvB,UAAwB,IAAU,EAAE,KAAe;QACjD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAES,6BAAc,GAAxB,UAAyB,IAAU,EAAE,KAAe,IAAG,CAAC;IAEjD,mBAAI,GAAX;QACE,iBAAM,IAAI,WAAE,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACrE,CAAC;IAES,4BAAa,GAAvB;QACE,OAAO,OAAO,CAAC,EAAE,EAAE,iBAAM,aAAa,WAAE,EAAE;YACxC,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE;gBACJ,KAAK,EAAE;oBACL,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,MAAM;iBACf;gBACD,MAAM,EAAE,KAAK;aACd;YACD,SAAS,EAAE,IAAI;YACf,KAAK,EAAE,iBAAM,kBAAkB,WAAE;SAClC,CAAC,CAAC;IACL,CAAC;IAES,6BAAc,GAAxB,UAAyB,IAAa;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACjE;QACD,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAM,SAAS,GAAa;YAC1B,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI;YACnB,IAAI,MAAA;YACJ,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE;YAC/B,kBAAkB,EAAE,CAAC;YACrB,QAAQ,EAAE,CAAC;YACX,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;SAChB,CAAC;QACF,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpB,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACpB,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,wBAAS,GAAjB,UAAkB,MAAkB;QAApC,iBAMC;QALC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACnB,kBAAkB;YAClB,KAAK,CAAC,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;IACL,CAAC;IAES,wBAAS,GAAnB,UAAoB,IAAY,EAAE,IAAY,EAAE,MAAc,EAAE,MAAc;QAC5E,IAAM,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,eAAe;QACxC,IAAM,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,eAAe;QAExC,IAAM,KAAK,GAAG;YACZ,CAAC,EAAE,MAAM,GAAG,MAAM;YAClB,CAAC,EAAE,MAAM,GAAG,MAAM;SACnB,CAAC;QAEF,IAAM,GAAG,GAAG;YACV,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,MAAM;YACnB,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,mBAAmB;SACnD,CAAC;QACF,OAAO;YACL,KAAK,OAAA;YACL,GAAG,KAAA;SACJ,CAAC;IACJ,CAAC;IAEO,4BAAa,GAArB,UAAsB,MAAkB;;QAAxC,iBA8BC;QA7BC,IAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACnB,IAAI,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClB,kBAAkB;gBAClB,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC1B,KAAK,EAAE,CAAC;aACT;QACH,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,OAAO,CAAC,UAAC,KAAK;YAClB,aAAa;YACb,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1C,CAAC,CAAC,CAAC;QACH,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;gBACrD,6BAA6B;gBAC7B,KAAoB,IAAA,+BAAA,SAAA,WAAW,CAAA,CAAA,wCAAA,iEAAE;oBAA5B,IAAM,KAAK,wBAAA;oBACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;wBACvB,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,WAAW,CAAC;wBACzC,aAAa;wBACb,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;wBACxD,aAAa;wBACb,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC;qBACzC;iBACF;;;;;;;;;SACF;IACH,CAAC;IAED,sBAAsB;IACd,+BAAgB,GAAxB,UAAyB,MAAkB,EAAE,KAAa;QACxD,IAAM,GAAG,GAAe,EAAE,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACnB,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,EAAE;gBAC5B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC;IAED,oFAAoF;IAC5E,6BAAc,GAAtB,UAAuB,QAAoB;QACzC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;IACxE,CAAC;IAED,8BAA8B;IACtB,qBAAM,GAAd,UAAe,KAAe;QAC5B,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;IACnD,CAAC;IAEO,sBAAO,GAAf;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,CAAC;IAED,YAAY;IACJ,6BAAc,GAAtB,UAAuB,IAAa,EAAE,KAAa,EAAE,GAAe;QAApE,iBAmCC;QAlCC,uBAAuB;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,MAAM,GAAG,KAAK,EAAE;YAClB,OAAO;SACR;QACD,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,kCAAkC;QAClC,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAChC,kBAAkB;QAClB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAC1B,IAAM,UAAU,GAAG,CAAC,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAe,CAAC,CAAC;YAC3D,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;YACjE,IAAI,OAAO,CAAC,MAAM,EAAE;gBAClB,IAAM,KAAK,GAAa;oBACtB,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,IAAI;oBACnB,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,EAAE;oBACZ,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,EAAE;oBACZ,WAAW,EAAE,KAAK;oBAClB,eAAe,EAAE,KAAI,CAAC,OAAO,EAAE;oBAC/B,kBAAkB,EAAE,CAAC;oBACrB,QAAQ,EAAE,KAAK;oBACf,QAAQ,EAAE,KAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;iBACvD,CAAC;gBACF,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,qBAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YACtB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IASO,0BAAW,GAAnB;QAAA,iBAgBC;QAfC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,KAAe;YACxB,IAAA,WAAW,GAAW,KAAK,YAAhB,EAAE,IAAI,GAAK,KAAK,KAAV,CAAW;YACpC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAI,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAEnD,IAAM,MAAM,GAAG,OAAO,CACpB;gBACE,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,CAAqB;gBAC3C,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW;aAC1D,EACD,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,EACjC,KAAI,CAAC,GAAG,CAAC,KAAK,CACf,CAAC;YAEF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,wBAAS,GAAjB,UAAkB,MAAkB;QAApC,iBAOC;QANC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACnB,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACvB,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;gBAChC,KAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B;IACvB,4BAAa,GAArB,UAAsB,KAAe,EAAE,QAAoB;QAA3D,iBA0BC;QAzBC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;QACnC,2BAA2B;QAC3B,IAAM,KAAK,GAAG;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC;YAC9B,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;SAC5B,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,UAAC,QAAQ;YACxB,IAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;YAC/C,IAAM,GAAG,GAAG;gBACV,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;gBACzD,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;aAClB,CAAC;YAEF,IAAM,OAAO,GAAG;gBACd,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;aACnC,CAAC;YACF,IAAM,OAAO,GAAG;gBACd,CAAC,EAAE,GAAG,CAAC,CAAC;gBACR,CAAC,EAAE,OAAO,CAAC,CAAC;aACb,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAO,GAAf,UAAgB,MAAM;QACpB,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QACpC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChG;aAAM;YACL,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;gBAC1B,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,yBAAyB;IACjB,uBAAQ,GAAhB,UAAiB,MAAM;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC9B,KAAK,EAAE,MAAM,CACX;gBACE,aAAa;gBACb,IAAI,MAAA;aACL,EACD,IAAI,CACL;SACF,CAAC,CAAC;IACL,CAAC;IAES,6BAAc,GAAxB,UAAyB,CAAS,EAAE,IAAS,EAAE,MAAe,EAAE,KAAe;QAC7E,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,eAAe,GAAG,CAAC,EAAE;YAChD,6BACK,MAAM,KACT,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,IAAI,IACX;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,6BAAc,GAAxB,UAAyB,CAAS,EAAE,IAAS,EAAE,MAAe,EAAE,KAAe;QAC7E,IAAI,KAAK,CAAC,cAAc,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,EAAE;YACzD,6BACK,MAAM,KACT,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,IAAI,IACX;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,WAAC;AAAD,CAAC,AA7SD,CAAkC,KAAK,GA6StC", "sourcesContent": ["/**\n * Create By <PERSON>\n * On 2020-02-10\n */\nimport { assign, deepMix, each, get } from '@antv/util';\nimport View from '../chart/view';\nimport { DIRECTION, VIEW_LIFE_CIRCLE } from '../constant';\nimport { AxisCfg, Condition, Datum, TreeCfg, TreeData } from '../interface';\nimport { getFactTitleConfig } from '../util/facet';\nimport { Facet } from './facet';\n\n/**\n * @ignore\n * Tree Facet\n */\nexport default class Tree extends Facet<TreeCfg, TreeData> {\n  protected afterEachView(view: View, facet: TreeData) {\n    this.processAxis(view, facet);\n  }\n\n  protected beforeEachView(view: View, facet: TreeData) {}\n\n  public init() {\n    super.init();\n    this.view.on(VIEW_LIFE_CIRCLE.AFTER_RENDER, this.afterChartRender);\n  }\n\n  protected getDefaultCfg() {\n    return deepMix({}, super.getDefaultCfg(), {\n      type: 'tree',\n      line: {\n        style: {\n          lineWidth: 1,\n          stroke: '#ddd',\n        },\n        smooth: false,\n      },\n      showTitle: true,\n      title: super.getDefaultTitleCfg(),\n    });\n  }\n\n  protected generateFacets(data: Datum[]): TreeData[] {\n    const fields = this.cfg.fields;\n    if (!fields.length) {\n      throw new Error('Please specify for the fields for rootFacet!');\n    }\n    const rst = [];\n    const rootFacet: TreeData = {\n      type: this.cfg.type,\n      data,\n      region: null,\n      rowValuesLength: this.getRows(),\n      columnValuesLength: 1,\n      rowIndex: 0,\n      columnIndex: 0,\n      rowField: '',\n      columnField: '',\n      rowValue: '',\n      columnValue: '',\n    };\n    rst.push(rootFacet);\n    rootFacet.children = this.getChildFacets(data, 1, rst);\n    this.setRegion(rst);\n    return rst;\n  }\n\n  private setRegion(facets: TreeData[]) {\n    this.forceColIndex(facets);\n    facets.forEach((facet) => {\n      // @ts-ignore 允许调整\n      facet.region = this.getRegion(facet.rowValuesLength, facet.columnValuesLength, facet.columnIndex, facet.rowIndex);\n    });\n  }\n\n  protected getRegion(rows: number, cols: number, xIndex: number, yIndex: number) {\n    const xWidth = 1 / cols; // x轴方向的每个分面的偏移\n    const yWidth = 1 / rows; // y轴方向的每个分面的偏移\n\n    const start = {\n      x: xWidth * xIndex,\n      y: yWidth * yIndex,\n    };\n\n    const end = {\n      x: start.x + xWidth,\n      y: start.y + (yWidth * 2) / 3, // 预留1/3的空隙，方便添加连接线\n    };\n    return {\n      start,\n      end,\n    };\n  }\n\n  private forceColIndex(facets: TreeData[]) {\n    const leafs: TreeData[] = [];\n    let index = 0;\n    facets.forEach((facet) => {\n      if (this.isLeaf(facet)) {\n        leafs.push(facet);\n        // @ts-ignore 允许调整\n        facet.columnIndex = index;\n        index++;\n      }\n    });\n\n    leafs.forEach((facet) => {\n      // @ts-ignore\n      facet.columnValuesLength = leafs.length;\n    });\n    const maxLevel = this.cfg.fields.length;\n    for (let i = maxLevel - 1; i >= 0; i--) {\n      const levelFacets = this.getFacetsByLevel(facets, i);\n      // var yIndex = maxLevel - i;\n      for (const facet of levelFacets) {\n        if (!this.isLeaf(facet)) {\n          facet.originColIndex = facet.columnIndex;\n          // @ts-ignore\n          facet.columnIndex = this.getRegionIndex(facet.children);\n          // @ts-ignore\n          facet.columnValuesLength = leafs.length;\n        }\n      }\n    }\n  }\n\n  // get facet use level\n  private getFacetsByLevel(facets: TreeData[], level: number) {\n    const rst: TreeData[] = [];\n    facets.forEach((facet) => {\n      if (facet.rowIndex === level) {\n        rst.push(facet);\n      }\n    });\n    return rst;\n  }\n\n  // if the facet has children , make it's column index in the middle of it's children\n  private getRegionIndex(children: TreeData[]) {\n    const first = children[0];\n    const last = children[children.length - 1];\n    return (last.columnIndex - first.columnIndex) / 2 + first.columnIndex;\n  }\n\n  // is  a leaf without children\n  private isLeaf(facet: TreeData) {\n    return !facet.children || !facet.children.length;\n  }\n\n  private getRows() {\n    return this.cfg.fields.length + 1;\n  }\n\n  // get child\n  private getChildFacets(data: Datum[], level: number, arr: TreeData[]) {\n    // [ 'grade', 'class' ]\n    const fields = this.cfg.fields;\n    const length = fields.length;\n    if (length < level) {\n      return;\n    }\n    const rst = [];\n    // get fist level except root node\n    const field = fields[level - 1];\n    // get field value\n    const values = this.getFieldValues(data, field);\n    values.forEach((value, index) => {\n      const conditions = [{ field, value, values } as Condition];\n      const subData = data.filter(this.getFacetDataFilter(conditions));\n      if (subData.length) {\n        const facet: TreeData = {\n          type: this.cfg.type,\n          data: subData,\n          region: null,\n          columnValue: value,\n          rowValue: '',\n          columnField: field,\n          rowField: '',\n          columnIndex: index,\n          rowValuesLength: this.getRows(),\n          columnValuesLength: 1,\n          rowIndex: level,\n          children: this.getChildFacets(subData, level + 1, arr),\n        };\n        rst.push(facet);\n        arr.push(facet);\n      }\n    });\n    return rst;\n  }\n\n  public render() {\n    super.render();\n    if (this.cfg.showTitle) {\n      this.renderTitle();\n    }\n  }\n\n  private afterChartRender = () => {\n    if (this.facets && this.cfg.line) {\n      this.container.clear();\n      this.drawLines(this.facets);\n    }\n  };\n\n  private renderTitle() {\n    each(this.facets, (facet: TreeData) => {\n      const { columnValue, view } = facet;\n      const formatter = get(this.cfg.title, 'formatter');\n\n      const config = deepMix(\n        {\n          position: ['50%', '0%'] as [string, string],\n          content: formatter ? formatter(columnValue) : columnValue,\n        },\n        getFactTitleConfig(DIRECTION.TOP),\n        this.cfg.title\n      );\n\n      view.annotation().text(config);\n    });\n  }\n\n  private drawLines(facets: TreeData[]) {\n    facets.forEach((facet) => {\n      if (!this.isLeaf(facet)) {\n        const children = facet.children;\n        this.addFacetLines(facet, children);\n      }\n    });\n  }\n\n  // add lines with it's children\n  private addFacetLines(facet: TreeData, children: TreeData[]) {\n    const view = facet.view;\n    const region = view.coordinateBBox;\n    // top, right, bottom, left\n    const start = {\n      x: region.x + region.width / 2,\n      y: region.y + region.height,\n    };\n\n    children.forEach((subFacet) => {\n      const subRegion = subFacet.view.coordinateBBox;\n      const end = {\n        x: subRegion.bl.x + (subRegion.tr.x - subRegion.bl.x) / 2,\n        y: subRegion.tr.y,\n      };\n\n      const middle1 = {\n        x: start.x,\n        y: start.y + (end.y - start.y) / 2,\n      };\n      const middle2 = {\n        x: end.x,\n        y: middle1.y,\n      };\n      this.drawLine([start, middle1, middle2, end]);\n    });\n  }\n\n  private getPath(points) {\n    const path = [];\n    const smooth = this.cfg.line.smooth;\n    if (smooth) {\n      path.push(['M', points[0].x, points[0].y]);\n      path.push(['C', points[1].x, points[1].y, points[2].x, points[2].y, points[3].x, points[3].y]);\n    } else {\n      points.forEach((point, index) => {\n        if (index === 0) {\n          path.push(['M', point.x, point.y]);\n        } else {\n          path.push(['L', point.x, point.y]);\n        }\n      });\n    }\n\n    return path;\n  }\n\n  // draw line width points\n  private drawLine(points) {\n    const path = this.getPath(points);\n    const line = this.cfg.line.style;\n    this.container.addShape('path', {\n      attrs: assign(\n        {\n          // @ts-ignore\n          path,\n        },\n        line\n      ),\n    });\n  }\n\n  protected getXAxisOption(x: string, axes: any, option: AxisCfg, facet: TreeData): object {\n    if (facet.rowIndex !== facet.rowValuesLength - 1) {\n      return {\n        ...option,\n        title: null,\n        label: null,\n      };\n    }\n    return option;\n  }\n\n  protected getYAxisOption(y: string, axes: any, option: AxisCfg, facet: TreeData): object {\n    if (facet.originColIndex !== 0 && facet.columnIndex !== 0) {\n      return {\n        ...option,\n        title: null,\n        label: null,\n      };\n    }\n    return option;\n  }\n}\n"]}