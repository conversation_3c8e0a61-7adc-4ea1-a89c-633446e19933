{"version": 3, "file": "scale-zoom.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/scale-zoom.ts"], "names": [], "mappings": ";;;AAAA,mCAA4C;AAC5C,8EAAgD;AAEhD;;;GAGG;AACH;IAA6B,0CAAe;IAA5C;QAAA,qEAsEC;QArES,eAAS,GAAG,IAAI,CAAC;;QAkEzB,UAAU;QACV,uDAAuD;QACvD,IAAI;IACN,CAAC;IApEC;;OAEG;IACI,+BAAM,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC5B,CAAC;IAEO,6BAAI,GAAZ,UAAa,KAAK;QAAlB,iBAMC;QALC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,GAAG;YACb,KAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,gCAAO,GAAd;QACE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED,OAAO;IACC,gCAAO,GAAf,UAAgB,GAAG,EAAE,MAAM;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACpB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aACrC;YACD,uBAAuB;YACvB,8CAA8C;YAC9C,IAAI;SACL;IACH,CAAC;IACD,cAAc;IACN,mCAAU,GAAlB,UAAmB,GAAG,EAAE,KAAK,EAAE,MAAM;QACnC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG;gBACzB,aAAa;gBACb,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,GAAG,EAAE,KAAK,CAAC,GAAG;aACf,CAAC;SACH;QACD,iBAAiB;QACjB,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QAClC,IAAA,GAAG,GAAU,KAAK,IAAf,EAAE,GAAG,GAAK,KAAK,IAAV,CAAW;QAC3B,IAAM,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;QACzB,IAAM,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QACtB,IAAM,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QACtB,IAAM,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC;QAC/B,IAAM,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QAChC,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;YAClD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;gBACtB,aAAa;gBACb,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,GAAG,GAAG,CAAC;gBACZ,GAAG,EAAE,GAAG,GAAG,CAAC;aACb,CAAC,CAAC;SACJ;IACH,CAAC;IAKH,qBAAC;AAAD,CAAC,AAtED,CAA6B,yBAAe,GAsE3C;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["import { each, throttle } from '@antv/util';\nimport TransformAction from './scale-transform';\n\n/**\n * 缩放 Scale 的 Action\n * @ignore\n */\nclass ScaleTranslate extends TransformAction {\n  private zoomRatio = 0.05;\n  /**\n   * 缩小\n   */\n  public zoomIn() {\n    this.zoom(this.zoomRatio);\n  }\n\n  private zoom(scale) {\n    const dims = this.dims;\n    each(dims, (dim) => {\n      this.zoomDim(dim, scale);\n    });\n    this.context.view.render(true);\n  }\n\n  /**\n   * 放大\n   */\n  public zoomOut() {\n    this.zoom(-1 * this.zoomRatio);\n  }\n\n  // 缩放度量\n  private zoomDim(dim, dRatio) {\n    if (this.hasDim(dim)) {\n      const scale = this.getScale(dim);\n      if (scale.isLinear) {\n        this.zoomLinear(dim, scale, dRatio);\n      }\n      //  else { // 暂时仅处理连续字段\n      // this.zoomCategory(dim, scale, normalPoint);\n      // }\n    }\n  }\n  // linear 度量平移\n  private zoomLinear(dim, scale, dRatio) {\n    const view = this.context.view;\n    // 只有第一次缓存，否则无法回滚\n    if (!this.cacheScaleDefs[dim]) {\n      this.cacheScaleDefs[dim] = {\n        // @ts-ignore\n        nice: scale.nice,\n        min: scale.min,\n        max: scale.max,\n      };\n    }\n    // 使用使用原始度量作为缩放标准\n    const scaleDef = this.cacheScaleDefs[dim];\n    const range = scaleDef.max - scaleDef.min;\n    const { min, max } = scale;\n    const d = dRatio * range;\n    const toMin = min - d;\n    const toMax = max + d;\n    const curRange = toMax - toMin;\n    const scaled = curRange / range;\n    if (toMax > toMin && scaled < 100 && scaled > 0.01) {\n      view.scale(scale.field, {\n        // @ts-ignore\n        nice: false,\n        min: min - d,\n        max: max + d,\n      });\n    }\n  }\n\n  // 平移分类的度量\n  // private translateCategory(dim, scale, normalPoint) {\n  // }\n}\n\nexport default ScaleTranslate;\n"]}