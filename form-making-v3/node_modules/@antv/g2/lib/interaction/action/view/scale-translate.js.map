{"version": 3, "file": "scale-translate.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/scale-translate.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAElC,8EAAgD;AAEhD;;;GAGG;AACH;IAA6B,0CAAe;IAA5C;QAAA,qEAoGC;QAnGW,gBAAU,GAAU,IAAI,CAAC;QACzB,cAAQ,GAAG,KAAK,CAAC;QACnB,gBAAU,GAAG,EAAE,CAAC;;IAiG1B,CAAC;IAhGC;;OAEG;IACI,8BAAK,GAAZ;QAAA,iBASC;QARC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,GAAG;YACb,IAAM,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACzB,IAAA,GAAG,GAAkB,KAAK,IAAvB,EAAE,GAAG,GAAa,KAAK,IAAlB,EAAE,MAAM,GAAK,KAAK,OAAV,CAAW;YACnC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,MAAM,QAAA,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACV,uDAAuD;IACvD,IAAI;IAEJ;;OAEG;IACI,4BAAG,GAAV;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,kCAAS,GAAhB;QAAA,iBAiBC;QAhBC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAChD,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACpD,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACjD,IAAM,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QAC3C,IAAM,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,GAAG;YACb,KAAI,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED,OAAO;IACC,qCAAY,GAApB,UAAqB,GAAG,EAAE,WAAW;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACpB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;aAC/C;YACD,uBAAuB;YACvB,mDAAmD;YACnD,IAAI;SACL;IACH,CAAC;IACD,cAAc;IACN,wCAAe,GAAvB,UAAwB,GAAG,EAAE,KAAK,EAAE,WAAW;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACzB,IAAA,KAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAjC,GAAG,SAAA,EAAE,GAAG,SAAyB,CAAC;QAC1C,IAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QACxB,IAAM,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnC,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG;gBACzB,aAAa;gBACb,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,KAAA;gBACH,GAAG,KAAA;aACJ,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;YACtB,aAAa;YACb,IAAI,EAAE,KAAK;YACX,GAAG,EAAE,GAAG,GAAG,CAAC;YACZ,GAAG,EAAE,GAAG,GAAG,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACV,uDAAuD;IACvD,IAAI;IAEJ;;OAEG;IACI,8BAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IACH,qBAAC;AAAD,CAAC,AApGD,CAA6B,yBAAe,GAoG3C;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { Point } from '../../../dependents';\nimport TransformAction from './scale-transform';\n\n/**\n * 拖拽 Scale 的 Action\n * @ignore\n */\nclass ScaleTranslate extends TransformAction {\n  protected startPoint: Point = null;\n  protected starting = false;\n  private startCache = {};\n  /**\n   * 开始\n   */\n  public start() {\n    this.startPoint = this.context.getCurrentPoint();\n    this.starting = true;\n    const dims = this.dims;\n    each(dims, (dim) => {\n      const scale = this.getScale(dim);\n      const { min, max, values } = scale;\n      this.startCache[dim] = { min, max, values };\n    });\n  }\n\n  // 平移分类的度量\n  // private translateCategory(dim, scale, normalPoint) {\n  // }\n\n  /**\n   * 结束\n   */\n  public end() {\n    this.startPoint = null;\n    this.starting = false;\n    this.startCache = {};\n  }\n\n  /**\n   * 平移\n   */\n  public translate() {\n    if (!this.starting) {\n      return;\n    }\n    const startPoint = this.startPoint;\n    const coord = this.context.view.getCoordinate();\n    const currentPoint = this.context.getCurrentPoint();\n    const normalStart = coord.invert(startPoint);\n    const noramlCurrent = coord.invert(currentPoint);\n    const dx = noramlCurrent.x - normalStart.x;\n    const dy = noramlCurrent.y - normalStart.y;\n    const view = this.context.view;\n    const dims = this.dims;\n    each(dims, (dim) => {\n      this.translateDim(dim, { x: dx * -1, y: dy * -1 });\n    });\n    view.render(true);\n  }\n\n  // 平移度量\n  private translateDim(dim, normalPoint) {\n    if (this.hasDim(dim)) {\n      const scale = this.getScale(dim);\n      if (scale.isLinear) {\n        this.translateLinear(dim, scale, normalPoint);\n      }\n      //  else { // 暂时仅处理连续字段\n      // this.translateCategory(dim, scale, normalPoint);\n      // }\n    }\n  }\n  // linear 度量平移\n  private translateLinear(dim, scale, normalPoint) {\n    const view = this.context.view;\n    const { min, max } = this.startCache[dim];\n    const range = max - min;\n    const d = normalPoint[dim] * range;\n    // 只有第一次缓存，否则无法回滚\n    if (!this.cacheScaleDefs[dim]) {\n      this.cacheScaleDefs[dim] = {\n        // @ts-ignore\n        nice: scale.nice,\n        min,\n        max,\n      };\n    }\n    view.scale(scale.field, {\n      // @ts-ignore\n      nice: false,\n      min: min + d,\n      max: max + d,\n    });\n  }\n\n  // 平移分类的度量\n  // private translateCategory(dim, scale, normalPoint) {\n  // }\n\n  /**\n   * 回滚\n   */\n  public reset() {\n    super.reset();\n    this.startPoint = null;\n    this.starting = false;\n  }\n}\n\nexport default ScaleTranslate;\n"]}