{"version": 3, "file": "mousewheel-scroll.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/mousewheel-scroll.ts"], "names": [], "mappings": ";;;AAAA,mCAAsD;AAEtD,wBAA4B;AAG5B,SAAS,WAAW,CAAC,KAAkB;IACrC,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,aAA2B,CAAC;IAC5D,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,IAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B;IAA+B,4CAAM;IAArC;;IAwBA,CAAC;IAvBQ,iCAAM,GAAb,UAAc,GAAI;QACV,IAAA,KAAkB,IAAI,CAAC,OAAO,EAA5B,IAAI,UAAA,EAAE,KAAK,WAAiB,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE;YAChC,OAAO;SACR;QAED,IAAM,UAAU,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,UAAU,KAAI,kBAAkB,CAAC;QACzD,IAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAE5D,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC;QACpC,IAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,IAAA,kBAAW,EAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACvD,IAAM,IAAI,GAAG,IAAA,WAAI,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEjC,IAAM,YAAY,GAAG,mBAAmB,CAAC,QAAQ,EAAE,CAAC;QACpD,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAElE,IAAM,SAAS,GAAG,YAAY,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACjF,IAAM,UAAU,GAAG,UAAU,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;QAC1D,IAAM,SAAS,GAAG,IAAA,YAAK,EAAC,SAAS,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IACH,uBAAC;AAAD,CAAC,AAxBD,CAA+B,UAAM,GAwBpC;AAED,kBAAe,gBAAgB,CAAC", "sourcesContent": ["import { clamp, size, valuesOfKey } from '@antv/util';\nimport { COMPONENT_TYPE } from '../../../constant';\nimport { Action } from '..';\nimport { LooseObject } from '../../../interface';\n\nfunction isWheelDown(event: LooseObject) {\n  const wheelEvent = event.gEvent.originalEvent as WheelEvent;\n  return wheelEvent.deltaY > 0;\n}\n\nconst DEFAULT_WHEELDELTA = 1;\nclass MousewheelScroll extends Action {\n  public scroll(arg?) {\n    const { view, event } = this.context;\n\n    if (!view.getOptions().scrollbar) {\n      return;\n    }\n\n    const wheelDelta = arg?.wheelDelta || DEFAULT_WHEELDELTA;\n    const scrollbarController = view.getController('scrollbar');\n\n    const xScale = view.getXScale();\n    const data = view.getOptions().data;\n    const dataSize = size(valuesOfKey(data, xScale.field));\n    const step = size(xScale.values);\n\n    const currentRatio = scrollbarController.getValue();\n    const currentStart = Math.floor((dataSize - step) * currentRatio);\n\n    const nextStart = currentStart + (isWheelDown(event) ? wheelDelta : -wheelDelta);\n    const correction = wheelDelta / (dataSize - step) / 10000;\n    const nextRatio = clamp(nextStart / (dataSize - step) + correction, 0, 1);\n    scrollbarController.setValue(nextRatio);\n  }\n}\n\nexport default MousewheelScroll;\n"]}