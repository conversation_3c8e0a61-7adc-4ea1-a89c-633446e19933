{"version": 3, "file": "rect.js", "sourceRoot": "", "sources": ["../../src/facet/rect.ts"], "names": [], "mappings": ";;;AAAA,mCAAwD;AACxD,wCAAwC;AAIxC,uCAAmD;AACnD,iCAAgC;AAEhC;;;GAGG;AACH;IAAkC,gCAAwB;IAA1D;;IAoKA,CAAC;IAnKW,4BAAa,GAAvB,UAAwB,IAAU,EAAE,KAAe;QACjD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAES,6BAAc,GAAxB,UAAyB,IAAU,EAAE,KAAe;QAClD,aAAa;IACf,CAAC;IAES,4BAAa,GAAvB;QACE,OAAO,IAAA,cAAO,EAAC,EAAE,EAAE,iBAAM,aAAa,WAAE,EAAE;YACxC,IAAI,EAAE,MAAM;YACZ,WAAW,uBACN,iBAAM,kBAAkB,WAAE,CAC9B;YACD,QAAQ,uBACH,iBAAM,kBAAkB,WAAE,CAC9B;SACF,CAAC,CAAC;IACL,CAAC;IAEM,qBAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QAEf,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YACtB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAED;;;OAGG;IACO,6BAAc,GAAxB,UAAyB,IAAa;QAAtC,iBA+CC;QA9CO,IAAA,KAAA,eAA0B,IAAI,CAAC,GAAG,CAAC,MAAM,IAAA,EAAxC,WAAW,QAAA,EAAE,QAAQ,QAAmB,CAAC;QAEhD,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,IAAI,YAAY,GAAa,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,SAAS,GAAa,CAAC,EAAE,CAAC,CAAC;QAE/B,IAAI,WAAW,EAAE;YACf,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACtD,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAAC;SAC1C;QACD,IAAI,QAAQ,EAAE;YACZ,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAChD,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC;SACpC;QAED,kBAAkB;QAClB,YAAY,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,MAAM;YAChC,SAAS,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,MAAM;gBAC7B,IAAM,UAAU,GAAG;oBACjB,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE;oBACzD,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;iBACpD,CAAC;gBACF,IAAM,SAAS,GAAG,IAAA,aAAM,EAAC,IAAI,EAAE,KAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;gBAEpE,IAAM,KAAK,GAAa;oBACtB,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,IAAI;oBACnB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAI,CAAC,SAAS,CAAC,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,MAAM,CAAC;oBAE3E,WAAW,EAAE,IAAI;oBACjB,QAAQ,EAAE,IAAI;oBACd,WAAW,aAAA;oBACX,QAAQ,UAAA;oBACR,WAAW,EAAE,MAAM;oBACnB,QAAQ,EAAE,MAAM;oBAChB,kBAAkB,oBAAA;oBAClB,eAAe,iBAAA;iBAChB,CAAC;gBACF,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,0BAAW,GAAnB;QAAA,iBAiCC;QAhCC,IAAA,WAAI,EAAC,IAAI,CAAC,MAAM,EAAE,UAAC,KAAe,EAAE,UAAkB;YAC5C,IAAA,WAAW,GAAgE,KAAK,YAArE,EAAE,QAAQ,GAAsD,KAAK,SAA3D,EAAE,kBAAkB,GAAkC,KAAK,mBAAvC,EAAE,WAAW,GAAqB,KAAK,YAA1B,EAAE,QAAQ,GAAW,KAAK,SAAhB,EAAE,IAAI,GAAK,KAAK,KAAV,CAAW;YAEzF,MAAM;YACN,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAClB,IAAM,SAAS,GAAG,IAAA,UAAG,EAAC,KAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;gBACzD,IAAM,MAAM,GAAG,IAAA,cAAO,EACpB;oBACE,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,CAAqB;oBAC3C,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW;iBAC1D,EACD,IAAA,0BAAkB,EAAC,oBAAS,CAAC,GAAG,CAAC,EACjC,KAAI,CAAC,GAAG,CAAC,WAAW,CACrB,CAAC;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAChC;YACD,QAAQ;YACR,IAAI,WAAW,KAAK,kBAAkB,GAAG,CAAC,EAAE;gBAC1C,IAAM,SAAS,GAAG,IAAA,UAAG,EAAC,KAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBACtD,IAAM,MAAM,GAAG,IAAA,cAAO,EACpB;oBACE,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,CAAqB;oBAC7C,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ;iBACpD,EACD,IAAA,0BAAkB,EAAC,oBAAS,CAAC,KAAK,CAAC,EACnC,KAAI,CAAC,GAAG,CAAC,QAAQ,CAClB,CAAC;gBAEF,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAChC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACO,6BAAc,GAAxB,UAAyB,CAAS,EAAE,IAAS,EAAE,MAAe,EAAE,KAAe;QAC7E,QAAQ;QACR,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,eAAe,GAAG,CAAC,EAAE;YAChD,6CACK,MAAM,KACT,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,IAAI,IACX;SACH;aAAM,IAAI,KAAK,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YAC/E,QAAQ;YACR,6CACK,MAAM,KACT,KAAK,EAAE,IAAI,IACX;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACO,6BAAc,GAAxB,UAAyB,CAAS,EAAE,IAAS,EAAE,MAAe,EAAE,KAAe;QAC7E,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,EAAE;YAC3B,6CACK,MAAM,KACT,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,IAAI,IACX;SACH;aAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YACzE,6CACK,MAAM,KACT,KAAK,EAAE,IAAI,IACX;SACH;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,WAAC;AAAD,CAAC,AApKD,CAAkC,aAAK,GAoKtC", "sourcesContent": ["import { deepMix, each, filter, get } from '@antv/util';\nimport { DIRECTION } from '../constant';\nimport { AxisCfg, Datum, RectCfg, RectData } from '../interface';\n\nimport View from '../chart/view';\nimport { getFactTitleConfig } from '../util/facet';\nimport { Facet } from './facet';\n\n/**\n * @ignore\n * 矩阵分面\n */\nexport default class Rect extends Facet<RectCfg, RectData> {\n  protected afterEachView(view: View, facet: RectData) {\n    this.processAxis(view, facet);\n  }\n\n  protected beforeEachView(view: View, facet: RectData) {\n    // do nothing\n  }\n\n  protected getDefaultCfg() {\n    return deepMix({}, super.getDefaultCfg(), {\n      type: 'rect',\n      columnTitle: {\n        ...super.getDefaultTitleCfg(),\n      },\n      rowTitle: {\n        ...super.getDefaultTitleCfg(),\n      },\n    });\n  }\n\n  public render() {\n    super.render();\n\n    if (this.cfg.showTitle) {\n      this.renderTitle();\n    }\n  }\n\n  /**\n   * 生成矩阵分面的分面数据\n   * @param data\n   */\n  protected generateFacets(data: Datum[]): RectData[] {\n    const [columnField, rowField] = this.cfg.fields;\n\n    const rst = [];\n    let columnValuesLength = 1;\n    let rowValuesLength = 1;\n\n    let columnValues: string[] = [''];\n    let rowValues: string[] = [''];\n\n    if (columnField) {\n      columnValues = this.getFieldValues(data, columnField);\n      columnValuesLength = columnValues.length;\n    }\n    if (rowField) {\n      rowValues = this.getFieldValues(data, rowField);\n      rowValuesLength = rowValues.length;\n    }\n\n    // 获取每个维度对应的数据配置片段\n    columnValues.forEach((xVal, xIndex) => {\n      rowValues.forEach((yVal, yIndex) => {\n        const conditions = [\n          { field: columnField, value: xVal, values: columnValues },\n          { field: rowField, value: yVal, values: rowValues },\n        ];\n        const facetData = filter(data, this.getFacetDataFilter(conditions));\n\n        const facet: RectData = {\n          type: this.cfg.type,\n          data: facetData,\n          region: this.getRegion(rowValuesLength, columnValuesLength, xIndex, yIndex),\n\n          columnValue: xVal,\n          rowValue: yVal,\n          columnField,\n          rowField,\n          columnIndex: xIndex,\n          rowIndex: yIndex,\n          columnValuesLength,\n          rowValuesLength,\n        };\n        rst.push(facet);\n      });\n    });\n\n    return rst;\n  }\n\n  private renderTitle(): void {\n    each(this.facets, (facet: RectData, facetIndex: number) => {\n      const { columnIndex, rowIndex, columnValuesLength, columnValue, rowValue, view } = facet;\n\n      // top\n      if (rowIndex === 0) {\n        const formatter = get(this.cfg.columnTitle, 'formatter');\n        const config = deepMix(\n          {\n            position: ['50%', '0%'] as [string, string],\n            content: formatter ? formatter(columnValue) : columnValue,\n          },\n          getFactTitleConfig(DIRECTION.TOP),\n          this.cfg.columnTitle\n        );\n\n        view.annotation().text(config);\n      }\n      // right\n      if (columnIndex === columnValuesLength - 1) {\n        const formatter = get(this.cfg.rowTitle, 'formatter');\n        const config = deepMix(\n          {\n            position: ['100%', '50%'] as [string, string],\n            content: formatter ? formatter(rowValue) : rowValue,\n          },\n          getFactTitleConfig(DIRECTION.RIGHT),\n          this.cfg.rowTitle\n        );\n\n        view.annotation().text(config);\n      }\n    });\n  }\n\n  /**\n   * 设置 x 坐标轴的文本、title 是否显示\n   * @param x\n   * @param axes\n   * @param option\n   * @param facet\n   */\n  protected getXAxisOption(x: string, axes: any, option: AxisCfg, facet: RectData): object {\n    // 非最后一行\n    if (facet.rowIndex !== facet.rowValuesLength - 1) {\n      return {\n        ...option,\n        title: null,\n        label: null,\n      };\n    } else if (facet.columnIndex !== Math.floor((facet.columnValuesLength - 1) / 2)) {\n      // 不是中间列\n      return {\n        ...option,\n        title: null,\n      };\n    }\n    return option;\n  }\n\n  /**\n   * 设置 y 坐标轴的文本、title 是否显示\n   * @param y\n   * @param axes\n   * @param option\n   * @param facet\n   */\n  protected getYAxisOption(y: string, axes: any, option: AxisCfg, facet: RectData): object {\n    if (facet.columnIndex !== 0) {\n      return {\n        ...option,\n        title: null,\n        label: null,\n      };\n    } else if (facet.rowIndex !== Math.floor((facet.rowValuesLength - 1) / 2)) {\n      return {\n        ...option,\n        title: null,\n      };\n    }\n    return option;\n  }\n}\n"]}