{"version": 3, "file": "variable.js", "sourceRoot": "", "sources": ["../../../../src/chart/layout/constraint/variable.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAEtC;;GAEG;AACH;IAoBE;;;OAGG;IACH,kBAAY,IAAa;QAvBzB;;WAEG;QACI,SAAI,GAAG,EAAE,CAAC;QAqBf,IAAI,CAAC,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,sBAAK,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAE,CAAC;IAC3C,CAAC;IAfD;;;;OAIG;IACW,mBAAU,GAAxB,UAAyB,CAAM;QAC7B,OAAO,CAAC,YAAY,QAAQ,CAAC;IAC/B,CAAC;IASH,eAAC;AAAD,CAAC,AA3BD,IA2BC;AA3BY,4BAAQ", "sourcesContent": ["import { uniqueId } from '@antv/util';\n\n/**\n * 定义一个变量\n */\nexport class Variable {\n  /**\n   * 变量的名称\n   */\n  public name = '';\n\n  /**\n   * 实际的值\n   */\n  public value: number;\n\n  /**\n   * 判断是否为一个变量\n   * @param v\n   * @returns\n   */\n  public static isVariable(v: any): v is Variable {\n    return v instanceof Variable;\n  }\n\n  /**\n   * 构造方法\n   * @param name\n   */\n  constructor(name?: string) {\n    this.name = name ?? `𝒳${uniqueId('_')}`;\n  }\n}\n"]}