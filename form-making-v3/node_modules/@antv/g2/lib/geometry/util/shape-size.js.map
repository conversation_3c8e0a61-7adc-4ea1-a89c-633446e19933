{"version": 3, "file": "shape-size.js", "sourceRoot": "", "sources": ["../../../src/geometry/util/shape-size.ts"], "names": [], "mappings": ";;;AAAA,mCAAmE;AACnE,oDAA4D;AAE5D,kBAAkB;AAClB,SAAS,eAAe,CAAC,GAAG,EAAE,KAAK;IACjC,IAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;IACzB,IAAI,SAAS,GAAG,GAAG,CAAC;IACpB,IAAI,IAAA,eAAQ,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,gCAAgC;QAChC,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,UAAC,CAAS;YAC5B,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;KACJ;IACD,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,QAAQ,GAAG,GAAG,EAAE;YAClB,QAAQ,GAAG,GAAG,CAAC;SAChB;KACF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO;IACvC,IAAI,OAAO,EAAE;QACX,IAAM,SAAS,GAAG,IAAA,cAAO,EAAC,SAAS,CAAC,CAAC;QACrC,IAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC,MAAM,CAAC;KACtB;IAED,OAAO,SAAS,CAAC,MAAM,CAAC;AAC1B,CAAC;AAED,cAAc;AACd,SAAgB,cAAc,CAAC,QAAQ;IACrC,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC7B,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9B,IAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC;IAC7C,IAAI,KAAK,GAAW,OAAO,CAAC,MAAM,CAAC;IACnC,IAAM,gBAAgB,GAAG,IAAA,gCAAmB,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClE,YAAY;IACJ,IAAA,eAAe,GAAmB,QAAQ,gBAA3B,EAAE,YAAY,GAAK,QAAQ,aAAb,CAAc;IACnD,YAAY;IACZ,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC;IACvE,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC;IACvE,IAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,CAAC;IAC7E,IAAM,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,IAAI,KAAK,CAAC,qBAAqB,CAAC;IAC5F,IAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC;IAEvE,cAAc;IACd,IAAI,MAAM,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACzC,oDAAoD;QACpD,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,IAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAClD,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;QAC7C,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,EAAE;YAC1B,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;SACxB;KACF;IAED,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAC3B,IAAI,cAAc,GAAG,CAAC,GAAG,KAAK,CAAC;IAC/B,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,UAAU,CAAC,OAAO,EAAE;QACtB,QAAQ;QACR,IAAI,UAAU,CAAC,YAAY,IAAI,KAAK,GAAG,CAAC,EAAE;YACxC,WAAW;YACX,EAAE,GAAG,qBAAqB,CAAC;SAC5B;aAAM;YACL,EAAE,GAAG,cAAc,CAAC;SACrB;KACF;SAAM;QACL,SAAS;QACT,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,cAAc,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;SACvC;QACD,EAAE,GAAG,gBAAgB,CAAC;KACvB;IAED,QAAQ;IACR,IAAI,CAAC,IAAA,YAAK,EAAC,eAAe,CAAC,IAAI,eAAe,IAAI,CAAC,EAAE;QACnD,UAAU;QACV,IAAM,yBAAyB,GAAG,eAAe,GAAG,gBAAgB,CAAC;QACrE,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,KAAK,CAAC;KACxE;SAAM;QACL,OAAO;QACP,cAAc,IAAI,EAAE,CAAC;KACtB;IACD,QAAQ;IACR,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QAC/B,IAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAChD,IAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,IAAM,UAAU,GAAG,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,IAAA,YAAK,EAAC,YAAY,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;YAC7C,YAAY;YACZ,IAAM,sBAAsB,GAAG,YAAY,GAAG,gBAAgB,CAAC;YAC/D,cAAc,GAAG,CAAC,cAAc,GAAG,sBAAsB,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;SAC5F;aAAM,IAAI,CAAC,IAAA,YAAK,EAAC,eAAe,CAAC,IAAI,eAAe,IAAI,CAAC,EAAE;YAC1D,sCAAsC;YACtC,cAAc,IAAI,EAAE,CAAC;YACrB,cAAc,GAAG,cAAc,GAAG,UAAU,CAAC;SAC9C;aAAM;YACL,eAAe;YACf,cAAc,GAAG,cAAc,GAAG,UAAU,CAAC;SAC9C;QACD,cAAc,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3D;IAED,UAAU;IACV,IAAI,CAAC,IAAA,YAAK,EAAC,cAAc,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE;QACjD,IAAM,wBAAwB,GAAG,cAAc,GAAG,gBAAgB,CAAC;QACnE,IAAI,cAAc,GAAG,wBAAwB,EAAE;YAC7C,cAAc,GAAG,wBAAwB,CAAC;SAC3C;KACF;IAED,wBAAwB;IACxB,IAAI,CAAC,IAAA,YAAK,EAAC,cAAc,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE;QACjD,IAAM,wBAAwB,GAAG,cAAc,GAAG,gBAAgB,CAAC;QACnE,IAAI,cAAc,GAAG,wBAAwB,EAAE;YAC7C,cAAc,GAAG,wBAAwB,CAAC;SAC3C;KACF;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AA7FD,wCA6FC", "sourcesContent": ["import { flatten, isString, valuesOfKey, isNil } from '@antv/util';\nimport { getXDimensionLength } from '../../util/coordinate';\n\n// 已经排序后的数据查找距离最小的\nfunction findMinDistance(arr, scale) {\n  const count = arr.length;\n  let sourceArr = arr;\n  if (isString(sourceArr[0])) {\n    // 日期类型的 values 经常上文本类型，所以需要转换一下\n    sourceArr = arr.map((v: string) => {\n      return scale.translate(v);\n    });\n  }\n  let distance = sourceArr[1] - sourceArr[0];\n  for (let i = 2; i < count; i++) {\n    const tmp = sourceArr[i] - sourceArr[i - 1];\n    if (distance > tmp) {\n      distance = tmp;\n    }\n  }\n  return distance;\n}\n\nfunction getDodgeCount(dataArray, dodgeBy) {\n  if (dodgeBy) {\n    const mergeData = flatten(dataArray);\n    const values = valuesOfKey(mergeData, dodgeBy);\n    return values.length;\n  }\n\n  return dataArray.length;\n}\n\n/** @ignore */\nexport function getDefaultSize(geometry): number {\n  const theme = geometry.theme;\n  const coordinate = geometry.coordinate;\n  const xScale = geometry.getXScale();\n  const xValues = xScale.values;\n  const dataArray = geometry.beforeMappingData;\n  let count: number = xValues.length;\n  const xDimensionLength = getXDimensionLength(geometry.coordinate);\n  // 获取柱宽相关配置项\n  const { intervalPadding, dodgePadding } = geometry;\n  // 兼容theme配置\n  const maxColumnWidth = geometry.maxColumnWidth || theme.maxColumnWidth;\n  const minColumnWidth = geometry.minColumnWidth || theme.minColumnWidth;\n  const columnWidthRatio = geometry.columnWidthRatio || theme.columnWidthRatio;\n  const multiplePieWidthRatio = geometry.multiplePieWidthRatio || theme.multiplePieWidthRatio;\n  const roseWidthRatio = geometry.roseWidthRatio || theme.roseWidthRatio;\n\n  // 线性情况下count值\n  if (xScale.isLinear && xValues.length > 1) {\n    // Linear 类型用户有可能设置了 min, max 范围所以需要根据数据最小区间计算 count\n    xValues.sort();\n    const interval = findMinDistance(xValues, xScale);\n    count = (xScale.max - xScale.min) / interval;\n    if (xValues.length > count) {\n      count = xValues.length;\n    }\n  }\n\n  const range = xScale.range;\n  let normalizedSize = 1 / count;\n  let wr = 1;\n  if (coordinate.isPolar) {\n    // 极坐标场景\n    if (coordinate.isTransposed && count > 1) {\n      // 极坐标下多层环图\n      wr = multiplePieWidthRatio;\n    } else {\n      wr = roseWidthRatio;\n    }\n  } else {\n    // 非极坐标场景\n    if (xScale.isLinear) {\n      normalizedSize *= range[1] - range[0];\n    }\n    wr = columnWidthRatio;\n  }\n\n  // 基础柱状图\n  if (!isNil(intervalPadding) && intervalPadding >= 0) {\n    // 配置组间距情况\n    const normalizedIntervalPadding = intervalPadding / xDimensionLength;\n    normalizedSize = (1 - (count - 1) * normalizedIntervalPadding) / count;\n  } else {\n    // 默认情况\n    normalizedSize *= wr;\n  }\n  // 分组柱状图\n  if (geometry.getAdjust('dodge')) {\n    const dodgeAdjust = geometry.getAdjust('dodge');\n    const dodgeBy = dodgeAdjust.dodgeBy;\n    const dodgeCount = getDodgeCount(dataArray, dodgeBy);\n    if (!isNil(dodgePadding) && dodgePadding >= 0) {\n      // 仅配置组内间距情况\n      const normalizedDodgePadding = dodgePadding / xDimensionLength;\n      normalizedSize = (normalizedSize - normalizedDodgePadding * (dodgeCount - 1)) / dodgeCount;\n    } else if (!isNil(intervalPadding) && intervalPadding >= 0) {\n      // 设置组间距但未设置组内间距情况，避免组间距过小导致图形重叠，需乘以wr\n      normalizedSize *= wr;\n      normalizedSize = normalizedSize / dodgeCount;\n    } else {\n      // 组间距和组内间距均未配置\n      normalizedSize = normalizedSize / dodgeCount;\n    }\n    normalizedSize = normalizedSize >= 0 ? normalizedSize : 0;\n  }\n\n  // 最大和最小限制\n  if (!isNil(maxColumnWidth) && maxColumnWidth >= 0) {\n    const normalizedMaxColumnWidth = maxColumnWidth / xDimensionLength;\n    if (normalizedSize > normalizedMaxColumnWidth) {\n      normalizedSize = normalizedMaxColumnWidth;\n    }\n  }\n\n  // \bminColumnWidth可能设置为0\n  if (!isNil(minColumnWidth) && minColumnWidth >= 0) {\n    const normalizedMinColumnWidth = minColumnWidth / xDimensionLength;\n    if (normalizedSize < normalizedMinColumnWidth) {\n      normalizedSize = normalizedMinColumnWidth;\n    }\n  }\n\n  return normalizedSize;\n}\n"]}