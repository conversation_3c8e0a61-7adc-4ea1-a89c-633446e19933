{"version": 3, "file": "parse-fields.js", "sourceRoot": "", "sources": ["../../../src/geometry/util/parse-fields.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AAErC,cAAc;AACd,SAAgB,WAAW,CAAC,KAAwB;IAClD,IAAI,IAAA,cAAO,EAAC,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AAND,kCAMC", "sourcesContent": ["import { isArray } from '@antv/util';\n\n/** @ignore */\nexport function parseFields(field: string | string[]): string[] {\n  if (isArray(field)) {\n    return field;\n  }\n\n  return field.split('*');\n}\n"]}