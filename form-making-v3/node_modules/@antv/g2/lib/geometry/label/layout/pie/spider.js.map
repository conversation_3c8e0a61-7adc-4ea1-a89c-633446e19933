{"version": 3, "file": "spider.js", "sourceRoot": "", "sources": ["../../../../../src/geometry/label/layout/pie/spider.ts"], "names": [], "mappings": ";;;;AACA,mCAAgE;AAChE,sDAA6D;AAE7D,+BAAuC;AACvC,wDAAuD;AAGvD,mBAAmB;AACnB,IAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,sDAAsD;AACtD,IAAM,cAAc,GAAG,CAAC,CAAC;AACzB,iBAAiB;AACjB,IAAM,sBAAsB,GAAG,CAAC,CAAC;AAEjC,SAAS,aAAa,CAAC,IAAoB,EAAE,UAAsB,EAAE,OAAgB;IACnF,WAAW;IACX,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,UAAU;IACV,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,IAAM,UAAU,GAAG;QACjB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC;QACxE,CAAC,EAAE,IAAI,CAAC,CAAC;KACV,CAAC;IACF,IAAM,eAAe,GAAG,IAAA,2BAAgB,EAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACrG,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC;IAChD,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;IAE1D,IAAM,QAAQ,GAAG,IAAA,2BAAgB,EAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1E,IAAI,IAAI,GAAG,EAAE,CAAC;IAEd,oBAAoB;IACpB,IAAI,UAAU,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE;QACtC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;QAEpB,cAAc;QACd,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;YAChD,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC1D,IAAI,UAAU,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE;gBACpC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aACb;iBAAM;gBACL,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;aACtC;SACF;QACD,eAAe;QACf,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;YAC9C,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC1D,IAAI,UAAU,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE;gBACpC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aACb;iBAAM;gBACL,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;aACtC;SACF;QACD,eAAe;QACf,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;YAC5B,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC1D,IAAI,UAAU,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE;gBACpC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aACb;iBAAM;gBACL,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;aACtC;SACF;QACD,eAAe;QACf,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;YAC7B,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YAC1D,IAAI,UAAU,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE;gBACpC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aACb;iBAAM;gBACL,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;aACtC;SACF;KACF;IAED,IAAI,GAAG;QACL,YAAK,UAAU,CAAC,CAAC,cAAI,UAAU,CAAC,CAAC,CAAE;QACnC,YAAK,EAAE,CAAC,CAAC,cAAI,EAAE,CAAC,CAAC,CAAE;QACnB,YAAK,EAAE,CAAC,CAAC,cAAI,EAAE,CAAC,CAAC,CAAE;QACnB,YAAK,eAAe,CAAC,CAAC,cAAI,eAAe,CAAC,CAAC,CAAE;QAC7C,YAAK,QAAQ,CAAC,CAAC,cAAI,QAAQ,CAAC,CAAC,CAAE;KAChC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,IAAI,CAAC,SAAS,GAAG,IAAA,cAAO,EAAC,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;AACzD,CAAC;AAED;;;GAGG;AACH,SAAgB,oBAAoB,CAAC,KAAkB,EAAE,MAAgB,EAAE,MAA2B,EAAE,MAAY;;IAClH,UAAU;IACV,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC5D,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IAED,WAAW;IACX,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,UAAU;IACV,IAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,mBAAmB;IACnB,IAAM,SAAS,GAAqC,EAAE,CAAC;;QACvD,KAAyB,IAAA,WAAA,iBAAA,MAAM,CAAA,8BAAA,kDAAE;YAA5B,IAAM,UAAU,mBAAA;YACnB,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC;SAC9C;;;;;;;;;IAED,IAAM,WAAW,GAAW,IAAA,UAAG,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IAC7D,IAAM,WAAW,GAAW,IAAI,CAAC,GAAG,CAAC,IAAA,UAAG,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAEpF,iCAAiC;IACjC,IAAA,WAAI,EAAC,KAAK,EAAE,UAAC,IAAI;QACf,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,IAAM,KAAK,GAAG,IAAA,UAAG,EAAC,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChF,IAAM,OAAO,GAAG,CAAC,IAAA,YAAK,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;QACrE,IAAM,eAAe,GAAG,IAAA,2BAAgB,EAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAErG,IAAM,WAAW,GAAG,WAAW,GAAG,OAAO,CAAC;QAC1C,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QAChE,IAAI,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEK,IAAA,KAAK,GAAU,UAAU,MAApB,EAAE,GAAG,GAAK,UAAU,IAAf,CAAgB;IAClC,IAAM,aAAa,GAAG,MAAM,CAAC;IAC7B,IAAM,cAAc,GAAG,OAAO,CAAC;IAC/B,0BAA0B;IAC1B,IAAM,cAAc,GAAG,IAAA,cAAO,EAAC,KAAK,EAAE,UAAC,IAAI,IAAK,OAAA,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,EAApD,CAAoD,CAAC,CAAC;IAEtG,+BAA+B;IAC/B,IAAI,WAAW,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;IAE3D,IAAA,WAAI,EAAC,cAAc,EAAE,UAAC,IAAsB;QAC1C,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC7C,IAAI,UAAU,GAAG,WAAW,EAAE;YAC5B,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D;IACH,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,IAAM,oBAAoB,GAAG;QAC3B,IAAI,EAAE,KAAK,CAAC,CAAC;QACb,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC;QAChC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC;KACjC,CAAC;IAEF,wBAAwB;IACxB,IAAA,WAAI,EAAC,cAAc,EAAE,UAAC,IAAI,EAAE,GAAG;QAC7B,IAAM,wBAAwB,GAAG,WAAW,GAAG,WAAW,CAAC;QAC3D,IAAI,IAAI,CAAC,MAAM,GAAG,wBAAwB,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACb,0BAA0B;gBAC1B,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,SAAyB,EAAE,GAAG;gBACxC,IAAI,GAAG,GAAG,wBAAwB,EAAE;oBAClC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;oBAC9C,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;iBAC5B;YACH,CAAC,CAAC,CAAC;SACJ;QACD,IAAA,oBAAa,EAAC,IAAI,EAAE,WAAW,EAAE,oBAAoB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC;IACzC,IAAM,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC;IAEvC,8CAA8C;IAC9C,IAAA,WAAI,EAAC,cAAc,EAAE,UAAC,IAAI,EAAE,GAAG;QAC7B,IAAM,OAAO,GAAG,GAAG,KAAK,cAAc,CAAC;QAEvC,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,IAAI;YACd,IAAM,KAAK,GAAW,IAAA,UAAG,EAAC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YACD,uBAAuB;YACvB,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE;gBACpC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC5B,OAAO;aACR;YAED,IAAM,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAM,GAAG,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;YACzC,IAAM,WAAW,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,6BAA6B,EAAE,CAAC;YAE/G,IAAA,qBAAS,EAAC,YAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAEzG,oBAAoB;YACpB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AA5GD,oDA4GC", "sourcesContent": ["import { BBox, IGroup, IShape } from '@antv/g-base';\nimport { each, get, isNil, deepMix, groupBy } from '@antv/util';\nimport { polarToCartesian } from '../../../../util/graphics';\nimport { LabelItem, PolarLabelItem } from '../../interface';\nimport { antiCollision } from './util';\nimport { translate } from '../../../../util/transform';\nimport { Coordinate } from '@antv/coord';\n\n/** 拐点偏移量, 暂不可配置 */\nconst INFLECTION_OFFSET = 4;\n/** 标签偏移量, distance between label and edge: offsetX */\nconst LABEL_OFFSET_X = 4;\n/** 标签与牵引线的偏移量 */\nconst LABEL_TEXT_LINE_OFFSET = 4;\n\nfunction drawLabelline(item: PolarLabelItem, coordinate: Coordinate, inRight: boolean) {\n  /** 坐标圆心 */\n  const center = coordinate.getCenter();\n  /** 圆半径 */\n  const radius = coordinate.getRadius();\n  const startPoint = {\n    x: item.x - (inRight ? LABEL_TEXT_LINE_OFFSET : -LABEL_TEXT_LINE_OFFSET),\n    y: item.y,\n  };\n  const inflectionPoint = polarToCartesian(center.x, center.y, radius + INFLECTION_OFFSET, item.angle);\n  const p1 = { x: startPoint.x, y: startPoint.y };\n  const p2 = { x: inflectionPoint.x, y: inflectionPoint.y };\n\n  const endPoint = polarToCartesian(center.x, center.y, radius, item.angle);\n  let path = '';\n\n  // 文本被调整下去了，则添加拐点连接线\n  if (startPoint.y !== inflectionPoint.y) {\n    const offset = inRight ? 4 : -4;\n    p1.y = startPoint.y;\n\n    /** 是否在第一象限 */\n    if (item.angle < 0 && item.angle >= -Math.PI / 2) {\n      p1.x = Math.max(inflectionPoint.x, startPoint.x - offset);\n      if (startPoint.y < inflectionPoint.y) {\n        p2.y = p1.y;\n      } else {\n        p2.y = inflectionPoint.y;\n        p2.x = Math.max(p2.x, p1.x - offset);\n      }\n    }\n    /** 是否在 第二象限 */\n    if (item.angle > 0 && item.angle < Math.PI / 2) {\n      p1.x = Math.max(inflectionPoint.x, startPoint.x - offset);\n      if (startPoint.y > inflectionPoint.y) {\n        p2.y = p1.y;\n      } else {\n        p2.y = inflectionPoint.y;\n        p2.x = Math.max(p2.x, p1.x - offset);\n      }\n    }\n    /** 是否在 第三象限 */\n    if (item.angle > Math.PI / 2) {\n      p1.x = Math.min(inflectionPoint.x, startPoint.x - offset);\n      if (startPoint.y > inflectionPoint.y) {\n        p2.y = p1.y;\n      } else {\n        p2.y = inflectionPoint.y;\n        p2.x = Math.min(p2.x, p1.x - offset);\n      }\n    }\n    /** 是否在 第四象限 */\n    if (item.angle < -Math.PI / 2) {\n      p1.x = Math.min(inflectionPoint.x, startPoint.x - offset);\n      if (startPoint.y < inflectionPoint.y) {\n        p2.y = p1.y;\n      } else {\n        p2.y = inflectionPoint.y;\n        p2.x = Math.min(p2.x, p1.x - offset);\n      }\n    }\n  }\n\n  path = [\n    `M ${startPoint.x},${startPoint.y}`,\n    `L ${p1.x},${p1.y}`,\n    `L ${p2.x},${p2.y}`,\n    `L ${inflectionPoint.x},${inflectionPoint.y}`,\n    `L ${endPoint.x},${endPoint.y}`,\n  ].join(' ');\n  item.labelLine = deepMix({}, item.labelLine, { path });\n}\n\n/**\n * 饼图标签 spider 布局, 只适用于 pie-spider 的标签类型\n * region 应该是 labelsRenderer 容器的范围限制(便于后续组件间布局)\n */\nexport function pieSpiderLabelLayout(items: LabelItem[], labels: IGroup[], shapes: IShape[] | IGroup[], region: BBox) {\n  /** 坐标系 */\n  const coordinate = labels[0] && labels[0].get('coordinate');\n  if (!coordinate) {\n    return;\n  }\n\n  /** 坐标圆心 */\n  const center = coordinate.getCenter();\n  /** 圆半径 */\n  const radius = coordinate.getRadius();\n  /** label shapes */\n  const labelsMap: Record<string /** id */, IGroup> = {};\n  for (const labelShape of labels) {\n    labelsMap[labelShape.get('id')] = labelShape;\n  }\n\n  const labelHeight: number = get(items[0], 'labelHeight', 14);\n  const labelOffset: number = Math.max(get(items[0], 'offset', 0), INFLECTION_OFFSET);\n\n  // step 1: adjust items to spider\n  each(items, (item) => {\n    if (!item) return;\n    const label = get(labelsMap, [item.id]);\n    if (!label) return;\n\n    const inRight = item.x > center.x || (item.x === center.x && item.y > center.y);\n    const offsetX = !isNil(item.offsetX) ? item.offsetX : LABEL_OFFSET_X;\n    const inflectionPoint = polarToCartesian(center.x, center.y, radius + INFLECTION_OFFSET, item.angle);\n\n    const totalOffset = labelOffset + offsetX;\n    item.x = center.x + (inRight ? 1 : -1) * (radius + totalOffset);\n    item.y = inflectionPoint.y;\n  });\n\n  const { start, end } = coordinate;\n  const LEFT_HALF_KEY = 'left';\n  const RIGHT_HALF_KEY = 'right';\n  // step 1: separate labels\n  const separateLabels = groupBy(items, (item) => (item.x < center.x ? LEFT_HALF_KEY : RIGHT_HALF_KEY));\n\n  // step2: calculate totalHeight\n  let totalHeight = (radius + labelOffset) * 2 + labelHeight;\n\n  each(separateLabels, (half: PolarLabelItem[]) => {\n    const halfHeight = half.length * labelHeight;\n    if (halfHeight > totalHeight) {\n      totalHeight = Math.min(halfHeight, Math.abs(start.y - end.y));\n    }\n  });\n\n  /** labels 容器的范围(后续根据组件的布局设计进行调整) */\n  const labelsContainerRange = {\n    minX: start.x,\n    maxX: end.x,\n    minY: center.y - totalHeight / 2,\n    maxY: center.y + totalHeight / 2,\n  };\n\n  // step 3: antiCollision\n  each(separateLabels, (half, key) => {\n    const maxLabelsCountForOneSide = totalHeight / labelHeight;\n    if (half.length > maxLabelsCountForOneSide) {\n      half.sort((a, b) => {\n        // sort by percentage DESC\n        return b.percent - a.percent;\n      });\n\n      each(half, (labelItem: PolarLabelItem, idx) => {\n        if (idx > maxLabelsCountForOneSide) {\n          labelsMap[labelItem.id].set('visible', false);\n          labelItem.invisible = true;\n        }\n      });\n    }\n    antiCollision(half, labelHeight, labelsContainerRange);\n  });\n\n  const startY = labelsContainerRange.minY;\n  const endY = labelsContainerRange.maxY;\n\n  // step4: applyTo labels and adjust labelLines\n  each(separateLabels, (half, key) => {\n    const inRight = key === RIGHT_HALF_KEY;\n\n    each(half, (item) => {\n      const label: IGroup = get(labelsMap, item && [item.id]);\n      if (!label) {\n        return;\n      }\n      // out of range, hidden\n      if (item.y < startY || item.y > endY) {\n        label.set('visible', false);\n        return;\n      }\n\n      const labelContent = label.getChildByIndex(0);\n      const box = labelContent.getCanvasBBox();\n      const originalPos = { x: inRight ? box.x : box.maxX, y: box.y + box.height / 2 /** vertical-align: middle */ };\n\n      translate(labelContent as any, item.x - originalPos.x /** 从 pos.x 移动到 item.x */, item.y - originalPos.y);\n\n      // adjust labelLines\n      if (item.labelLine) {\n        drawLabelline(item, coordinate, inRight);\n      }\n    });\n  });\n}\n"]}