<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Selector</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <button class="target">Click me</button>
    <button class="target">Click me</button>
    <button class="target">Click me</button>

    <!-- 2. Include library -->
    <script src="../dist/good-listener.js"></script>

    <!-- 3. Add listener by passing a string selector -->
    <script>
    listen('.target', 'click', function(e) {
        console.info(e);
    });
    </script>
</body>
</html>
