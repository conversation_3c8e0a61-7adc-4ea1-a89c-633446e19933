<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Selector</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <button data-a>Click me</button>
    <button data-a>Click me</button>
    <button data-a>Click me</button>
    <button data-b>Click me</button>
    <button data-b>Click me</button>
    <button data-b>Click me</button>

    <!-- 2. Include library -->
    <script src="../dist/good-listener.js"></script>

    <!-- 3. Add listener by passing a string selector -->
    <script>
    listen('[data-a]', 'click', function(e) {
        console.info(e);
    });

    listen('[data-b]', 'click', function(e) {
        console.info(e);
    });
    </script>
</body>
</html>
