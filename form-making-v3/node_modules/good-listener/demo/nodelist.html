<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>NodeList</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <button>Click me</button>
    <button>Click me</button>
    <button>Click me</button>

    <!-- 2. Include library -->
    <script src="../dist/good-listener.js"></script>

    <!-- 3. Add listener by passing a list of HTML elements -->
    <script>
    var targets = document.querySelectorAll('button');

    listen(targets, 'click', function(e) {
        console.info(e);
    });
    </script>
</body>
</html>
