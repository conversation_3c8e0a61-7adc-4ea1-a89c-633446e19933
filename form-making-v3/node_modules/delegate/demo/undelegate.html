<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Undelegate</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <ul>
        <li><button>Item 1</button></li>
        <li><button>Item 2</button></li>
        <li><button>Item 3</button></li>
        <li><button>Item 4</button></li>
        <li><button>Item 5</button></li>
    </ul>

    <!-- 2. Include library -->
    <script src="../dist/delegate.js"></script>

    <!-- 3. Remove event delegation -->
    <script>
    var ul = document.querySelector('ul');

    var delegation = delegate(ul, 'li button', 'click', function(e) {
        console.log(e.target);
    });

    delegation.destroy();
    </script>
</body>
</html>
