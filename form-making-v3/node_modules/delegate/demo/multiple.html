<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Delegate</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <ul>
        <li><button>Item 1</button></li>
        <li><button>Item 2</button></li>
        <li><button>Item 3</button></li>
        <li><button>Item 4</button></li>
        <li><button>Item 5</button></li>
    </ul>
    <ul>
        <li><span>Item 6</span></li>
        <li><span>Item 7</span></li>
    </ul>

    <!-- 2. Include library -->
    <script src="../dist/delegate.js"></script>

    <!-- 3. Add event delegation -->
    <script>
    var ul = document.querySelector('ul');

    delegate(ul, 'button', 'click', function(e) {
        console.log(e.target);
    });

    delegate(document.body, 'span', 'click', function(e) {
        console.log(e.target);
    });
    </script>
</body>
</html>
