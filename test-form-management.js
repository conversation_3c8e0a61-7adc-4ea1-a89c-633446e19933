#!/usr/bin/env node

// 表单管理功能测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testFormManagement() {
  console.log('🚀 开始测试表单管理功能...\n');

  try {
    // 1. 测试获取表单列表
    console.log('1. 测试获取表单列表...');
    const listResponse = await axios.get(`${BASE_URL}/forms?page=1&limit=10`);
    console.log('✅ 表单列表获取成功');
    console.log(`   - 总数: ${listResponse.data.data.pagination.total}`);
    console.log(`   - 当前页: ${listResponse.data.data.pagination.page}`);
    console.log(`   - 表单数量: ${listResponse.data.data.list.length}\n`);

    if (listResponse.data.data.list.length > 0) {
      const firstForm = listResponse.data.data.list[0];
      
      // 2. 测试获取单个表单详情
      console.log('2. 测试获取表单详情...');
      const detailResponse = await axios.get(`${BASE_URL}/forms/${firstForm.id}`);
      console.log('✅ 表单详情获取成功');
      console.log(`   - 表单ID: ${detailResponse.data.data.id}`);
      console.log(`   - 表单名称: ${detailResponse.data.data.name}`);
      console.log(`   - 表单描述: ${detailResponse.data.data.description}`);
      console.log(`   - 创建者: ${detailResponse.data.data.created_by}\n`);

      // 3. 测试更新表单
      console.log('3. 测试更新表单...');
      const updateData = {
        name: `${firstForm.name} (已更新)`,
        description: `${firstForm.description} - 测试更新`,
        form_config: detailResponse.data.data.form_config,
        created_by: detailResponse.data.data.created_by
      };
      
      const updateResponse = await axios.put(`${BASE_URL}/forms/${firstForm.id}`, updateData);
      console.log('✅ 表单更新成功');
      console.log(`   - 更新后名称: ${updateResponse.data.data.name}\n`);
    }

    // 4. 测试创建新表单
    console.log('4. 测试创建新表单...');
    const newFormData = {
      name: '测试表单 - ' + new Date().toLocaleString(),
      description: '这是一个自动化测试创建的表单',
      form_config: {
        list: [
          {
            type: 'input',
            label: '测试字段',
            model: 'test_field',
            key: 'test_field_001',
            options: {
              defaultValue: '',
              placeholder: '请输入测试内容',
              required: true
            }
          }
        ],
        config: {
          labelWidth: 120,
          labelPosition: 'right',
          size: 'default'
        }
      },
      created_by: '自动化测试'
    };

    const createResponse = await axios.post(`${BASE_URL}/forms`, newFormData);
    console.log('✅ 新表单创建成功');
    console.log(`   - 新表单ID: ${createResponse.data.data.id}`);
    console.log(`   - 新表单名称: ${createResponse.data.data.name}\n`);

    // 5. 测试删除表单
    console.log('5. 测试删除表单...');
    const deleteResponse = await axios.delete(`${BASE_URL}/forms/${createResponse.data.data.id}`);
    console.log('✅ 表单删除成功\n');

    console.log('🎉 所有测试通过！表单管理功能正常工作。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data?.message || error.message);
    console.error('详细错误:', error.response?.data || error.message);
  }
}

// 运行测试
testFormManagement();
