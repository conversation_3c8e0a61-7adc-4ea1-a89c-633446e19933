-- 字典管理系统数据库表结构

-- 字典分类表
CREATE TABLE IF NOT EXISTS dictionary_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类编码',
  description TEXT COMMENT '分类描述',
  parent_id INT DEFAULT NULL COMMENT '父分类ID',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(100) DEFAULT 'system',
  INDEX idx_parent_id (parent_id),
  INDEX idx_code (code),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典分类表';

-- 字典表
CREATE TABLE IF NOT EXISTS dictionaries (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '字典名称',
  code VARCHAR(50) NOT NULL COMMENT '字典编码',
  category_id INT NOT NULL COMMENT '分类ID',
  description TEXT COMMENT '字典描述',
  data_type ENUM('string', 'number', 'boolean', 'object') DEFAULT 'string' COMMENT '数据类型',
  version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
  status ENUM('active', 'inactive', 'draft') DEFAULT 'draft' COMMENT '状态',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统字典',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(100) DEFAULT 'system',
  UNIQUE KEY uk_category_code (category_id, code),
  INDEX idx_category_id (category_id),
  INDEX idx_code (code),
  INDEX idx_status (status),
  FOREIGN KEY (category_id) REFERENCES dictionary_categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典表';

-- 字典项表
CREATE TABLE IF NOT EXISTS dictionary_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  dictionary_id INT NOT NULL COMMENT '字典ID',
  label VARCHAR(200) NOT NULL COMMENT '显示标签',
  value VARCHAR(500) NOT NULL COMMENT '字典值',
  description TEXT COMMENT '描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  parent_id INT DEFAULT NULL COMMENT '父项ID（用于树形结构）',
  extra_data JSON COMMENT '扩展数据',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_dictionary_id (dictionary_id),
  INDEX idx_parent_id (parent_id),
  INDEX idx_value (value),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order),
  FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典项表';

-- 字典版本历史表
CREATE TABLE IF NOT EXISTS dictionary_versions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  dictionary_id INT NOT NULL COMMENT '字典ID',
  version VARCHAR(20) NOT NULL COMMENT '版本号',
  items_data JSON NOT NULL COMMENT '字典项数据快照',
  change_log TEXT COMMENT '变更日志',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100) DEFAULT 'system',
  INDEX idx_dictionary_id (dictionary_id),
  INDEX idx_version (version),
  FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典版本历史表';

-- 字典使用记录表（跟踪哪些表单使用了哪些字典）
CREATE TABLE IF NOT EXISTS dictionary_usage (
  id INT AUTO_INCREMENT PRIMARY KEY,
  dictionary_id INT NOT NULL COMMENT '字典ID',
  form_template_id INT NOT NULL COMMENT '表单模板ID',
  field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
  usage_type ENUM('dropdown', 'radio', 'checkbox', 'autocomplete') NOT NULL COMMENT '使用类型',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_dictionary_id (dictionary_id),
  INDEX idx_form_template_id (form_template_id),
  FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE,
  FOREIGN KEY (form_template_id) REFERENCES form_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典使用记录表';
