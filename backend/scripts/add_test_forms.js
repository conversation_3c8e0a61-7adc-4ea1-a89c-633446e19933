const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'test',
  database: 'form_designer',
  charset: 'utf8mb4'
};

// 测试表单数据
const testForms = [
  {
    name: '患者入院登记表',
    description: '用于患者入院时填写基本信息和病史记录',
    category: 'patient',
    status: 'active',
    created_by: '张医生',
    form_config: JSON.stringify({
      list: [
        {
          type: 'input',
          label: '患者姓名',
          model: 'patientName',
          key: 'patientName',
          required: true
        },
        {
          type: 'input',
          label: '身份证号',
          model: 'idCard',
          key: 'idCard',
          required: true
        },
        {
          type: 'select',
          label: '性别',
          model: 'gender',
          key: 'gender',
          options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' }
          ]
        },
        {
          type: 'date',
          label: '出生日期',
          model: 'birthDate',
          key: 'birthDate'
        },
        {
          type: 'textarea',
          label: '病史描述',
          model: 'medicalHistory',
          key: 'medicalHistory'
        }
      ]
    })
  },
  {
    name: '手术申请单',
    description: '手术前的申请和审批流程表单',
    category: 'medical',
    status: 'active',
    created_by: '李主任',
    form_config: JSON.stringify({
      list: [
        {
          type: 'input',
          label: '患者姓名',
          model: 'patientName',
          key: 'patientName',
          required: true
        },
        {
          type: 'input',
          label: '病历号',
          model: 'medicalRecordNo',
          key: 'medicalRecordNo',
          required: true
        },
        {
          type: 'select',
          label: '手术类型',
          model: 'surgeryType',
          key: 'surgeryType',
          options: [
            { label: '一级手术', value: 'level1' },
            { label: '二级手术', value: 'level2' },
            { label: '三级手术', value: 'level3' },
            { label: '四级手术', value: 'level4' }
          ]
        },
        {
          type: 'datetime',
          label: '预计手术时间',
          model: 'surgeryTime',
          key: 'surgeryTime'
        },
        {
          type: 'textarea',
          label: '手术适应症',
          model: 'indication',
          key: 'indication'
        }
      ]
    })
  },
  {
    name: '药品入库登记',
    description: '药品采购入库时的详细登记表单',
    category: 'medicine',
    status: 'active',
    created_by: '王药师',
    form_config: JSON.stringify({
      list: [
        {
          type: 'input',
          label: '药品名称',
          model: 'medicineName',
          key: 'medicineName',
          required: true
        },
        {
          type: 'input',
          label: '批准文号',
          model: 'approvalNumber',
          key: 'approvalNumber'
        },
        {
          type: 'number',
          label: '入库数量',
          model: 'quantity',
          key: 'quantity',
          required: true
        },
        {
          type: 'date',
          label: '生产日期',
          model: 'productionDate',
          key: 'productionDate'
        },
        {
          type: 'date',
          label: '有效期至',
          model: 'expiryDate',
          key: 'expiryDate'
        }
      ]
    })
  },
  {
    name: '设备维护记录',
    description: '医疗设备定期维护和检修记录表单',
    category: 'equipment',
    status: 'active',
    created_by: '陈工程师',
    form_config: JSON.stringify({
      list: [
        {
          type: 'input',
          label: '设备名称',
          model: 'equipmentName',
          key: 'equipmentName',
          required: true
        },
        {
          type: 'input',
          label: '设备编号',
          model: 'equipmentNo',
          key: 'equipmentNo',
          required: true
        },
        {
          type: 'select',
          label: '维护类型',
          model: 'maintenanceType',
          key: 'maintenanceType',
          options: [
            { label: '日常保养', value: 'daily' },
            { label: '定期维护', value: 'regular' },
            { label: '故障维修', value: 'repair' },
            { label: '年度检查', value: 'annual' }
          ]
        },
        {
          type: 'datetime',
          label: '维护时间',
          model: 'maintenanceTime',
          key: 'maintenanceTime'
        },
        {
          type: 'textarea',
          label: '维护内容',
          model: 'maintenanceContent',
          key: 'maintenanceContent'
        }
      ]
    })
  }
];

async function addTestForms() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    for (const form of testForms) {
      try {
        const [result] = await connection.execute(
          `INSERT INTO form_templates (name, description, form_config, created_by, status)
           VALUES (?, ?, ?, ?, ?)`,
          [form.name, form.description, form.form_config, form.created_by, form.status]
        );
        console.log(`✅ 添加表单: ${form.name} (ID: ${result.insertId})`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️  表单已存在: ${form.name}`);
        } else {
          console.error(`❌ 添加表单失败: ${form.name}`, error.message);
        }
      }
    }
    
    console.log('🎉 测试数据添加完成');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addTestForms();
