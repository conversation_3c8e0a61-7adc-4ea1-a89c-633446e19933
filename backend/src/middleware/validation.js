const Joi = require('joi');

// 表单模板验证规则
const formTemplateSchema = Joi.object({
  name: Joi.string().min(1).max(255).required().messages({
    'string.empty': '表单名称不能为空',
    'string.max': '表单名称不能超过255个字符',
    'any.required': '表单名称是必填项'
  }),
  description: Joi.string().allow('').max(1000).messages({
    'string.max': '表单描述不能超过1000个字符'
  }),
  form_config: Joi.object().required().messages({
    'any.required': '表单配置是必填项',
    'object.base': '表单配置必须是有效的JSON对象'
  }),
  created_by: Joi.string().max(100).allow('').messages({
    'string.max': '创建者名称不能超过100个字符'
  }),
  status: Joi.string().valid('active', 'inactive').default('active')
});

// 表单提交数据验证规则
const formSubmissionSchema = Joi.object({
  form_data: Joi.object().required().messages({
    'any.required': '表单数据是必填项',
    'object.base': '表单数据必须是有效的JSON对象'
  }),
  submitted_by: Joi.string().max(100).allow('').messages({
    'string.max': '提交者名称不能超过100个字符'
  })
});

// 验证中间件
const validateFormTemplate = (req, res, next) => {
  const { error, value } = formTemplateSchema.validate(req.body, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors
    });
  }
  
  req.validatedData = value;
  next();
};

const validateFormSubmission = (req, res, next) => {
  const { error, value } = formSubmissionSchema.validate(req.body, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors
    });
  }
  
  req.validatedData = value;
  next();
};

// 分页参数验证
const validatePagination = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const search = req.query.search || '';
  
  if (page < 1) {
    return res.status(400).json({
      success: false,
      message: '页码必须大于0'
    });
  }
  
  if (limit < 1 || limit > 100) {
    return res.status(400).json({
      success: false,
      message: '每页数量必须在1-100之间'
    });
  }
  
  req.pagination = {
    page,
    limit,
    offset: (page - 1) * limit,
    search: search.trim()
  };
  
  next();
};

module.exports = {
  validateFormTemplate,
  validateFormSubmission,
  validatePagination
};
