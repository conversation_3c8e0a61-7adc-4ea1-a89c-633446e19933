const { pool } = require('../config/database');

class FormController {
  // 获取表单模板列表
  async getFormTemplates(req, res, next) {
    try {
      const { page, limit, offset, search } = req.pagination;

      // 使用字符串拼接来避免MySQL 8.0的参数绑定问题
      let whereClause = 'WHERE status = ?';
      let queryParams = ['active'];

      if (search) {
        whereClause += ' AND (name LIKE ? OR description LIKE ?)';
        queryParams.push(`%${search}%`, `%${search}%`);
      }

      // 获取总数
      const [countResult] = await pool.execute(
        `SELECT COUNT(*) as total FROM form_templates ${whereClause}`,
        queryParams
      );
      const total = countResult[0].total;

      // 获取分页数据 - 使用字符串拼接而不是参数绑定
      const [rows] = await pool.execute(
        `SELECT id, name, description, created_at, updated_at, created_by, status
         FROM form_templates ${whereClause}
         ORDER BY updated_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        queryParams
      );

      res.json({
        success: true,
        data: {
          list: rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取表单列表错误:', error);
      next(error);
    }
  }

  // 获取单个表单模板
  async getFormTemplate(req, res, next) {
    try {
      const { id } = req.params;
      
      const [rows] = await pool.execute(
        'SELECT * FROM form_templates WHERE id = ? AND status = "active"',
        [id]
      );
      
      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '表单模板不存在'
        });
      }
      
      res.json({
        success: true,
        data: rows[0]
      });
    } catch (error) {
      next(error);
    }
  }

  // 创建表单模板
  async createFormTemplate(req, res, next) {
    try {
      const { name, description, form_config, created_by, status } = req.validatedData;
      
      const [result] = await pool.execute(
        'INSERT INTO form_templates (name, description, form_config, created_by, status) VALUES (?, ?, ?, ?, ?)',
        [name, description || '', JSON.stringify(form_config), created_by || '', status || 'active']
      );
      
      res.status(201).json({
        success: true,
        message: '表单模板创建成功',
        data: {
          id: result.insertId,
          name,
          description,
          created_by,
          status
        }
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新表单模板
  async updateFormTemplate(req, res, next) {
    try {
      const { id } = req.params;
      const { name, description, form_config, status } = req.validatedData;
      
      // 检查表单是否存在
      const [existingRows] = await pool.execute(
        'SELECT id FROM form_templates WHERE id = ? AND status = "active"',
        [id]
      );
      
      if (existingRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '表单模板不存在'
        });
      }
      
      await pool.execute(
        'UPDATE form_templates SET name = ?, description = ?, form_config = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [name, description || '', JSON.stringify(form_config), status || 'active', id]
      );
      
      res.json({
        success: true,
        message: '表单模板更新成功'
      });
    } catch (error) {
      next(error);
    }
  }

  // 删除表单模板（软删除）
  async deleteFormTemplate(req, res, next) {
    try {
      const { id } = req.params;
      
      // 检查表单是否存在
      const [existingRows] = await pool.execute(
        'SELECT id FROM form_templates WHERE id = ? AND status = "active"',
        [id]
      );
      
      if (existingRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '表单模板不存在'
        });
      }
      
      await pool.execute(
        'UPDATE form_templates SET status = "inactive", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );
      
      res.json({
        success: true,
        message: '表单模板删除成功'
      });
    } catch (error) {
      next(error);
    }
  }

  // 提交表单数据
  async submitForm(req, res, next) {
    try {
      const { id } = req.params;
      const { form_data, submitted_by } = req.validatedData;
      
      // 检查表单模板是否存在
      const [templateRows] = await pool.execute(
        'SELECT id FROM form_templates WHERE id = ? AND status = "active"',
        [id]
      );
      
      if (templateRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '表单模板不存在'
        });
      }
      
      const [result] = await pool.execute(
        'INSERT INTO form_submissions (template_id, form_data, submitted_by) VALUES (?, ?, ?)',
        [id, JSON.stringify(form_data), submitted_by || '']
      );
      
      res.status(201).json({
        success: true,
        message: '表单提交成功',
        data: {
          id: result.insertId,
          template_id: id,
          submitted_by
        }
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取表单提交数据
  async getFormSubmissions(req, res, next) {
    try {
      const { id } = req.params;
      const { page, limit, offset } = req.pagination;
      
      // 检查表单模板是否存在
      const [templateRows] = await pool.execute(
        'SELECT id FROM form_templates WHERE id = ? AND status = "active"',
        [id]
      );
      
      if (templateRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '表单模板不存在'
        });
      }
      
      // 获取总数
      const [countResult] = await pool.execute(
        'SELECT COUNT(*) as total FROM form_submissions WHERE template_id = ?',
        [id]
      );
      const total = countResult[0].total;
      
      // 获取分页数据
      const [rows] = await pool.execute(
        'SELECT * FROM form_submissions WHERE template_id = ? ORDER BY submitted_at DESC LIMIT ? OFFSET ?',
        [id, limit, offset]
      );
      
      res.json({
        success: true,
        data: {
          list: rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new FormController();
