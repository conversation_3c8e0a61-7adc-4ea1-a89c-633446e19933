const { pool } = require('../config/database');

class DictionaryController {
  // 获取字典分类列表
  async getCategories(req, res, next) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          id, name, code, description, parent_id, sort_order, status,
          created_at, updated_at, created_by
        FROM dictionary_categories 
        WHERE status = 'active'
        ORDER BY sort_order ASC, created_at DESC
      `);

      res.json({
        success: true,
        data: rows
      });
    } catch (error) {
      console.error('获取字典分类错误:', error);
      next(error);
    }
  }

  // 创建字典分类
  async createCategory(req, res, next) {
    try {
      const { name, code, description, parent_id, sort_order } = req.body;

      const [result] = await pool.execute(`
        INSERT INTO dictionary_categories (name, code, description, parent_id, sort_order, created_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [name, code, description, parent_id, sort_order || 0, req.user?.username || 'system']);

      res.json({
        success: true,
        data: { id: result.insertId },
        message: '字典分类创建成功'
      });
    } catch (error) {
      console.error('创建字典分类错误:', error);
      if (error.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
          success: false,
          message: '分类编码已存在'
        });
      }
      next(error);
    }
  }

  // 更新字典分类
  async updateCategory(req, res, next) {
    try {
      const { id } = req.params;
      const { name, description, parent_id, sort_order } = req.body;

      const [result] = await pool.execute(`
        UPDATE dictionary_categories
        SET name = ?, description = ?, parent_id = ?, sort_order = ?, updated_at = NOW()
        WHERE id = ?
      `, [name, description, parent_id, sort_order || 0, id]);

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: '分类不存在'
        });
      }

      res.json({
        success: true,
        message: '字典分类更新成功'
      });
    } catch (error) {
      console.error('更新字典分类错误:', error);
      next(error);
    }
  }

  // 删除字典分类
  async deleteCategory(req, res, next) {
    try {
      const { id } = req.params;

      // 检查是否有子分类
      const [children] = await pool.execute(`
        SELECT COUNT(*) as count FROM dictionary_categories WHERE parent_id = ?
      `, [id]);

      if (children[0].count > 0) {
        return res.status(400).json({
          success: false,
          message: '该分类下还有子分类，无法删除'
        });
      }

      // 检查是否有字典使用该分类
      const [dictionaries] = await pool.execute(`
        SELECT COUNT(*) as count FROM dictionaries WHERE category_id = ?
      `, [id]);

      if (dictionaries[0].count > 0) {
        return res.status(400).json({
          success: false,
          message: '该分类下还有字典，无法删除'
        });
      }

      const [result] = await pool.execute(`
        DELETE FROM dictionary_categories WHERE id = ?
      `, [id]);

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: '分类不存在'
        });
      }

      res.json({
        success: true,
        message: '字典分类删除成功'
      });
    } catch (error) {
      console.error('删除字典分类错误:', error);
      next(error);
    }
  }

  // 获取字典列表
  async getDictionaries(req, res, next) {
    try {
      const { page, limit, offset } = req.pagination;
      const { category_id, search, status } = req.query;

      let whereClause = 'WHERE 1=1';
      let queryParams = [];

      if (category_id) {
        whereClause += ' AND d.category_id = ?';
        queryParams.push(category_id);
      }

      if (search) {
        whereClause += ' AND (d.name LIKE ? OR d.code LIKE ? OR d.description LIKE ?)';
        queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      if (status) {
        whereClause += ' AND d.status = ?';
        queryParams.push(status);
      }

      // 获取总数
      const [countResult] = await pool.execute(`
        SELECT COUNT(*) as total 
        FROM dictionaries d
        LEFT JOIN dictionary_categories dc ON d.category_id = dc.id
        ${whereClause}
      `, queryParams);
      const total = countResult[0].total;

      // 获取分页数据
      const [rows] = await pool.execute(`
        SELECT 
          d.id, d.name, d.code, d.category_id, d.description, d.data_type,
          d.version, d.status, d.is_system, d.created_at, d.updated_at, d.created_by,
          dc.name as category_name,
          (SELECT COUNT(*) FROM dictionary_items WHERE dictionary_id = d.id AND status = 'active') as item_count
        FROM dictionaries d
        LEFT JOIN dictionary_categories dc ON d.category_id = dc.id
        ${whereClause}
        ORDER BY d.updated_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `, queryParams);

      res.json({
        success: true,
        data: {
          list: rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取字典列表错误:', error);
      next(error);
    }
  }

  // 获取单个字典详情（包含字典项）
  async getDictionary(req, res, next) {
    try {
      const { id } = req.params;

      // 获取字典基本信息
      const [dictRows] = await pool.execute(`
        SELECT 
          d.id, d.name, d.code, d.category_id, d.description, d.data_type,
          d.version, d.status, d.is_system, d.created_at, d.updated_at, d.created_by,
          dc.name as category_name
        FROM dictionaries d
        LEFT JOIN dictionary_categories dc ON d.category_id = dc.id
        WHERE d.id = ?
      `, [id]);

      if (dictRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '字典不存在'
        });
      }

      // 获取字典项
      const [itemRows] = await pool.execute(`
        SELECT 
          id, label, value, description, sort_order, parent_id, extra_data, status
        FROM dictionary_items
        WHERE dictionary_id = ?
        ORDER BY sort_order ASC, id ASC
      `, [id]);

      const dictionary = dictRows[0];
      dictionary.items = itemRows;

      res.json({
        success: true,
        data: dictionary
      });
    } catch (error) {
      console.error('获取字典详情错误:', error);
      next(error);
    }
  }

  // 创建字典
  async createDictionary(req, res, next) {
    try {
      const { name, code, category_id, description, data_type, items } = req.body;

      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // 创建字典
        const [dictResult] = await connection.execute(`
          INSERT INTO dictionaries (name, code, category_id, description, data_type, created_by)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [name, code, category_id, description, data_type || 'string', req.user?.username || 'system']);

        const dictionaryId = dictResult.insertId;

        // 创建字典项
        if (items && items.length > 0) {
          for (const item of items) {
            await connection.execute(`
              INSERT INTO dictionary_items (dictionary_id, label, value, description, sort_order, parent_id, extra_data)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              dictionaryId,
              item.label,
              item.value,
              item.description || null,
              item.sort_order || 0,
              item.parent_id || null,
              item.extra_data ? JSON.stringify(item.extra_data) : null
            ]);
          }
        }

        await connection.commit();

        res.json({
          success: true,
          data: { id: dictionaryId },
          message: '字典创建成功'
        });
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('创建字典错误:', error);
      if (error.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
          success: false,
          message: '字典编码在该分类下已存在'
        });
      }
      next(error);
    }
  }

  // 更新字典
  async updateDictionary(req, res, next) {
    try {
      const { id } = req.params;
      const { name, description, data_type, status, items } = req.body;

      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // 更新字典基本信息
        await connection.execute(`
          UPDATE dictionaries 
          SET name = ?, description = ?, data_type = ?, status = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [name, description, data_type, status, id]);

        // 如果提供了字典项，则更新字典项
        if (items) {
          // 删除现有字典项
          await connection.execute('DELETE FROM dictionary_items WHERE dictionary_id = ?', [id]);

          // 插入新的字典项
          for (const item of items) {
            await connection.execute(`
              INSERT INTO dictionary_items (dictionary_id, label, value, description, sort_order, parent_id, extra_data, status)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              id,
              item.label,
              item.value,
              item.description || null,
              item.sort_order || 0,
              item.parent_id || null,
              item.extra_data ? JSON.stringify(item.extra_data) : null,
              item.status || 'active'
            ]);
          }

          // 创建版本快照
          const [itemsSnapshot] = await connection.execute(`
            SELECT label, value, description, sort_order, parent_id, extra_data, status
            FROM dictionary_items WHERE dictionary_id = ?
            ORDER BY sort_order ASC, id ASC
          `, [id]);

          await connection.execute(`
            INSERT INTO dictionary_versions (dictionary_id, version, items_data, change_log, created_by)
            VALUES (?, ?, ?, ?, ?)
          `, [
            id,
            new Date().toISOString().slice(0, 19).replace('T', ' '),
            JSON.stringify(itemsSnapshot),
            '字典项更新',
            req.user?.username || 'system'
          ]);
        }

        await connection.commit();

        res.json({
          success: true,
          message: '字典更新成功'
        });
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('更新字典错误:', error);
      next(error);
    }
  }

  // 删除字典
  async deleteDictionary(req, res, next) {
    try {
      const { id } = req.params;

      // 检查是否有表单在使用该字典
      const [usageRows] = await pool.execute(`
        SELECT COUNT(*) as count FROM dictionary_usage WHERE dictionary_id = ?
      `, [id]);

      if (usageRows[0].count > 0) {
        return res.status(400).json({
          success: false,
          message: '该字典正在被表单使用，无法删除'
        });
      }

      await pool.execute('DELETE FROM dictionaries WHERE id = ?', [id]);

      res.json({
        success: true,
        message: '字典删除成功'
      });
    } catch (error) {
      console.error('删除字典错误:', error);
      next(error);
    }
  }

  // 获取字典的使用情况
  async getDictionaryUsage(req, res, next) {
    try {
      const { id } = req.params;

      const [rows] = await pool.execute(`
        SELECT
          du.id, du.field_name, du.usage_type, du.created_at,
          ft.name as form_name, ft.id as form_id
        FROM dictionary_usage du
        LEFT JOIN form_templates ft ON du.form_template_id = ft.id
        WHERE du.dictionary_id = ?
        ORDER BY du.created_at DESC
      `, [id]);

      res.json({
        success: true,
        data: rows
      });
    } catch (error) {
      console.error('获取字典使用情况错误:', error);
      next(error);
    }
  }

  // 获取字典的版本历史
  async getDictionaryVersions(req, res, next) {
    try {
      const { id } = req.params;

      const [rows] = await pool.execute(`
        SELECT
          id, version, change_log, created_at, created_by
        FROM dictionary_versions
        WHERE dictionary_id = ?
        ORDER BY created_at DESC
      `, [id]);

      res.json({
        success: true,
        data: rows
      });
    } catch (error) {
      console.error('获取字典版本历史错误:', error);
      next(error);
    }
  }

  // 根据编码获取字典项（供表单使用）
  async getDictionaryByCode(req, res, next) {
    try {
      const { code } = req.params;
      const { category_code } = req.query;

      let whereClause = 'WHERE d.code = ? AND d.status = "active"';
      let queryParams = [code];

      if (category_code) {
        whereClause += ' AND dc.code = ?';
        queryParams.push(category_code);
      }

      const [dictRows] = await pool.execute(`
        SELECT d.id, d.name, d.code, d.data_type
        FROM dictionaries d
        LEFT JOIN dictionary_categories dc ON d.category_id = dc.id
        ${whereClause}
        LIMIT 1
      `, queryParams);

      if (dictRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: '字典不存在'
        });
      }

      const dictionary = dictRows[0];

      // 获取字典项
      const [itemRows] = await pool.execute(`
        SELECT label, value, description, sort_order, parent_id, extra_data
        FROM dictionary_items
        WHERE dictionary_id = ? AND status = 'active'
        ORDER BY sort_order ASC, id ASC
      `, [dictionary.id]);

      res.json({
        success: true,
        data: {
          ...dictionary,
          items: itemRows
        }
      });
    } catch (error) {
      console.error('根据编码获取字典错误:', error);
      next(error);
    }
  }
}

module.exports = new DictionaryController();
