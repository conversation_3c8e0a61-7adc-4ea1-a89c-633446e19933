const mysql = require('mysql2/promise');
require('dotenv').config();

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'form_designer',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
});

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
};

// 初始化数据库表
const initDatabase = async () => {
  try {
    // 创建表单模板表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS form_templates (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL COMMENT '表单名称',
        description TEXT COMMENT '表单描述',
        form_config JSON NOT NULL COMMENT '表单配置JSON',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) COMMENT '创建者',
        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
        INDEX idx_name (name),
        INDEX idx_created_at (created_at),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建表单提交数据表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS form_submissions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        template_id INT NOT NULL,
        form_data JSON NOT NULL COMMENT '表单填写数据',
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        submitted_by VARCHAR(100) COMMENT '提交者',
        INDEX idx_template_id (template_id),
        INDEX idx_submitted_at (submitted_at),
        FOREIGN KEY (template_id) REFERENCES form_templates(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ 数据库表初始化成功');
  } catch (error) {
    console.error('❌ 数据库表初始化失败:', error.message);
    throw error;
  }
};

module.exports = {
  pool,
  testConnection,
  initDatabase
};
