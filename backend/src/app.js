const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
require('dotenv').config();

const { testConnection, initDatabase, initAuthTables, initAuthData } = require('./config/database');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const formsRouter = require('./routes/forms');
const authRouter = require('./routes/auth');
const dictionariesRouter = require('./routes/dictionaries');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());

// 压缩中间件
app.use(compression());

// CORS配置
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    process.env.CORS_ORIGIN
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 解析JSON请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API路由
app.use('/api/auth', authRouter);
app.use('/api/forms', formsRouter);
app.use('/api/dictionaries', dictionariesRouter);

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    console.log('🔄 正在启动医疗低代码平台...');

    // 测试数据库连接
    await testConnection();

    // 初始化表单相关表
    await initDatabase();

    // 初始化认证系统表
    await initAuthTables();

    // 初始化认证系统基础数据
    await initAuthData();

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 医疗低代码平台启动成功！`);
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔐 认证API: http://localhost:${PORT}/api/auth`);
      console.log(`📋 表单API: http://localhost:${PORT}/api/forms`);
      console.log(`📚 字典API: http://localhost:${PORT}/api/dictionaries`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`👤 默认管理员: admin / admin123`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动应用
startServer();

module.exports = app;
