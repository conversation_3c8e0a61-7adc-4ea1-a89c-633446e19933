{"name": "form-designer-backend", "version": "1.0.0", "description": "Backend API for form designer", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "joi": "^17.9.2", "helmet": "^7.0.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2"}, "keywords": ["form", "designer", "api"], "author": "Your Name", "license": "MIT"}