{"name": "untyped", "version": "2.0.0", "description": "", "repository": "unjs/untyped", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./babel-plugin": {"types": "./dist/loader/babel.d.mts", "default": "./dist/loader/babel.mjs"}, "./loader": {"types": "./dist/loader/loader.d.mts", "default": "./dist/loader/loader.mjs"}}, "types": "./dist/index.d.mts", "bin": {"untyped": "./dist/cli.mjs"}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test web", "lint:fix": "automd && eslint --fix . && prettier -w src test web", "prepack": "pnpm build", "release": "pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage", "untyped": "jiti ./src/cli.ts", "web": "nuxi dev web", "web:build": "nuxi generate web"}, "dependencies": {"citty": "^0.1.6", "defu": "^6.1.4", "jiti": "^2.4.2", "knitwork": "^1.2.0", "scule": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/standalone": "^7.26.9", "@babel/template": "^7.26.9", "@babel/types": "^7.26.9", "@types/babel__standalone": "^7.1.9", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.13.5", "@vitest/coverage-v8": "^3.0.7", "@vue/compiler-sfc": "^3.5.13", "automd": "^0.3.12", "changelogen": "^0.6.0", "esbuild": "^0.25.0", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "hljs": "^6.2.3", "json-schema": "^0.4.0", "marked": "^15.0.7", "monaco-editor": "^0.52.2", "nuxt": "^3.15.4", "nuxt-windicss": "^3.0.1", "prettier": "^3.5.2", "prismjs": "^1.29.0", "typescript": "^5.7.3", "unbuild": "^3.4.1", "vitest": "^3.0.7"}, "packageManager": "pnpm@10.5.1"}