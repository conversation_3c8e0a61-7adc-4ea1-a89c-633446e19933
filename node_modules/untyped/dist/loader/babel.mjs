import { m as mergedTypes, c as getTypeDescriptor, n as normalizeTypes, e as cachedFn } from '../shared/untyped.Br_uXjZG.mjs';
import 'scule';

function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

var r$w={};

var e$R={};

var e$Q={};

var t$b={};

var e$P={};

var a$i={};

var u$c;function a$h(){if(u$c)return a$i;u$c=1,Object.defineProperty(a$i,"__esModule",{value:true}),a$i.default=t;function t(l,r){const s=Object.keys(r);for(const o of s)if(l[o]!==r[o])return  false;return  true}return a$i}

var r$v={};

var u$b;function m$4(){if(u$b)return r$v;u$b=1,Object.defineProperty(r$v,"__esModule",{value:true}),r$v.default=p;const i=new Set;function p(r,t,n=""){if(i.has(r))return;i.add(r);const{internal:c,trace:e}=f(1,2);c||console.warn(`${n}\`${r}\` has been deprecated, please migrate to \`${t}\`
${e}`);}function f(r,t){const{stackTraceLimit:n,prepareStackTrace:c}=Error;let e;if(Error.stackTraceLimit=1+r+t,Error.prepareStackTrace=function(s,l){e=l;},new Error().stack,Error.stackTraceLimit=n,Error.prepareStackTrace=c,!e)return {internal:false,trace:""};const o=e.slice(1+r,1+r+t);return {internal:/[\\/]@babel[\\/]/.test(o[1].getFileName()),trace:o.map(s=>`    at ${s}`).join(`
`)}}return r$v}

var l$6;function qn(){if(l$6)return e$P;l$6=1,Object.defineProperty(e$P,"__esModule",{value:true}),e$P.isAccessor=Pn,e$P.isAnyTypeAnnotation=ve,e$P.isArgumentPlaceholder=mr,e$P.isArrayExpression=s,e$P.isArrayPattern=ie,e$P.isArrayTypeAnnotation=Ve,e$P.isArrowFunctionExpression=le,e$P.isAssignmentExpression=u,e$P.isAssignmentPattern=ne,e$P.isAwaitExpression=Ne,e$P.isBigIntLiteral=Be,e$P.isBinary=qa,e$P.isBinaryExpression=f,e$P.isBindExpression=Er,e$P.isBlock=_a,e$P.isBlockParent=za,e$P.isBlockStatement=o,e$P.isBooleanLiteral=F,e$P.isBooleanLiteralTypeAnnotation=We,e$P.isBooleanTypeAnnotation=Ue,e$P.isBreakStatement=T,e$P.isCallExpression=S,e$P.isCatchClause=m,e$P.isClass=An,e$P.isClassAccessorProperty=Ke,e$P.isClassBody=se,e$P.isClassDeclaration=fe,e$P.isClassExpression=ue,e$P.isClassImplements=Qe,e$P.isClassMethod=be,e$P.isClassPrivateMethod=Je,e$P.isClassPrivateProperty=Xe,e$P.isClassProperty=ke,e$P.isCompletionStatement=$a,e$P.isConditional=Ha,e$P.isConditionalExpression=E,e$P.isContinueStatement=x,e$P.isDebuggerStatement=D,e$P.isDecimalLiteral=Ir,e$P.isDeclaration=un,e$P.isDeclareClass=ze,e$P.isDeclareExportAllDeclaration=rt,e$P.isDeclareExportDeclaration=tt,e$P.isDeclareFunction=_e,e$P.isDeclareInterface=Ge,e$P.isDeclareModule=Ye,e$P.isDeclareModuleExports=$e,e$P.isDeclareOpaqueType=Ze,e$P.isDeclareTypeAlias=He,e$P.isDeclareVariable=et,e$P.isDeclaredPredicate=at,e$P.isDecorator=Dr,e$P.isDirective=p,e$P.isDirectiveLiteral=y,e$P.isDoExpression=Ar,e$P.isDoWhileStatement=A,e$P.isEmptyStatement=b,e$P.isEmptyTypeAnnotation=Tt,e$P.isEnumBody=Mn,e$P.isEnumBooleanBody=Vt,e$P.isEnumBooleanMember=Qt,e$P.isEnumDeclaration=vt,e$P.isEnumDefaultedMember=Gt,e$P.isEnumMember=Bn,e$P.isEnumNumberBody=Ut,e$P.isEnumNumberMember=zt,e$P.isEnumStringBody=Wt,e$P.isEnumStringMember=_t,e$P.isEnumSymbolBody=qt,e$P.isExistsTypeAnnotation=nt,e$P.isExportAllDeclaration=ce,e$P.isExportDeclaration=bn,e$P.isExportDefaultDeclaration=pe,e$P.isExportDefaultSpecifier=br,e$P.isExportNamedDeclaration=ye,e$P.isExportNamespaceSpecifier=Le,e$P.isExportSpecifier=oe,e$P.isExpression=Wa,e$P.isExpressionStatement=d,e$P.isExpressionWrapper=tn,e$P.isFile=P,e$P.isFlow=hn,e$P.isFlowBaseAnnotation=gn,e$P.isFlowDeclaration=Cn,e$P.isFlowPredicate=Nn,e$P.isFlowType=wn,e$P.isFor=rn,e$P.isForInStatement=I,e$P.isForOfStatement=Te,e$P.isForStatement=h,e$P.isForXStatement=an,e$P.isFunction=nn,e$P.isFunctionDeclaration=w,e$P.isFunctionExpression=g,e$P.isFunctionParent=ln,e$P.isFunctionTypeAnnotation=it,e$P.isFunctionTypeParam=lt,e$P.isGenericTypeAnnotation=st,e$P.isIdentifier=C,e$P.isIfStatement=N,e$P.isImmutable=on,e$P.isImport=Me,e$P.isImportAttribute=xr,e$P.isImportDeclaration=Se,e$P.isImportDefaultSpecifier=me,e$P.isImportExpression=De,e$P.isImportNamespaceSpecifier=Ee,e$P.isImportOrExportDeclaration=i,e$P.isImportSpecifier=xe,e$P.isIndexedAccessType=Yt,e$P.isInferredPredicate=ut,e$P.isInterfaceDeclaration=ct,e$P.isInterfaceExtends=ft,e$P.isInterfaceTypeAnnotation=pt,e$P.isInterpreterDirective=c,e$P.isIntersectionTypeAnnotation=yt,e$P.isJSX=Ln,e$P.isJSXAttribute=Ht,e$P.isJSXClosingElement=Zt,e$P.isJSXClosingFragment=yr,e$P.isJSXElement=er,e$P.isJSXEmptyExpression=tr,e$P.isJSXExpressionContainer=rr,e$P.isJSXFragment=cr,e$P.isJSXIdentifier=nr,e$P.isJSXMemberExpression=ir,e$P.isJSXNamespacedName=lr,e$P.isJSXOpeningElement=sr,e$P.isJSXOpeningFragment=pr,e$P.isJSXSpreadAttribute=ur,e$P.isJSXSpreadChild=ar,e$P.isJSXText=fr,e$P.isLVal=cn,e$P.isLabeledStatement=M,e$P.isLiteral=yn,e$P.isLogicalExpression=K,e$P.isLoop=Za,e$P.isMemberExpression=X,e$P.isMetaProperty=Ae,e$P.isMethod=Sn,e$P.isMiscellaneous=On,e$P.isMixedTypeAnnotation=ot,e$P.isModuleDeclaration=Vn,e$P.isModuleExpression=hr,e$P.isModuleSpecifier=dn,e$P.isNewExpression=J,e$P.isNoop=or,e$P.isNullLiteral=O,e$P.isNullLiteralTypeAnnotation=qe,e$P.isNullableTypeAnnotation=St,e$P.isNumberLiteral=Jn,e$P.isNumberLiteralTypeAnnotation=mt,e$P.isNumberTypeAnnotation=Et,e$P.isNumericLiteral=L,e$P.isObjectExpression=R,e$P.isObjectMember=mn,e$P.isObjectMethod=v,e$P.isObjectPattern=de,e$P.isObjectProperty=V,e$P.isObjectTypeAnnotation=xt,e$P.isObjectTypeCallProperty=At,e$P.isObjectTypeIndexer=bt,e$P.isObjectTypeInternalSlot=Dt,e$P.isObjectTypeProperty=dt,e$P.isObjectTypeSpreadProperty=Pt,e$P.isOpaqueType=It,e$P.isOptionalCallExpression=Fe,e$P.isOptionalIndexedAccessType=$t,e$P.isOptionalMemberExpression=Oe,e$P.isParenthesizedExpression=Q,e$P.isPattern=Dn,e$P.isPatternLike=fn,e$P.isPipelineBareFunction=Cr,e$P.isPipelinePrimaryTopicReference=Nr,e$P.isPipelineTopicExpression=gr,e$P.isPlaceholder=Tr,e$P.isPrivate=In,e$P.isPrivateName=je,e$P.isProgram=j,e$P.isProperty=En,e$P.isPureish=sn,e$P.isQualifiedTypeIdentifier=ht,e$P.isRecordExpression=dr,e$P.isRegExpLiteral=k,e$P.isRegexLiteral=jn,e$P.isRestElement=U,e$P.isRestProperty=Rn,e$P.isReturnStatement=W,e$P.isScopable=Qa,e$P.isSequenceExpression=q,e$P.isSpreadElement=Pe,e$P.isSpreadProperty=vn,e$P.isStandardized=Ua,e$P.isStatement=Ga,e$P.isStaticBlock=Re,e$P.isStringLiteral=B,e$P.isStringLiteralTypeAnnotation=wt,e$P.isStringTypeAnnotation=gt,e$P.isSuper=Ie,e$P.isSwitchCase=z,e$P.isSwitchStatement=_,e$P.isSymbolTypeAnnotation=Ct,e$P.isTSAnyKeyword=jr,e$P.isTSArrayType=ia,e$P.isTSAsExpression=ha,e$P.isTSBaseType=Xn,e$P.isTSBigIntKeyword=vr,e$P.isTSBooleanKeyword=Rr,e$P.isTSCallSignatureDeclaration=Fr,e$P.isTSConditionalType=ya,e$P.isTSConstructSignatureDeclaration=kr,e$P.isTSConstructorType=ea,e$P.isTSDeclareFunction=Br,e$P.isTSDeclareMethod=Lr,e$P.isTSEntityName=pn,e$P.isTSEnumBody=Ca,e$P.isTSEnumDeclaration=Na,e$P.isTSEnumMember=Ma,e$P.isTSExportAssignment=Xa,e$P.isTSExpressionWithTypeArguments=Aa,e$P.isTSExternalModuleReference=ka,e$P.isTSFunctionType=Zr,e$P.isTSImportEqualsDeclaration=Fa,e$P.isTSImportType=Oa,e$P.isTSIndexSignature=Jr,e$P.isTSIndexedAccessType=ma,e$P.isTSInferType=oa,e$P.isTSInstantiationExpression=Ia,e$P.isTSInterfaceBody=da,e$P.isTSInterfaceDeclaration=ba,e$P.isTSIntersectionType=pa,e$P.isTSIntrinsicKeyword=Vr,e$P.isTSLiteralType=Da,e$P.isTSMappedType=Ea,e$P.isTSMethodSignature=Xr,e$P.isTSModuleBlock=La,e$P.isTSModuleDeclaration=Ba,e$P.isTSNamedTupleMember=fa,e$P.isTSNamespaceExportDeclaration=Ja,e$P.isTSNeverKeyword=Ur,e$P.isTSNonNullExpression=Ka,e$P.isTSNullKeyword=Wr,e$P.isTSNumberKeyword=qr,e$P.isTSObjectKeyword=Qr,e$P.isTSOptionalType=sa,e$P.isTSParameterProperty=Mr,e$P.isTSParenthesizedType=Ta,e$P.isTSPropertySignature=Kr,e$P.isTSQualifiedName=Or,e$P.isTSRestType=ua,e$P.isTSSatisfiesExpression=wa,e$P.isTSStringKeyword=zr,e$P.isTSSymbolKeyword=_r,e$P.isTSTemplateLiteralType=xa,e$P.isTSThisType=Hr,e$P.isTSTupleType=la,e$P.isTSType=Kn,e$P.isTSTypeAliasDeclaration=Pa,e$P.isTSTypeAnnotation=ja,e$P.isTSTypeAssertion=ga,e$P.isTSTypeElement=kn,e$P.isTSTypeLiteral=na,e$P.isTSTypeOperator=Sa,e$P.isTSTypeParameter=Va,e$P.isTSTypeParameterDeclaration=va,e$P.isTSTypeParameterInstantiation=Ra,e$P.isTSTypePredicate=ra,e$P.isTSTypeQuery=aa,e$P.isTSTypeReference=ta,e$P.isTSUndefinedKeyword=Gr,e$P.isTSUnionType=ca,e$P.isTSUnknownKeyword=Yr,e$P.isTSVoidKeyword=$r,e$P.isTaggedTemplateExpression=he,e$P.isTemplateElement=we,e$P.isTemplateLiteral=ge,e$P.isTerminatorless=Ya,e$P.isThisExpression=G,e$P.isThisTypeAnnotation=Nt,e$P.isThrowStatement=Y,e$P.isTopicReference=wr,e$P.isTryStatement=$,e$P.isTupleExpression=Pr,e$P.isTupleTypeAnnotation=Mt,e$P.isTypeAlias=Lt,e$P.isTypeAnnotation=Ot,e$P.isTypeCastExpression=Ft,e$P.isTypeParameter=kt,e$P.isTypeParameterDeclaration=Kt,e$P.isTypeParameterInstantiation=Xt,e$P.isTypeScript=Fn,e$P.isTypeofTypeAnnotation=Bt,e$P.isUnaryExpression=H,e$P.isUnaryLike=xn,e$P.isUnionTypeAnnotation=Jt,e$P.isUpdateExpression=Z,e$P.isUserWhitespacable=Tn,e$P.isV8IntrinsicIdentifier=Sr,e$P.isVariableDeclaration=ee,e$P.isVariableDeclarator=te,e$P.isVariance=jt,e$P.isVoidTypeAnnotation=Rt,e$P.isWhile=en,e$P.isWhileStatement=re,e$P.isWithStatement=ae,e$P.isYieldExpression=Ce;var a=a$h(),n=m$4();function s(e,t){return !e||e.type!=="ArrayExpression"?false:t==null||(0, a.default)(e,t)}function u(e,t){return !e||e.type!=="AssignmentExpression"?false:t==null||(0, a.default)(e,t)}function f(e,t){return !e||e.type!=="BinaryExpression"?false:t==null||(0, a.default)(e,t)}function c(e,t){return !e||e.type!=="InterpreterDirective"?false:t==null||(0, a.default)(e,t)}function p(e,t){return !e||e.type!=="Directive"?false:t==null||(0, a.default)(e,t)}function y(e,t){return !e||e.type!=="DirectiveLiteral"?false:t==null||(0, a.default)(e,t)}function o(e,t){return !e||e.type!=="BlockStatement"?false:t==null||(0, a.default)(e,t)}function T(e,t){return !e||e.type!=="BreakStatement"?false:t==null||(0, a.default)(e,t)}function S(e,t){return !e||e.type!=="CallExpression"?false:t==null||(0, a.default)(e,t)}function m(e,t){return !e||e.type!=="CatchClause"?false:t==null||(0, a.default)(e,t)}function E(e,t){return !e||e.type!=="ConditionalExpression"?false:t==null||(0, a.default)(e,t)}function x(e,t){return !e||e.type!=="ContinueStatement"?false:t==null||(0, a.default)(e,t)}function D(e,t){return !e||e.type!=="DebuggerStatement"?false:t==null||(0, a.default)(e,t)}function A(e,t){return !e||e.type!=="DoWhileStatement"?false:t==null||(0, a.default)(e,t)}function b(e,t){return !e||e.type!=="EmptyStatement"?false:t==null||(0, a.default)(e,t)}function d(e,t){return !e||e.type!=="ExpressionStatement"?false:t==null||(0, a.default)(e,t)}function P(e,t){return !e||e.type!=="File"?false:t==null||(0, a.default)(e,t)}function I(e,t){return !e||e.type!=="ForInStatement"?false:t==null||(0, a.default)(e,t)}function h(e,t){return !e||e.type!=="ForStatement"?false:t==null||(0, a.default)(e,t)}function w(e,t){return !e||e.type!=="FunctionDeclaration"?false:t==null||(0, a.default)(e,t)}function g(e,t){return !e||e.type!=="FunctionExpression"?false:t==null||(0, a.default)(e,t)}function C(e,t){return !e||e.type!=="Identifier"?false:t==null||(0, a.default)(e,t)}function N(e,t){return !e||e.type!=="IfStatement"?false:t==null||(0, a.default)(e,t)}function M(e,t){return !e||e.type!=="LabeledStatement"?false:t==null||(0, a.default)(e,t)}function B(e,t){return !e||e.type!=="StringLiteral"?false:t==null||(0, a.default)(e,t)}function L(e,t){return !e||e.type!=="NumericLiteral"?false:t==null||(0, a.default)(e,t)}function O(e,t){return !e||e.type!=="NullLiteral"?false:t==null||(0, a.default)(e,t)}function F(e,t){return !e||e.type!=="BooleanLiteral"?false:t==null||(0, a.default)(e,t)}function k(e,t){return !e||e.type!=="RegExpLiteral"?false:t==null||(0, a.default)(e,t)}function K(e,t){return !e||e.type!=="LogicalExpression"?false:t==null||(0, a.default)(e,t)}function X(e,t){return !e||e.type!=="MemberExpression"?false:t==null||(0, a.default)(e,t)}function J(e,t){return !e||e.type!=="NewExpression"?false:t==null||(0, a.default)(e,t)}function j(e,t){return !e||e.type!=="Program"?false:t==null||(0, a.default)(e,t)}function R(e,t){return !e||e.type!=="ObjectExpression"?false:t==null||(0, a.default)(e,t)}function v(e,t){return !e||e.type!=="ObjectMethod"?false:t==null||(0, a.default)(e,t)}function V(e,t){return !e||e.type!=="ObjectProperty"?false:t==null||(0, a.default)(e,t)}function U(e,t){return !e||e.type!=="RestElement"?false:t==null||(0, a.default)(e,t)}function W(e,t){return !e||e.type!=="ReturnStatement"?false:t==null||(0, a.default)(e,t)}function q(e,t){return !e||e.type!=="SequenceExpression"?false:t==null||(0, a.default)(e,t)}function Q(e,t){return !e||e.type!=="ParenthesizedExpression"?false:t==null||(0, a.default)(e,t)}function z(e,t){return !e||e.type!=="SwitchCase"?false:t==null||(0, a.default)(e,t)}function _(e,t){return !e||e.type!=="SwitchStatement"?false:t==null||(0, a.default)(e,t)}function G(e,t){return !e||e.type!=="ThisExpression"?false:t==null||(0, a.default)(e,t)}function Y(e,t){return !e||e.type!=="ThrowStatement"?false:t==null||(0, a.default)(e,t)}function $(e,t){return !e||e.type!=="TryStatement"?false:t==null||(0, a.default)(e,t)}function H(e,t){return !e||e.type!=="UnaryExpression"?false:t==null||(0, a.default)(e,t)}function Z(e,t){return !e||e.type!=="UpdateExpression"?false:t==null||(0, a.default)(e,t)}function ee(e,t){return !e||e.type!=="VariableDeclaration"?false:t==null||(0, a.default)(e,t)}function te(e,t){return !e||e.type!=="VariableDeclarator"?false:t==null||(0, a.default)(e,t)}function re(e,t){return !e||e.type!=="WhileStatement"?false:t==null||(0, a.default)(e,t)}function ae(e,t){return !e||e.type!=="WithStatement"?false:t==null||(0, a.default)(e,t)}function ne(e,t){return !e||e.type!=="AssignmentPattern"?false:t==null||(0, a.default)(e,t)}function ie(e,t){return !e||e.type!=="ArrayPattern"?false:t==null||(0, a.default)(e,t)}function le(e,t){return !e||e.type!=="ArrowFunctionExpression"?false:t==null||(0, a.default)(e,t)}function se(e,t){return !e||e.type!=="ClassBody"?false:t==null||(0, a.default)(e,t)}function ue(e,t){return !e||e.type!=="ClassExpression"?false:t==null||(0, a.default)(e,t)}function fe(e,t){return !e||e.type!=="ClassDeclaration"?false:t==null||(0, a.default)(e,t)}function ce(e,t){return !e||e.type!=="ExportAllDeclaration"?false:t==null||(0, a.default)(e,t)}function pe(e,t){return !e||e.type!=="ExportDefaultDeclaration"?false:t==null||(0, a.default)(e,t)}function ye(e,t){return !e||e.type!=="ExportNamedDeclaration"?false:t==null||(0, a.default)(e,t)}function oe(e,t){return !e||e.type!=="ExportSpecifier"?false:t==null||(0, a.default)(e,t)}function Te(e,t){return !e||e.type!=="ForOfStatement"?false:t==null||(0, a.default)(e,t)}function Se(e,t){return !e||e.type!=="ImportDeclaration"?false:t==null||(0, a.default)(e,t)}function me(e,t){return !e||e.type!=="ImportDefaultSpecifier"?false:t==null||(0, a.default)(e,t)}function Ee(e,t){return !e||e.type!=="ImportNamespaceSpecifier"?false:t==null||(0, a.default)(e,t)}function xe(e,t){return !e||e.type!=="ImportSpecifier"?false:t==null||(0, a.default)(e,t)}function De(e,t){return !e||e.type!=="ImportExpression"?false:t==null||(0, a.default)(e,t)}function Ae(e,t){return !e||e.type!=="MetaProperty"?false:t==null||(0, a.default)(e,t)}function be(e,t){return !e||e.type!=="ClassMethod"?false:t==null||(0, a.default)(e,t)}function de(e,t){return !e||e.type!=="ObjectPattern"?false:t==null||(0, a.default)(e,t)}function Pe(e,t){return !e||e.type!=="SpreadElement"?false:t==null||(0, a.default)(e,t)}function Ie(e,t){return !e||e.type!=="Super"?false:t==null||(0, a.default)(e,t)}function he(e,t){return !e||e.type!=="TaggedTemplateExpression"?false:t==null||(0, a.default)(e,t)}function we(e,t){return !e||e.type!=="TemplateElement"?false:t==null||(0, a.default)(e,t)}function ge(e,t){return !e||e.type!=="TemplateLiteral"?false:t==null||(0, a.default)(e,t)}function Ce(e,t){return !e||e.type!=="YieldExpression"?false:t==null||(0, a.default)(e,t)}function Ne(e,t){return !e||e.type!=="AwaitExpression"?false:t==null||(0, a.default)(e,t)}function Me(e,t){return !e||e.type!=="Import"?false:t==null||(0, a.default)(e,t)}function Be(e,t){return !e||e.type!=="BigIntLiteral"?false:t==null||(0, a.default)(e,t)}function Le(e,t){return !e||e.type!=="ExportNamespaceSpecifier"?false:t==null||(0, a.default)(e,t)}function Oe(e,t){return !e||e.type!=="OptionalMemberExpression"?false:t==null||(0, a.default)(e,t)}function Fe(e,t){return !e||e.type!=="OptionalCallExpression"?false:t==null||(0, a.default)(e,t)}function ke(e,t){return !e||e.type!=="ClassProperty"?false:t==null||(0, a.default)(e,t)}function Ke(e,t){return !e||e.type!=="ClassAccessorProperty"?false:t==null||(0, a.default)(e,t)}function Xe(e,t){return !e||e.type!=="ClassPrivateProperty"?false:t==null||(0, a.default)(e,t)}function Je(e,t){return !e||e.type!=="ClassPrivateMethod"?false:t==null||(0, a.default)(e,t)}function je(e,t){return !e||e.type!=="PrivateName"?false:t==null||(0, a.default)(e,t)}function Re(e,t){return !e||e.type!=="StaticBlock"?false:t==null||(0, a.default)(e,t)}function ve(e,t){return !e||e.type!=="AnyTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Ve(e,t){return !e||e.type!=="ArrayTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Ue(e,t){return !e||e.type!=="BooleanTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function We(e,t){return !e||e.type!=="BooleanLiteralTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function qe(e,t){return !e||e.type!=="NullLiteralTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Qe(e,t){return !e||e.type!=="ClassImplements"?false:t==null||(0, a.default)(e,t)}function ze(e,t){return !e||e.type!=="DeclareClass"?false:t==null||(0, a.default)(e,t)}function _e(e,t){return !e||e.type!=="DeclareFunction"?false:t==null||(0, a.default)(e,t)}function Ge(e,t){return !e||e.type!=="DeclareInterface"?false:t==null||(0, a.default)(e,t)}function Ye(e,t){return !e||e.type!=="DeclareModule"?false:t==null||(0, a.default)(e,t)}function $e(e,t){return !e||e.type!=="DeclareModuleExports"?false:t==null||(0, a.default)(e,t)}function He(e,t){return !e||e.type!=="DeclareTypeAlias"?false:t==null||(0, a.default)(e,t)}function Ze(e,t){return !e||e.type!=="DeclareOpaqueType"?false:t==null||(0, a.default)(e,t)}function et(e,t){return !e||e.type!=="DeclareVariable"?false:t==null||(0, a.default)(e,t)}function tt(e,t){return !e||e.type!=="DeclareExportDeclaration"?false:t==null||(0, a.default)(e,t)}function rt(e,t){return !e||e.type!=="DeclareExportAllDeclaration"?false:t==null||(0, a.default)(e,t)}function at(e,t){return !e||e.type!=="DeclaredPredicate"?false:t==null||(0, a.default)(e,t)}function nt(e,t){return !e||e.type!=="ExistsTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function it(e,t){return !e||e.type!=="FunctionTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function lt(e,t){return !e||e.type!=="FunctionTypeParam"?false:t==null||(0, a.default)(e,t)}function st(e,t){return !e||e.type!=="GenericTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function ut(e,t){return !e||e.type!=="InferredPredicate"?false:t==null||(0, a.default)(e,t)}function ft(e,t){return !e||e.type!=="InterfaceExtends"?false:t==null||(0, a.default)(e,t)}function ct(e,t){return !e||e.type!=="InterfaceDeclaration"?false:t==null||(0, a.default)(e,t)}function pt(e,t){return !e||e.type!=="InterfaceTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function yt(e,t){return !e||e.type!=="IntersectionTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function ot(e,t){return !e||e.type!=="MixedTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Tt(e,t){return !e||e.type!=="EmptyTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function St(e,t){return !e||e.type!=="NullableTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function mt(e,t){return !e||e.type!=="NumberLiteralTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Et(e,t){return !e||e.type!=="NumberTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function xt(e,t){return !e||e.type!=="ObjectTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Dt(e,t){return !e||e.type!=="ObjectTypeInternalSlot"?false:t==null||(0, a.default)(e,t)}function At(e,t){return !e||e.type!=="ObjectTypeCallProperty"?false:t==null||(0, a.default)(e,t)}function bt(e,t){return !e||e.type!=="ObjectTypeIndexer"?false:t==null||(0, a.default)(e,t)}function dt(e,t){return !e||e.type!=="ObjectTypeProperty"?false:t==null||(0, a.default)(e,t)}function Pt(e,t){return !e||e.type!=="ObjectTypeSpreadProperty"?false:t==null||(0, a.default)(e,t)}function It(e,t){return !e||e.type!=="OpaqueType"?false:t==null||(0, a.default)(e,t)}function ht(e,t){return !e||e.type!=="QualifiedTypeIdentifier"?false:t==null||(0, a.default)(e,t)}function wt(e,t){return !e||e.type!=="StringLiteralTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function gt(e,t){return !e||e.type!=="StringTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Ct(e,t){return !e||e.type!=="SymbolTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Nt(e,t){return !e||e.type!=="ThisTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Mt(e,t){return !e||e.type!=="TupleTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Bt(e,t){return !e||e.type!=="TypeofTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Lt(e,t){return !e||e.type!=="TypeAlias"?false:t==null||(0, a.default)(e,t)}function Ot(e,t){return !e||e.type!=="TypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Ft(e,t){return !e||e.type!=="TypeCastExpression"?false:t==null||(0, a.default)(e,t)}function kt(e,t){return !e||e.type!=="TypeParameter"?false:t==null||(0, a.default)(e,t)}function Kt(e,t){return !e||e.type!=="TypeParameterDeclaration"?false:t==null||(0, a.default)(e,t)}function Xt(e,t){return !e||e.type!=="TypeParameterInstantiation"?false:t==null||(0, a.default)(e,t)}function Jt(e,t){return !e||e.type!=="UnionTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function jt(e,t){return !e||e.type!=="Variance"?false:t==null||(0, a.default)(e,t)}function Rt(e,t){return !e||e.type!=="VoidTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function vt(e,t){return !e||e.type!=="EnumDeclaration"?false:t==null||(0, a.default)(e,t)}function Vt(e,t){return !e||e.type!=="EnumBooleanBody"?false:t==null||(0, a.default)(e,t)}function Ut(e,t){return !e||e.type!=="EnumNumberBody"?false:t==null||(0, a.default)(e,t)}function Wt(e,t){return !e||e.type!=="EnumStringBody"?false:t==null||(0, a.default)(e,t)}function qt(e,t){return !e||e.type!=="EnumSymbolBody"?false:t==null||(0, a.default)(e,t)}function Qt(e,t){return !e||e.type!=="EnumBooleanMember"?false:t==null||(0, a.default)(e,t)}function zt(e,t){return !e||e.type!=="EnumNumberMember"?false:t==null||(0, a.default)(e,t)}function _t(e,t){return !e||e.type!=="EnumStringMember"?false:t==null||(0, a.default)(e,t)}function Gt(e,t){return !e||e.type!=="EnumDefaultedMember"?false:t==null||(0, a.default)(e,t)}function Yt(e,t){return !e||e.type!=="IndexedAccessType"?false:t==null||(0, a.default)(e,t)}function $t(e,t){return !e||e.type!=="OptionalIndexedAccessType"?false:t==null||(0, a.default)(e,t)}function Ht(e,t){return !e||e.type!=="JSXAttribute"?false:t==null||(0, a.default)(e,t)}function Zt(e,t){return !e||e.type!=="JSXClosingElement"?false:t==null||(0, a.default)(e,t)}function er(e,t){return !e||e.type!=="JSXElement"?false:t==null||(0, a.default)(e,t)}function tr(e,t){return !e||e.type!=="JSXEmptyExpression"?false:t==null||(0, a.default)(e,t)}function rr(e,t){return !e||e.type!=="JSXExpressionContainer"?false:t==null||(0, a.default)(e,t)}function ar(e,t){return !e||e.type!=="JSXSpreadChild"?false:t==null||(0, a.default)(e,t)}function nr(e,t){return !e||e.type!=="JSXIdentifier"?false:t==null||(0, a.default)(e,t)}function ir(e,t){return !e||e.type!=="JSXMemberExpression"?false:t==null||(0, a.default)(e,t)}function lr(e,t){return !e||e.type!=="JSXNamespacedName"?false:t==null||(0, a.default)(e,t)}function sr(e,t){return !e||e.type!=="JSXOpeningElement"?false:t==null||(0, a.default)(e,t)}function ur(e,t){return !e||e.type!=="JSXSpreadAttribute"?false:t==null||(0, a.default)(e,t)}function fr(e,t){return !e||e.type!=="JSXText"?false:t==null||(0, a.default)(e,t)}function cr(e,t){return !e||e.type!=="JSXFragment"?false:t==null||(0, a.default)(e,t)}function pr(e,t){return !e||e.type!=="JSXOpeningFragment"?false:t==null||(0, a.default)(e,t)}function yr(e,t){return !e||e.type!=="JSXClosingFragment"?false:t==null||(0, a.default)(e,t)}function or(e,t){return !e||e.type!=="Noop"?false:t==null||(0, a.default)(e,t)}function Tr(e,t){return !e||e.type!=="Placeholder"?false:t==null||(0, a.default)(e,t)}function Sr(e,t){return !e||e.type!=="V8IntrinsicIdentifier"?false:t==null||(0, a.default)(e,t)}function mr(e,t){return !e||e.type!=="ArgumentPlaceholder"?false:t==null||(0, a.default)(e,t)}function Er(e,t){return !e||e.type!=="BindExpression"?false:t==null||(0, a.default)(e,t)}function xr(e,t){return !e||e.type!=="ImportAttribute"?false:t==null||(0, a.default)(e,t)}function Dr(e,t){return !e||e.type!=="Decorator"?false:t==null||(0, a.default)(e,t)}function Ar(e,t){return !e||e.type!=="DoExpression"?false:t==null||(0, a.default)(e,t)}function br(e,t){return !e||e.type!=="ExportDefaultSpecifier"?false:t==null||(0, a.default)(e,t)}function dr(e,t){return !e||e.type!=="RecordExpression"?false:t==null||(0, a.default)(e,t)}function Pr(e,t){return !e||e.type!=="TupleExpression"?false:t==null||(0, a.default)(e,t)}function Ir(e,t){return !e||e.type!=="DecimalLiteral"?false:t==null||(0, a.default)(e,t)}function hr(e,t){return !e||e.type!=="ModuleExpression"?false:t==null||(0, a.default)(e,t)}function wr(e,t){return !e||e.type!=="TopicReference"?false:t==null||(0, a.default)(e,t)}function gr(e,t){return !e||e.type!=="PipelineTopicExpression"?false:t==null||(0, a.default)(e,t)}function Cr(e,t){return !e||e.type!=="PipelineBareFunction"?false:t==null||(0, a.default)(e,t)}function Nr(e,t){return !e||e.type!=="PipelinePrimaryTopicReference"?false:t==null||(0, a.default)(e,t)}function Mr(e,t){return !e||e.type!=="TSParameterProperty"?false:t==null||(0, a.default)(e,t)}function Br(e,t){return !e||e.type!=="TSDeclareFunction"?false:t==null||(0, a.default)(e,t)}function Lr(e,t){return !e||e.type!=="TSDeclareMethod"?false:t==null||(0, a.default)(e,t)}function Or(e,t){return !e||e.type!=="TSQualifiedName"?false:t==null||(0, a.default)(e,t)}function Fr(e,t){return !e||e.type!=="TSCallSignatureDeclaration"?false:t==null||(0, a.default)(e,t)}function kr(e,t){return !e||e.type!=="TSConstructSignatureDeclaration"?false:t==null||(0, a.default)(e,t)}function Kr(e,t){return !e||e.type!=="TSPropertySignature"?false:t==null||(0, a.default)(e,t)}function Xr(e,t){return !e||e.type!=="TSMethodSignature"?false:t==null||(0, a.default)(e,t)}function Jr(e,t){return !e||e.type!=="TSIndexSignature"?false:t==null||(0, a.default)(e,t)}function jr(e,t){return !e||e.type!=="TSAnyKeyword"?false:t==null||(0, a.default)(e,t)}function Rr(e,t){return !e||e.type!=="TSBooleanKeyword"?false:t==null||(0, a.default)(e,t)}function vr(e,t){return !e||e.type!=="TSBigIntKeyword"?false:t==null||(0, a.default)(e,t)}function Vr(e,t){return !e||e.type!=="TSIntrinsicKeyword"?false:t==null||(0, a.default)(e,t)}function Ur(e,t){return !e||e.type!=="TSNeverKeyword"?false:t==null||(0, a.default)(e,t)}function Wr(e,t){return !e||e.type!=="TSNullKeyword"?false:t==null||(0, a.default)(e,t)}function qr(e,t){return !e||e.type!=="TSNumberKeyword"?false:t==null||(0, a.default)(e,t)}function Qr(e,t){return !e||e.type!=="TSObjectKeyword"?false:t==null||(0, a.default)(e,t)}function zr(e,t){return !e||e.type!=="TSStringKeyword"?false:t==null||(0, a.default)(e,t)}function _r(e,t){return !e||e.type!=="TSSymbolKeyword"?false:t==null||(0, a.default)(e,t)}function Gr(e,t){return !e||e.type!=="TSUndefinedKeyword"?false:t==null||(0, a.default)(e,t)}function Yr(e,t){return !e||e.type!=="TSUnknownKeyword"?false:t==null||(0, a.default)(e,t)}function $r(e,t){return !e||e.type!=="TSVoidKeyword"?false:t==null||(0, a.default)(e,t)}function Hr(e,t){return !e||e.type!=="TSThisType"?false:t==null||(0, a.default)(e,t)}function Zr(e,t){return !e||e.type!=="TSFunctionType"?false:t==null||(0, a.default)(e,t)}function ea(e,t){return !e||e.type!=="TSConstructorType"?false:t==null||(0, a.default)(e,t)}function ta(e,t){return !e||e.type!=="TSTypeReference"?false:t==null||(0, a.default)(e,t)}function ra(e,t){return !e||e.type!=="TSTypePredicate"?false:t==null||(0, a.default)(e,t)}function aa(e,t){return !e||e.type!=="TSTypeQuery"?false:t==null||(0, a.default)(e,t)}function na(e,t){return !e||e.type!=="TSTypeLiteral"?false:t==null||(0, a.default)(e,t)}function ia(e,t){return !e||e.type!=="TSArrayType"?false:t==null||(0, a.default)(e,t)}function la(e,t){return !e||e.type!=="TSTupleType"?false:t==null||(0, a.default)(e,t)}function sa(e,t){return !e||e.type!=="TSOptionalType"?false:t==null||(0, a.default)(e,t)}function ua(e,t){return !e||e.type!=="TSRestType"?false:t==null||(0, a.default)(e,t)}function fa(e,t){return !e||e.type!=="TSNamedTupleMember"?false:t==null||(0, a.default)(e,t)}function ca(e,t){return !e||e.type!=="TSUnionType"?false:t==null||(0, a.default)(e,t)}function pa(e,t){return !e||e.type!=="TSIntersectionType"?false:t==null||(0, a.default)(e,t)}function ya(e,t){return !e||e.type!=="TSConditionalType"?false:t==null||(0, a.default)(e,t)}function oa(e,t){return !e||e.type!=="TSInferType"?false:t==null||(0, a.default)(e,t)}function Ta(e,t){return !e||e.type!=="TSParenthesizedType"?false:t==null||(0, a.default)(e,t)}function Sa(e,t){return !e||e.type!=="TSTypeOperator"?false:t==null||(0, a.default)(e,t)}function ma(e,t){return !e||e.type!=="TSIndexedAccessType"?false:t==null||(0, a.default)(e,t)}function Ea(e,t){return !e||e.type!=="TSMappedType"?false:t==null||(0, a.default)(e,t)}function xa(e,t){return !e||e.type!=="TSTemplateLiteralType"?false:t==null||(0, a.default)(e,t)}function Da(e,t){return !e||e.type!=="TSLiteralType"?false:t==null||(0, a.default)(e,t)}function Aa(e,t){return !e||e.type!=="TSExpressionWithTypeArguments"?false:t==null||(0, a.default)(e,t)}function ba(e,t){return !e||e.type!=="TSInterfaceDeclaration"?false:t==null||(0, a.default)(e,t)}function da(e,t){return !e||e.type!=="TSInterfaceBody"?false:t==null||(0, a.default)(e,t)}function Pa(e,t){return !e||e.type!=="TSTypeAliasDeclaration"?false:t==null||(0, a.default)(e,t)}function Ia(e,t){return !e||e.type!=="TSInstantiationExpression"?false:t==null||(0, a.default)(e,t)}function ha(e,t){return !e||e.type!=="TSAsExpression"?false:t==null||(0, a.default)(e,t)}function wa(e,t){return !e||e.type!=="TSSatisfiesExpression"?false:t==null||(0, a.default)(e,t)}function ga(e,t){return !e||e.type!=="TSTypeAssertion"?false:t==null||(0, a.default)(e,t)}function Ca(e,t){return !e||e.type!=="TSEnumBody"?false:t==null||(0, a.default)(e,t)}function Na(e,t){return !e||e.type!=="TSEnumDeclaration"?false:t==null||(0, a.default)(e,t)}function Ma(e,t){return !e||e.type!=="TSEnumMember"?false:t==null||(0, a.default)(e,t)}function Ba(e,t){return !e||e.type!=="TSModuleDeclaration"?false:t==null||(0, a.default)(e,t)}function La(e,t){return !e||e.type!=="TSModuleBlock"?false:t==null||(0, a.default)(e,t)}function Oa(e,t){return !e||e.type!=="TSImportType"?false:t==null||(0, a.default)(e,t)}function Fa(e,t){return !e||e.type!=="TSImportEqualsDeclaration"?false:t==null||(0, a.default)(e,t)}function ka(e,t){return !e||e.type!=="TSExternalModuleReference"?false:t==null||(0, a.default)(e,t)}function Ka(e,t){return !e||e.type!=="TSNonNullExpression"?false:t==null||(0, a.default)(e,t)}function Xa(e,t){return !e||e.type!=="TSExportAssignment"?false:t==null||(0, a.default)(e,t)}function Ja(e,t){return !e||e.type!=="TSNamespaceExportDeclaration"?false:t==null||(0, a.default)(e,t)}function ja(e,t){return !e||e.type!=="TSTypeAnnotation"?false:t==null||(0, a.default)(e,t)}function Ra(e,t){return !e||e.type!=="TSTypeParameterInstantiation"?false:t==null||(0, a.default)(e,t)}function va(e,t){return !e||e.type!=="TSTypeParameterDeclaration"?false:t==null||(0, a.default)(e,t)}function Va(e,t){return !e||e.type!=="TSTypeParameter"?false:t==null||(0, a.default)(e,t)}function Ua(e,t){if(!e)return  false;switch(e.type){case "ArrayExpression":case "AssignmentExpression":case "BinaryExpression":case "InterpreterDirective":case "Directive":case "DirectiveLiteral":case "BlockStatement":case "BreakStatement":case "CallExpression":case "CatchClause":case "ConditionalExpression":case "ContinueStatement":case "DebuggerStatement":case "DoWhileStatement":case "EmptyStatement":case "ExpressionStatement":case "File":case "ForInStatement":case "ForStatement":case "FunctionDeclaration":case "FunctionExpression":case "Identifier":case "IfStatement":case "LabeledStatement":case "StringLiteral":case "NumericLiteral":case "NullLiteral":case "BooleanLiteral":case "RegExpLiteral":case "LogicalExpression":case "MemberExpression":case "NewExpression":case "Program":case "ObjectExpression":case "ObjectMethod":case "ObjectProperty":case "RestElement":case "ReturnStatement":case "SequenceExpression":case "ParenthesizedExpression":case "SwitchCase":case "SwitchStatement":case "ThisExpression":case "ThrowStatement":case "TryStatement":case "UnaryExpression":case "UpdateExpression":case "VariableDeclaration":case "VariableDeclarator":case "WhileStatement":case "WithStatement":case "AssignmentPattern":case "ArrayPattern":case "ArrowFunctionExpression":case "ClassBody":case "ClassExpression":case "ClassDeclaration":case "ExportAllDeclaration":case "ExportDefaultDeclaration":case "ExportNamedDeclaration":case "ExportSpecifier":case "ForOfStatement":case "ImportDeclaration":case "ImportDefaultSpecifier":case "ImportNamespaceSpecifier":case "ImportSpecifier":case "ImportExpression":case "MetaProperty":case "ClassMethod":case "ObjectPattern":case "SpreadElement":case "Super":case "TaggedTemplateExpression":case "TemplateElement":case "TemplateLiteral":case "YieldExpression":case "AwaitExpression":case "Import":case "BigIntLiteral":case "ExportNamespaceSpecifier":case "OptionalMemberExpression":case "OptionalCallExpression":case "ClassProperty":case "ClassAccessorProperty":case "ClassPrivateProperty":case "ClassPrivateMethod":case "PrivateName":case "StaticBlock":break;case "Placeholder":switch(e.expectedNode){case "Identifier":case "StringLiteral":case "BlockStatement":case "ClassBody":break;default:return  false}break;default:return  false}return t==null||(0, a.default)(e,t)}function Wa(e,t){if(!e)return  false;switch(e.type){case "ArrayExpression":case "AssignmentExpression":case "BinaryExpression":case "CallExpression":case "ConditionalExpression":case "FunctionExpression":case "Identifier":case "StringLiteral":case "NumericLiteral":case "NullLiteral":case "BooleanLiteral":case "RegExpLiteral":case "LogicalExpression":case "MemberExpression":case "NewExpression":case "ObjectExpression":case "SequenceExpression":case "ParenthesizedExpression":case "ThisExpression":case "UnaryExpression":case "UpdateExpression":case "ArrowFunctionExpression":case "ClassExpression":case "ImportExpression":case "MetaProperty":case "Super":case "TaggedTemplateExpression":case "TemplateLiteral":case "YieldExpression":case "AwaitExpression":case "Import":case "BigIntLiteral":case "OptionalMemberExpression":case "OptionalCallExpression":case "TypeCastExpression":case "JSXElement":case "JSXFragment":case "BindExpression":case "DoExpression":case "RecordExpression":case "TupleExpression":case "DecimalLiteral":case "ModuleExpression":case "TopicReference":case "PipelineTopicExpression":case "PipelineBareFunction":case "PipelinePrimaryTopicReference":case "TSInstantiationExpression":case "TSAsExpression":case "TSSatisfiesExpression":case "TSTypeAssertion":case "TSNonNullExpression":break;case "Placeholder":switch(e.expectedNode){case "Expression":case "Identifier":case "StringLiteral":break;default:return  false}break;default:return  false}return t==null||(0, a.default)(e,t)}function qa(e,t){if(!e)return  false;switch(e.type){case "BinaryExpression":case "LogicalExpression":break;default:return  false}return t==null||(0, a.default)(e,t)}function Qa(e,t){if(!e)return  false;switch(e.type){case "BlockStatement":case "CatchClause":case "DoWhileStatement":case "ForInStatement":case "ForStatement":case "FunctionDeclaration":case "FunctionExpression":case "Program":case "ObjectMethod":case "SwitchStatement":case "WhileStatement":case "ArrowFunctionExpression":case "ClassExpression":case "ClassDeclaration":case "ForOfStatement":case "ClassMethod":case "ClassPrivateMethod":case "StaticBlock":case "TSModuleBlock":break;case "Placeholder":if(e.expectedNode==="BlockStatement")break;default:return  false}return t==null||(0, a.default)(e,t)}function za(e,t){if(!e)return  false;switch(e.type){case "BlockStatement":case "CatchClause":case "DoWhileStatement":case "ForInStatement":case "ForStatement":case "FunctionDeclaration":case "FunctionExpression":case "Program":case "ObjectMethod":case "SwitchStatement":case "WhileStatement":case "ArrowFunctionExpression":case "ForOfStatement":case "ClassMethod":case "ClassPrivateMethod":case "StaticBlock":case "TSModuleBlock":break;case "Placeholder":if(e.expectedNode==="BlockStatement")break;default:return  false}return t==null||(0, a.default)(e,t)}function _a(e,t){if(!e)return  false;switch(e.type){case "BlockStatement":case "Program":case "TSModuleBlock":break;case "Placeholder":if(e.expectedNode==="BlockStatement")break;default:return  false}return t==null||(0, a.default)(e,t)}function Ga(e,t){if(!e)return  false;switch(e.type){case "BlockStatement":case "BreakStatement":case "ContinueStatement":case "DebuggerStatement":case "DoWhileStatement":case "EmptyStatement":case "ExpressionStatement":case "ForInStatement":case "ForStatement":case "FunctionDeclaration":case "IfStatement":case "LabeledStatement":case "ReturnStatement":case "SwitchStatement":case "ThrowStatement":case "TryStatement":case "VariableDeclaration":case "WhileStatement":case "WithStatement":case "ClassDeclaration":case "ExportAllDeclaration":case "ExportDefaultDeclaration":case "ExportNamedDeclaration":case "ForOfStatement":case "ImportDeclaration":case "DeclareClass":case "DeclareFunction":case "DeclareInterface":case "DeclareModule":case "DeclareModuleExports":case "DeclareTypeAlias":case "DeclareOpaqueType":case "DeclareVariable":case "DeclareExportDeclaration":case "DeclareExportAllDeclaration":case "InterfaceDeclaration":case "OpaqueType":case "TypeAlias":case "EnumDeclaration":case "TSDeclareFunction":case "TSInterfaceDeclaration":case "TSTypeAliasDeclaration":case "TSEnumDeclaration":case "TSModuleDeclaration":case "TSImportEqualsDeclaration":case "TSExportAssignment":case "TSNamespaceExportDeclaration":break;case "Placeholder":switch(e.expectedNode){case "Statement":case "Declaration":case "BlockStatement":break;default:return  false}break;default:return  false}return t==null||(0, a.default)(e,t)}function Ya(e,t){if(!e)return  false;switch(e.type){case "BreakStatement":case "ContinueStatement":case "ReturnStatement":case "ThrowStatement":case "YieldExpression":case "AwaitExpression":break;default:return  false}return t==null||(0, a.default)(e,t)}function $a(e,t){if(!e)return  false;switch(e.type){case "BreakStatement":case "ContinueStatement":case "ReturnStatement":case "ThrowStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function Ha(e,t){if(!e)return  false;switch(e.type){case "ConditionalExpression":case "IfStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function Za(e,t){if(!e)return  false;switch(e.type){case "DoWhileStatement":case "ForInStatement":case "ForStatement":case "WhileStatement":case "ForOfStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function en(e,t){if(!e)return  false;switch(e.type){case "DoWhileStatement":case "WhileStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function tn(e,t){if(!e)return  false;switch(e.type){case "ExpressionStatement":case "ParenthesizedExpression":case "TypeCastExpression":break;default:return  false}return t==null||(0, a.default)(e,t)}function rn(e,t){if(!e)return  false;switch(e.type){case "ForInStatement":case "ForStatement":case "ForOfStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function an(e,t){if(!e)return  false;switch(e.type){case "ForInStatement":case "ForOfStatement":break;default:return  false}return t==null||(0, a.default)(e,t)}function nn(e,t){if(!e)return  false;switch(e.type){case "FunctionDeclaration":case "FunctionExpression":case "ObjectMethod":case "ArrowFunctionExpression":case "ClassMethod":case "ClassPrivateMethod":break;default:return  false}return t==null||(0, a.default)(e,t)}function ln(e,t){if(!e)return  false;switch(e.type){case "FunctionDeclaration":case "FunctionExpression":case "ObjectMethod":case "ArrowFunctionExpression":case "ClassMethod":case "ClassPrivateMethod":case "StaticBlock":case "TSModuleBlock":break;default:return  false}return t==null||(0, a.default)(e,t)}function sn(e,t){if(!e)return  false;switch(e.type){case "FunctionDeclaration":case "FunctionExpression":case "StringLiteral":case "NumericLiteral":case "NullLiteral":case "BooleanLiteral":case "RegExpLiteral":case "ArrowFunctionExpression":case "BigIntLiteral":case "DecimalLiteral":break;case "Placeholder":if(e.expectedNode==="StringLiteral")break;default:return  false}return t==null||(0, a.default)(e,t)}function un(e,t){if(!e)return  false;switch(e.type){case "FunctionDeclaration":case "VariableDeclaration":case "ClassDeclaration":case "ExportAllDeclaration":case "ExportDefaultDeclaration":case "ExportNamedDeclaration":case "ImportDeclaration":case "DeclareClass":case "DeclareFunction":case "DeclareInterface":case "DeclareModule":case "DeclareModuleExports":case "DeclareTypeAlias":case "DeclareOpaqueType":case "DeclareVariable":case "DeclareExportDeclaration":case "DeclareExportAllDeclaration":case "InterfaceDeclaration":case "OpaqueType":case "TypeAlias":case "EnumDeclaration":case "TSDeclareFunction":case "TSInterfaceDeclaration":case "TSTypeAliasDeclaration":case "TSEnumDeclaration":case "TSModuleDeclaration":case "TSImportEqualsDeclaration":break;case "Placeholder":if(e.expectedNode==="Declaration")break;default:return  false}return t==null||(0, a.default)(e,t)}function fn(e,t){if(!e)return  false;switch(e.type){case "Identifier":case "RestElement":case "AssignmentPattern":case "ArrayPattern":case "ObjectPattern":case "TSAsExpression":case "TSSatisfiesExpression":case "TSTypeAssertion":case "TSNonNullExpression":break;case "Placeholder":switch(e.expectedNode){case "Pattern":case "Identifier":break;default:return  false}break;default:return  false}return t==null||(0, a.default)(e,t)}function cn(e,t){if(!e)return  false;switch(e.type){case "Identifier":case "MemberExpression":case "RestElement":case "AssignmentPattern":case "ArrayPattern":case "ObjectPattern":case "TSParameterProperty":case "TSAsExpression":case "TSSatisfiesExpression":case "TSTypeAssertion":case "TSNonNullExpression":break;case "Placeholder":switch(e.expectedNode){case "Pattern":case "Identifier":break;default:return  false}break;default:return  false}return t==null||(0, a.default)(e,t)}function pn(e,t){if(!e)return  false;switch(e.type){case "Identifier":case "TSQualifiedName":break;case "Placeholder":if(e.expectedNode==="Identifier")break;default:return  false}return t==null||(0, a.default)(e,t)}function yn(e,t){if(!e)return  false;switch(e.type){case "StringLiteral":case "NumericLiteral":case "NullLiteral":case "BooleanLiteral":case "RegExpLiteral":case "TemplateLiteral":case "BigIntLiteral":case "DecimalLiteral":break;case "Placeholder":if(e.expectedNode==="StringLiteral")break;default:return  false}return t==null||(0, a.default)(e,t)}function on(e,t){if(!e)return  false;switch(e.type){case "StringLiteral":case "NumericLiteral":case "NullLiteral":case "BooleanLiteral":case "BigIntLiteral":case "JSXAttribute":case "JSXClosingElement":case "JSXElement":case "JSXExpressionContainer":case "JSXSpreadChild":case "JSXOpeningElement":case "JSXText":case "JSXFragment":case "JSXOpeningFragment":case "JSXClosingFragment":case "DecimalLiteral":break;case "Placeholder":if(e.expectedNode==="StringLiteral")break;default:return  false}return t==null||(0, a.default)(e,t)}function Tn(e,t){if(!e)return  false;switch(e.type){case "ObjectMethod":case "ObjectProperty":case "ObjectTypeInternalSlot":case "ObjectTypeCallProperty":case "ObjectTypeIndexer":case "ObjectTypeProperty":case "ObjectTypeSpreadProperty":break;default:return  false}return t==null||(0, a.default)(e,t)}function Sn(e,t){if(!e)return  false;switch(e.type){case "ObjectMethod":case "ClassMethod":case "ClassPrivateMethod":break;default:return  false}return t==null||(0, a.default)(e,t)}function mn(e,t){if(!e)return  false;switch(e.type){case "ObjectMethod":case "ObjectProperty":break;default:return  false}return t==null||(0, a.default)(e,t)}function En(e,t){if(!e)return  false;switch(e.type){case "ObjectProperty":case "ClassProperty":case "ClassAccessorProperty":case "ClassPrivateProperty":break;default:return  false}return t==null||(0, a.default)(e,t)}function xn(e,t){if(!e)return  false;switch(e.type){case "UnaryExpression":case "SpreadElement":break;default:return  false}return t==null||(0, a.default)(e,t)}function Dn(e,t){if(!e)return  false;switch(e.type){case "AssignmentPattern":case "ArrayPattern":case "ObjectPattern":break;case "Placeholder":if(e.expectedNode==="Pattern")break;default:return  false}return t==null||(0, a.default)(e,t)}function An(e,t){if(!e)return  false;switch(e.type){case "ClassExpression":case "ClassDeclaration":break;default:return  false}return t==null||(0, a.default)(e,t)}function i(e,t){if(!e)return  false;switch(e.type){case "ExportAllDeclaration":case "ExportDefaultDeclaration":case "ExportNamedDeclaration":case "ImportDeclaration":break;default:return  false}return t==null||(0, a.default)(e,t)}function bn(e,t){if(!e)return  false;switch(e.type){case "ExportAllDeclaration":case "ExportDefaultDeclaration":case "ExportNamedDeclaration":break;default:return  false}return t==null||(0, a.default)(e,t)}function dn(e,t){if(!e)return  false;switch(e.type){case "ExportSpecifier":case "ImportDefaultSpecifier":case "ImportNamespaceSpecifier":case "ImportSpecifier":case "ExportNamespaceSpecifier":case "ExportDefaultSpecifier":break;default:return  false}return t==null||(0, a.default)(e,t)}function Pn(e,t){if(!e)return  false;switch(e.type){case "ClassAccessorProperty":break;default:return  false}return t==null||(0, a.default)(e,t)}function In(e,t){if(!e)return  false;switch(e.type){case "ClassPrivateProperty":case "ClassPrivateMethod":case "PrivateName":break;default:return  false}return t==null||(0, a.default)(e,t)}function hn(e,t){if(!e)return  false;switch(e.type){case "AnyTypeAnnotation":case "ArrayTypeAnnotation":case "BooleanTypeAnnotation":case "BooleanLiteralTypeAnnotation":case "NullLiteralTypeAnnotation":case "ClassImplements":case "DeclareClass":case "DeclareFunction":case "DeclareInterface":case "DeclareModule":case "DeclareModuleExports":case "DeclareTypeAlias":case "DeclareOpaqueType":case "DeclareVariable":case "DeclareExportDeclaration":case "DeclareExportAllDeclaration":case "DeclaredPredicate":case "ExistsTypeAnnotation":case "FunctionTypeAnnotation":case "FunctionTypeParam":case "GenericTypeAnnotation":case "InferredPredicate":case "InterfaceExtends":case "InterfaceDeclaration":case "InterfaceTypeAnnotation":case "IntersectionTypeAnnotation":case "MixedTypeAnnotation":case "EmptyTypeAnnotation":case "NullableTypeAnnotation":case "NumberLiteralTypeAnnotation":case "NumberTypeAnnotation":case "ObjectTypeAnnotation":case "ObjectTypeInternalSlot":case "ObjectTypeCallProperty":case "ObjectTypeIndexer":case "ObjectTypeProperty":case "ObjectTypeSpreadProperty":case "OpaqueType":case "QualifiedTypeIdentifier":case "StringLiteralTypeAnnotation":case "StringTypeAnnotation":case "SymbolTypeAnnotation":case "ThisTypeAnnotation":case "TupleTypeAnnotation":case "TypeofTypeAnnotation":case "TypeAlias":case "TypeAnnotation":case "TypeCastExpression":case "TypeParameter":case "TypeParameterDeclaration":case "TypeParameterInstantiation":case "UnionTypeAnnotation":case "Variance":case "VoidTypeAnnotation":case "EnumDeclaration":case "EnumBooleanBody":case "EnumNumberBody":case "EnumStringBody":case "EnumSymbolBody":case "EnumBooleanMember":case "EnumNumberMember":case "EnumStringMember":case "EnumDefaultedMember":case "IndexedAccessType":case "OptionalIndexedAccessType":break;default:return  false}return t==null||(0, a.default)(e,t)}function wn(e,t){if(!e)return  false;switch(e.type){case "AnyTypeAnnotation":case "ArrayTypeAnnotation":case "BooleanTypeAnnotation":case "BooleanLiteralTypeAnnotation":case "NullLiteralTypeAnnotation":case "ExistsTypeAnnotation":case "FunctionTypeAnnotation":case "GenericTypeAnnotation":case "InterfaceTypeAnnotation":case "IntersectionTypeAnnotation":case "MixedTypeAnnotation":case "EmptyTypeAnnotation":case "NullableTypeAnnotation":case "NumberLiteralTypeAnnotation":case "NumberTypeAnnotation":case "ObjectTypeAnnotation":case "StringLiteralTypeAnnotation":case "StringTypeAnnotation":case "SymbolTypeAnnotation":case "ThisTypeAnnotation":case "TupleTypeAnnotation":case "TypeofTypeAnnotation":case "UnionTypeAnnotation":case "VoidTypeAnnotation":case "IndexedAccessType":case "OptionalIndexedAccessType":break;default:return  false}return t==null||(0, a.default)(e,t)}function gn(e,t){if(!e)return  false;switch(e.type){case "AnyTypeAnnotation":case "BooleanTypeAnnotation":case "NullLiteralTypeAnnotation":case "MixedTypeAnnotation":case "EmptyTypeAnnotation":case "NumberTypeAnnotation":case "StringTypeAnnotation":case "SymbolTypeAnnotation":case "ThisTypeAnnotation":case "VoidTypeAnnotation":break;default:return  false}return t==null||(0, a.default)(e,t)}function Cn(e,t){if(!e)return  false;switch(e.type){case "DeclareClass":case "DeclareFunction":case "DeclareInterface":case "DeclareModule":case "DeclareModuleExports":case "DeclareTypeAlias":case "DeclareOpaqueType":case "DeclareVariable":case "DeclareExportDeclaration":case "DeclareExportAllDeclaration":case "InterfaceDeclaration":case "OpaqueType":case "TypeAlias":break;default:return  false}return t==null||(0, a.default)(e,t)}function Nn(e,t){if(!e)return  false;switch(e.type){case "DeclaredPredicate":case "InferredPredicate":break;default:return  false}return t==null||(0, a.default)(e,t)}function Mn(e,t){if(!e)return  false;switch(e.type){case "EnumBooleanBody":case "EnumNumberBody":case "EnumStringBody":case "EnumSymbolBody":break;default:return  false}return t==null||(0, a.default)(e,t)}function Bn(e,t){if(!e)return  false;switch(e.type){case "EnumBooleanMember":case "EnumNumberMember":case "EnumStringMember":case "EnumDefaultedMember":break;default:return  false}return t==null||(0, a.default)(e,t)}function Ln(e,t){if(!e)return  false;switch(e.type){case "JSXAttribute":case "JSXClosingElement":case "JSXElement":case "JSXEmptyExpression":case "JSXExpressionContainer":case "JSXSpreadChild":case "JSXIdentifier":case "JSXMemberExpression":case "JSXNamespacedName":case "JSXOpeningElement":case "JSXSpreadAttribute":case "JSXText":case "JSXFragment":case "JSXOpeningFragment":case "JSXClosingFragment":break;default:return  false}return t==null||(0, a.default)(e,t)}function On(e,t){if(!e)return  false;switch(e.type){case "Noop":case "Placeholder":case "V8IntrinsicIdentifier":break;default:return  false}return t==null||(0, a.default)(e,t)}function Fn(e,t){if(!e)return  false;switch(e.type){case "TSParameterProperty":case "TSDeclareFunction":case "TSDeclareMethod":case "TSQualifiedName":case "TSCallSignatureDeclaration":case "TSConstructSignatureDeclaration":case "TSPropertySignature":case "TSMethodSignature":case "TSIndexSignature":case "TSAnyKeyword":case "TSBooleanKeyword":case "TSBigIntKeyword":case "TSIntrinsicKeyword":case "TSNeverKeyword":case "TSNullKeyword":case "TSNumberKeyword":case "TSObjectKeyword":case "TSStringKeyword":case "TSSymbolKeyword":case "TSUndefinedKeyword":case "TSUnknownKeyword":case "TSVoidKeyword":case "TSThisType":case "TSFunctionType":case "TSConstructorType":case "TSTypeReference":case "TSTypePredicate":case "TSTypeQuery":case "TSTypeLiteral":case "TSArrayType":case "TSTupleType":case "TSOptionalType":case "TSRestType":case "TSNamedTupleMember":case "TSUnionType":case "TSIntersectionType":case "TSConditionalType":case "TSInferType":case "TSParenthesizedType":case "TSTypeOperator":case "TSIndexedAccessType":case "TSMappedType":case "TSTemplateLiteralType":case "TSLiteralType":case "TSExpressionWithTypeArguments":case "TSInterfaceDeclaration":case "TSInterfaceBody":case "TSTypeAliasDeclaration":case "TSInstantiationExpression":case "TSAsExpression":case "TSSatisfiesExpression":case "TSTypeAssertion":case "TSEnumBody":case "TSEnumDeclaration":case "TSEnumMember":case "TSModuleDeclaration":case "TSModuleBlock":case "TSImportType":case "TSImportEqualsDeclaration":case "TSExternalModuleReference":case "TSNonNullExpression":case "TSExportAssignment":case "TSNamespaceExportDeclaration":case "TSTypeAnnotation":case "TSTypeParameterInstantiation":case "TSTypeParameterDeclaration":case "TSTypeParameter":break;default:return  false}return t==null||(0, a.default)(e,t)}function kn(e,t){if(!e)return  false;switch(e.type){case "TSCallSignatureDeclaration":case "TSConstructSignatureDeclaration":case "TSPropertySignature":case "TSMethodSignature":case "TSIndexSignature":break;default:return  false}return t==null||(0, a.default)(e,t)}function Kn(e,t){if(!e)return  false;switch(e.type){case "TSAnyKeyword":case "TSBooleanKeyword":case "TSBigIntKeyword":case "TSIntrinsicKeyword":case "TSNeverKeyword":case "TSNullKeyword":case "TSNumberKeyword":case "TSObjectKeyword":case "TSStringKeyword":case "TSSymbolKeyword":case "TSUndefinedKeyword":case "TSUnknownKeyword":case "TSVoidKeyword":case "TSThisType":case "TSFunctionType":case "TSConstructorType":case "TSTypeReference":case "TSTypePredicate":case "TSTypeQuery":case "TSTypeLiteral":case "TSArrayType":case "TSTupleType":case "TSOptionalType":case "TSRestType":case "TSUnionType":case "TSIntersectionType":case "TSConditionalType":case "TSInferType":case "TSParenthesizedType":case "TSTypeOperator":case "TSIndexedAccessType":case "TSMappedType":case "TSTemplateLiteralType":case "TSLiteralType":case "TSExpressionWithTypeArguments":case "TSImportType":break;default:return  false}return t==null||(0, a.default)(e,t)}function Xn(e,t){if(!e)return  false;switch(e.type){case "TSAnyKeyword":case "TSBooleanKeyword":case "TSBigIntKeyword":case "TSIntrinsicKeyword":case "TSNeverKeyword":case "TSNullKeyword":case "TSNumberKeyword":case "TSObjectKeyword":case "TSStringKeyword":case "TSSymbolKeyword":case "TSUndefinedKeyword":case "TSUnknownKeyword":case "TSVoidKeyword":case "TSThisType":case "TSTemplateLiteralType":case "TSLiteralType":break;default:return  false}return t==null||(0, a.default)(e,t)}function Jn(e,t){return (0, n.default)("isNumberLiteral","isNumericLiteral"),!e||e.type!=="NumberLiteral"?false:t==null||(0, a.default)(e,t)}function jn(e,t){return (0, n.default)("isRegexLiteral","isRegExpLiteral"),!e||e.type!=="RegexLiteral"?false:t==null||(0, a.default)(e,t)}function Rn(e,t){return (0, n.default)("isRestProperty","isRestElement"),!e||e.type!=="RestProperty"?false:t==null||(0, a.default)(e,t)}function vn(e,t){return (0, n.default)("isSpreadProperty","isSpreadElement"),!e||e.type!=="SpreadProperty"?false:t==null||(0, a.default)(e,t)}function Vn(e,t){return (0, n.default)("isModuleDeclaration","isImportOrExportDeclaration"),i(e,t)}return e$P}

var p$2;function d$4(){if(p$2)return t$b;p$2=1,Object.defineProperty(t$b,"__esModule",{value:true}),t$b.default=h;var t=qn();function h(l,o,c){if(!(0, t.isMemberExpression)(l))return  false;const i=Array.isArray(o)?o:o.split("."),e=[];let r;for(r=l;(0, t.isMemberExpression)(r);r=r.object)e.push(r.property);if(e.push(r),e.length<i.length||!c&&e.length>i.length)return  false;for(let u=0,f=e.length-1;u<i.length;u++,f--){const s=e[f];let n;if((0, t.isIdentifier)(s))n=s.name;else if((0, t.isStringLiteral)(s))n=s.value;else if((0, t.isThisExpression)(s))n="this";else return  false;if(i[u]!==n)return  false}return  true}return t$b}

var r$u;function m$3(){if(r$u)return e$Q;r$u=1,Object.defineProperty(e$Q,"__esModule",{value:true}),e$Q.default=s;var t=d$4();function s(i,u){const o=i.split(".");return a=>(0, t.default)(a,o,u)}return e$Q}

var r$t;function u$a(){if(r$t)return e$R;r$t=1,Object.defineProperty(e$R,"__esModule",{value:true}),e$R.default=void 0;var t=m$3();const o=(0, t.default)("React.Component");e$R.default=o;return e$R}

var a$g={};

var t$a;function s$8(){if(t$a)return a$g;t$a=1,Object.defineProperty(a$g,"__esModule",{value:true}),a$g.default=o;function o(r){return !!r&&/^[a-z]/.test(r)}return a$g}

var r$s={};

var e$O={};

var e$N={};

var e$M={};

var a$f={};

var e$L={};

var r$r={};

var r$q={};

var e$K={};

var t$9;function a$e(){if(t$9)return e$K;t$9=1,Object.defineProperty(e$K,"__esModule",{value:true}),e$K.default=f;var s=I$1();function f(r,u){if(r===u)return  true;if(r==null||s.ALIAS_KEYS[u])return  false;const i=s.FLIPPED_ALIAS_KEYS[u];if(i){if(i[0]===r)return  true;for(const n of i)if(r===n)return  true}return  false}return e$K}

var e$J={};

var s$7;function n$8(){if(s$7)return e$J;s$7=1,Object.defineProperty(e$J,"__esModule",{value:true}),e$J.default=t;var u=I$1();function t(r,i){if(r===i)return  true;const o=u.PLACEHOLDERS_ALIAS[r];if(o){for(const a of o)if(i===a)return  true}return  false}return e$J}

var t$8;function c$7(){if(t$8)return r$q;t$8=1,Object.defineProperty(r$q,"__esModule",{value:true}),r$q.default=o;var a=a$h(),s=a$e(),f=n$8(),_=I$1();function o(u,e,i){return e?(0, s.default)(e.type,u)?i===void 0?true:(0, a.default)(e,i):!i&&e.type==="Placeholder"&&u in _.FLIPPED_ALIAS_KEYS?(0, f.default)(e.expectedNode,u):false:false}return r$q}

var e$I={};

var r$p={};

var e$H={};

var l$5;function o$m(){if(l$5)return e$H;l$5=1,Object.defineProperty(e$H,"__esModule",{value:true}),e$H.isIdentifierChar=s,e$H.isIdentifierName=h,e$H.isIdentifierStart=n;let c="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",t="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65";const x=new RegExp("["+c+"]"),I=new RegExp("["+c+t+"]");c=t=null;const i=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],C=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function r(u,e){let a=65536;for(let f=0,d=e.length;f<d;f+=2){if(a+=e[f],a>u)return  false;if(a+=e[f+1],a>=u)return  true}return  false}function n(u){return u<65?u===36:u<=90?true:u<97?u===95:u<=122?true:u<=65535?u>=170&&x.test(String.fromCharCode(u)):r(u,i)}function s(u){return u<48?u===36:u<58?true:u<65?false:u<=90?true:u<97?u===95:u<=122?true:u<=65535?u>=170&&I.test(String.fromCharCode(u)):r(u,i)||r(u,C)}function h(u){let e=true;for(let a=0;a<u.length;a++){let f=u.charCodeAt(a);if((f&64512)===55296&&a+1<u.length){const d=u.charCodeAt(++a);(d&64512)===56320&&(f=65536+((f&1023)<<10)+(d&1023));}if(e){if(e=false,!n(f))return  false}else if(!s(f))return  false}return !e}return e$H}

var r$o={};

var d$3;function v(){if(d$3)return r$o;d$3=1,Object.defineProperty(r$o,"__esModule",{value:true}),r$o.isKeyword=f,r$o.isReservedWord=s,r$o.isStrictBindOnlyReservedWord=o,r$o.isStrictBindReservedWord=l,r$o.isStrictReservedWord=n;const i={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},c=new Set(i.keyword),u=new Set(i.strict),a=new Set(i.strictBind);function s(e,t){return t&&e==="await"||e==="enum"}function n(e,t){return s(e,t)||u.has(e)}function o(e){return a.has(e)}function l(e,t){return n(e,t)||o(e)}function f(e){return c.has(e)}return r$o}

var n$7;function o$l(){return n$7?r$p:(n$7=1,function(e){Object.defineProperty(e,"__esModule",{value:true}),Object.defineProperty(e,"isIdentifierChar",{enumerable:true,get:function(){return t.isIdentifierChar}}),Object.defineProperty(e,"isIdentifierName",{enumerable:true,get:function(){return t.isIdentifierName}}),Object.defineProperty(e,"isIdentifierStart",{enumerable:true,get:function(){return t.isIdentifierStart}}),Object.defineProperty(e,"isKeyword",{enumerable:true,get:function(){return r.isKeyword}}),Object.defineProperty(e,"isReservedWord",{enumerable:true,get:function(){return r.isReservedWord}}),Object.defineProperty(e,"isStrictBindOnlyReservedWord",{enumerable:true,get:function(){return r.isStrictBindOnlyReservedWord}}),Object.defineProperty(e,"isStrictBindReservedWord",{enumerable:true,get:function(){return r.isStrictBindReservedWord}}),Object.defineProperty(e,"isStrictReservedWord",{enumerable:true,get:function(){return r.isStrictReservedWord}});var t=o$m(),r=v();}(r$p),r$p)}

var t$7;function d$2(){if(t$7)return e$I;t$7=1,Object.defineProperty(e$I,"__esModule",{value:true}),e$I.default=s;var i=o$l();function s(e,u=true){return typeof e!="string"||u&&((0, i.isKeyword)(e)||(0, i.isStrictReservedWord)(e,true))?false:(0, i.isIdentifierName)(e)}return e$I}

var r$n={};

var q$2;function B$2(){if(q$2)return r$n;q$2=1,Object.defineProperty(r$n,"__esModule",{value:true}),r$n.readCodePoint=N,r$n.readInt=I,r$n.readStringContents=k;var S=function(e){return e>=48&&e<=57};const x={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},A={bin:t=>t===48||t===49,oct:t=>t>=48&&t<=55,dec:t=>t>=48&&t<=57,hex:t=>t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102};function k(t,e,n,l,r,f){const s=n,c=l,d=r;let i="",h=null,a=n;const{length:m}=e;for(;;){if(n>=m){f.unterminated(s,c,d),i+=e.slice(a,n);break}const u=e.charCodeAt(n);if(E(t,u,e,n)){i+=e.slice(a,n);break}if(u===92){i+=e.slice(a,n);const b=D(e,n,l,r,t==="template",f);b.ch===null&&!h?h={pos:n,lineStart:l,curLine:r}:i+=b.ch,{pos:n,lineStart:l,curLine:r}=b,a=n;}else u===8232||u===8233?(++n,++r,l=n):u===10||u===13?t==="template"?(i+=e.slice(a,n)+`
`,++n,u===13&&e.charCodeAt(n)===10&&++n,++r,a=l=n):f.unterminated(s,c,d):++n;}return {pos:n,str:i,firstInvalidLoc:h,lineStart:l,curLine:r,containsInvalid:!!h}}function E(t,e,n,l){return t==="template"?e===96||e===36&&n.charCodeAt(l+1)===123:e===(t==="double"?34:39)}function D(t,e,n,l,r,f){const s=!r;e++;const c=i=>({pos:e,ch:i,lineStart:n,curLine:l}),d=t.charCodeAt(e++);switch(d){case 110:return c(`
`);case 114:return c("\r");case 120:{let i;return {code:i,pos:e}=v(t,e,n,l,2,false,s,f),c(i===null?null:String.fromCharCode(i))}case 117:{let i;return {code:i,pos:e}=N(t,e,n,l,s,f),c(i===null?null:String.fromCodePoint(i))}case 116:return c("	");case 98:return c("\b");case 118:return c("\v");case 102:return c("\f");case 13:t.charCodeAt(e)===10&&++e;case 10:n=e,++l;case 8232:case 8233:return c("");case 56:case 57:if(r)return c(null);f.strictNumericEscape(e-1,n,l);default:if(d>=48&&d<=55){const i=e-1;let a=/^[0-7]+/.exec(t.slice(i,e+2))[0],m=parseInt(a,8);m>255&&(a=a.slice(0,-1),m=parseInt(a,8)),e+=a.length-1;const u=t.charCodeAt(e);if(a!=="0"||u===56||u===57){if(r)return c(null);f.strictNumericEscape(i,n,l);}return c(String.fromCharCode(m))}return c(String.fromCharCode(d))}}function v(t,e,n,l,r,f,s,c){const d=e;let i;return {n:i,pos:e}=I(t,e,n,l,16,r,f,false,c,!s),i===null&&(s?c.invalidEscapeSequence(d,n,l):e=d-1),{code:i,pos:e}}function I(t,e,n,l,r,f,s,c,d,i){const h=e,a=r===16?x.hex:x.decBinOct,m=r===16?A.hex:r===10?A.dec:r===8?A.oct:A.bin;let u=false,b=0;for(let _=0,j=f??1/0;_<j;++_){const g=t.charCodeAt(e);let C;if(g===95&&c!=="bail"){const w=t.charCodeAt(e-1),P=t.charCodeAt(e+1);if(c){if(Number.isNaN(P)||!m(P)||a.has(w)||a.has(P)){if(i)return {n:null,pos:e};d.unexpectedNumericSeparator(e,n,l);}}else {if(i)return {n:null,pos:e};d.numericSeparatorInEscapeSequence(e,n,l);}++e;continue}if(g>=97?C=g-97+10:g>=65?C=g-65+10:S(g)?C=g-48:C=1/0,C>=r){if(C<=9&&i)return {n:null,pos:e};if(C<=9&&d.invalidDigit(e,n,l,r))C=0;else if(s)C=0,u=true;else break}++e,b=b*r+C;}return e===h||f!=null&&e-h!==f||u?{n:null,pos:e}:{n:b,pos:e}}function N(t,e,n,l,r,f){const s=t.charCodeAt(e);let c;if(s===123){if(++e,{code:c,pos:e}=v(t,e,n,l,t.indexOf("}",e)-e,true,r,f),++e,c!==null&&c>1114111)if(r)f.invalidCodePoint(e,n,l);else return {code:null,pos:e}}else ({code:c,pos:e}=v(t,e,n,l,4,false,r,f));return {code:c,pos:e}}return r$n}

var s$6={};

var A;function B$1(){if(A)return s$6;A=1,Object.defineProperty(s$6,"__esModule",{value:true}),s$6.UPDATE_OPERATORS=s$6.UNARY_OPERATORS=s$6.STRING_UNARY_OPERATORS=s$6.STATEMENT_OR_BLOCK_KEYS=s$6.NUMBER_UNARY_OPERATORS=s$6.NUMBER_BINARY_OPERATORS=s$6.NOT_LOCAL_BINDING=s$6.LOGICAL_OPERATORS=s$6.INHERIT_KEYS=s$6.FOR_INIT_KEYS=s$6.FLATTENABLE_KEYS=s$6.EQUALITY_BINARY_OPERATORS=s$6.COMPARISON_BINARY_OPERATORS=s$6.COMMENT_KEYS=s$6.BOOLEAN_UNARY_OPERATORS=s$6.BOOLEAN_NUMBER_BINARY_OPERATORS=s$6.BOOLEAN_BINARY_OPERATORS=s$6.BLOCK_SCOPED_SYMBOL=s$6.BINARY_OPERATORS=s$6.ASSIGNMENT_OPERATORS=void 0;s$6.STATEMENT_OR_BLOCK_KEYS=["consequent","body","alternate"];s$6.FLATTENABLE_KEYS=["body","expressions"];s$6.FOR_INIT_KEYS=["left","init"];s$6.COMMENT_KEYS=["leadingComments","trailingComments","innerComments"];const E=s$6.LOGICAL_OPERATORS=["||","&&","??"];s$6.UPDATE_OPERATORS=["++","--"];const N=s$6.BOOLEAN_NUMBER_BINARY_OPERATORS=[">","<",">=","<="],T=s$6.EQUALITY_BINARY_OPERATORS=["==","===","!=","!=="],S=s$6.COMPARISON_BINARY_OPERATORS=[...T,"in","instanceof"],o=s$6.BOOLEAN_BINARY_OPERATORS=[...S,...N],_=s$6.NUMBER_BINARY_OPERATORS=["-","/","%","*","**","&","|",">>",">>>","<<","^"];s$6.BINARY_OPERATORS=["+",..._,...o,"|>"];s$6.ASSIGNMENT_OPERATORS=["=","+=",..._.map(R=>R+"="),...E.map(R=>R+"=")];const t=s$6.BOOLEAN_UNARY_OPERATORS=["delete","!"],n=s$6.NUMBER_UNARY_OPERATORS=["+","-","~"],e=s$6.STRING_UNARY_OPERATORS=["typeof"];s$6.UNARY_OPERATORS=["void","throw",...t,...n,...e];s$6.INHERIT_KEYS={optional:["typeAnnotation","typeParameters","returnType"],force:["start","loc","end"]};s$6.BLOCK_SCOPED_SYMBOL=Symbol.for("var used to be block scoped");s$6.NOT_LOCAL_BINDING=Symbol.for("should not be considered a local binding");return s$6}

var r$m={};

var N$3;function J(){if(N$3)return r$m;N$3=1,Object.defineProperty(r$m,"__esModule",{value:true}),r$m.VISITOR_KEYS=r$m.NODE_PARENT_VALIDATIONS=r$m.NODE_FIELDS=r$m.FLIPPED_ALIAS_KEYS=r$m.DEPRECATED_KEYS=r$m.BUILDER_KEYS=r$m.ALIAS_KEYS=void 0,r$m.arrayOf=A,r$m.arrayOfType=T,r$m.assertEach=_,r$m.assertNodeOrValueType=k,r$m.assertNodeType=p,r$m.assertOneOf=V,r$m.assertOptionalChainStart=j,r$m.assertShape=x,r$m.assertValueType=h,r$m.chain=w,r$m.default=I,r$m.defineAliasedType=q,r$m.validate=O,r$m.validateArrayOfType=R,r$m.validateOptional=K,r$m.validateOptionalType=Y,r$m.validateType=g;var S=c$7(),c=N$1();const $=r$m.VISITOR_KEYS={},b=r$m.ALIAS_KEYS={},E=r$m.FLIPPED_ALIAS_KEYS={},D=r$m.NODE_FIELDS={},P=r$m.BUILDER_KEYS={},m=r$m.DEPRECATED_KEYS={},L=r$m.NODE_PARENT_VALIDATIONS={};function y(e){return Array.isArray(e)?"array":e===null?"null":typeof e}function O(e){return {validate:e}}function g(...e){return O(p(...e))}function K(e){return {validate:e,optional:true}}function Y(...e){return {validate:p(...e),optional:true}}function A(e){return w(h("array"),_(e))}function T(...e){return A(p(...e))}function R(...e){return O(T(...e))}function _(e){const r=process.env.BABEL_TYPES_8_BREAKING?c.validateChild:()=>{};function n(t,a,l){if(Array.isArray(l))for(let s=0;s<l.length;s++){const o=`${a}[${s}]`,f=l[s];e(t,o,f),r(t,o,f);}}return n.each=e,n}function V(...e){function r(n,t,a){if(!e.includes(a))throw new TypeError(`Property ${t} expected value to be one of ${JSON.stringify(e)} but got ${JSON.stringify(a)}`)}return r.oneOf=e,r}function p(...e){function r(n,t,a){for(const l of e)if((0, S.default)(l,a)){(0, c.validateChild)(n,t,a);return}throw new TypeError(`Property ${t} of ${n.type} expected node to be of a type ${JSON.stringify(e)} but instead got ${JSON.stringify(a?.type)}`)}return r.oneOfNodeTypes=e,r}function k(...e){function r(n,t,a){for(const l of e)if(y(a)===l||(0, S.default)(l,a)){(0, c.validateChild)(n,t,a);return}throw new TypeError(`Property ${t} of ${n.type} expected node to be of a type ${JSON.stringify(e)} but instead got ${JSON.stringify(a?.type)}`)}return r.oneOfNodeOrValueTypes=e,r}function h(e){function r(n,t,a){if(!(y(a)===e))throw new TypeError(`Property ${t} expected type of ${e} but got ${y(a)}`)}return r.type=e,r}function x(e){function r(n,t,a){const l=[];for(const s of Object.keys(e))try{(0,c.validateField)(n,s,a[s],e[s]);}catch(o){if(o instanceof TypeError){l.push(o.message);continue}throw o}if(l.length)throw new TypeError(`Property ${t} of ${n.type} expected to have the following:
${l.join(`
`)}`)}return r.shapeOf=e,r}function j(){function e(r){var n;let t=r;for(;r;){const{type:a}=t;if(a==="OptionalCallExpression"){if(t.optional)return;t=t.callee;continue}if(a==="OptionalMemberExpression"){if(t.optional)return;t=t.object;continue}break}throw new TypeError(`Non-optional ${r.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${(n=t)==null?void 0:n.type}`)}return e}function w(...e){function r(...n){for(const t of e)t(...n);}if(r.chainOf=e,e.length>=2&&"type"in e[0]&&e[0].type==="array"&&!("each"in e[1]))throw new Error('An assertValueType("array") validator can only be followed by an assertEach(...) validator.');return r}const C=new Set(["aliases","builder","deprecatedAlias","fields","inherits","visitor","validate"]),F=new Set(["default","optional","deprecated","validate"]),v={};function q(...e){return (r,n={})=>{let t=n.aliases;if(!t){var a;n.inherits&&(t=(a=v[n.inherits].aliases)==null?void 0:a.slice()),(t)!=null||(t=[]),n.aliases=t;}const s=e.filter(o=>!t.includes(o));t.unshift(...s),I(r,n);}}function I(e,r={}){const n=r.inherits&&v[r.inherits]||{};let t=r.fields;if(!t&&(t={},n.fields)){const o=Object.getOwnPropertyNames(n.fields);for(const f of o){const u=n.fields[f],d=u.default;if(Array.isArray(d)?d.length>0:d&&typeof d=="object")throw new Error("field defaults can only be primitives or empty arrays currently");t[f]={default:Array.isArray(d)?[]:d,optional:u.optional,deprecated:u.deprecated,validate:u.validate};}}const a=r.visitor||n.visitor||[],l=r.aliases||n.aliases||[],s=r.builder||n.builder||r.visitor||[];for(const o of Object.keys(r))if(!C.has(o))throw new Error(`Unknown type option "${o}" on ${e}`);r.deprecatedAlias&&(m[r.deprecatedAlias]=e);for(const o of a.concat(s))t[o]=t[o]||{};for(const o of Object.keys(t)){const f=t[o];f.default!==void 0&&!s.includes(o)&&(f.optional=true),f.default===void 0?f.default=null:!f.validate&&f.default!=null&&(f.validate=h(y(f.default)));for(const u of Object.keys(f))if(!F.has(u))throw new Error(`Unknown field key "${u}" on ${e}.${o}`)}$[e]=r.visitor=a,P[e]=r.builder=s,D[e]=r.fields=t,b[e]=r.aliases=l,l.forEach(o=>{E[o]=E[o]||[],E[o].push(e);}),r.validate&&(L[e]=r.validate),v[e]=r;}return r$m}

var N$2;function B(){if(N$2)return r$r;N$2=1,Object.defineProperty(r$r,"__esModule",{value:true}),r$r.patternLikeCommon=r$r.importAttributes=r$r.functionTypeAnnotationCommon=r$r.functionDeclarationCommon=r$r.functionCommon=r$r.classMethodOrPropertyCommon=r$r.classMethodOrDeclareMethodCommon=void 0;var p=c$7(),S=d$2(),E=o$l(),x=B$2(),f=B$1(),e=J();const t=(0, e.defineAliasedType)("Standardized");t("ArrayExpression",{fields:{elements:{validate:(0, e.arrayOf)((0, e.assertNodeOrValueType)("null","Expression","SpreadElement")),default:process.env.BABEL_TYPES_8_BREAKING?void 0:[]}},visitor:["elements"],aliases:["Expression"]}),t("AssignmentExpression",{fields:{operator:{validate:process.env.BABEL_TYPES_8_BREAKING?Object.assign(function(){const r=(0, e.assertOneOf)(...f.ASSIGNMENT_OPERATORS),s=(0, e.assertOneOf)("=");return function(a,i,o){((0, p.default)("Pattern",a.left)?s:r)(a,i,o);}}(),{type:"string"}):(0, e.assertValueType)("string")},left:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertNodeType)("Identifier","MemberExpression","OptionalMemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0, e.assertNodeType)("LVal","OptionalMemberExpression")},right:{validate:(0, e.assertNodeType)("Expression")}},builder:["operator","left","right"],visitor:["left","right"],aliases:["Expression"]}),t("BinaryExpression",{builder:["operator","left","right"],fields:{operator:{validate:(0, e.assertOneOf)(...f.BINARY_OPERATORS)},left:{validate:function(){const r=(0, e.assertNodeType)("Expression"),s=(0, e.assertNodeType)("Expression","PrivateName");return Object.assign(function(i,o,n){(i.operator==="in"?s:r)(i,o,n);},{oneOfNodeTypes:["Expression","PrivateName"]})}()},right:{validate:(0, e.assertNodeType)("Expression")}},visitor:["left","right"],aliases:["Binary","Expression"]}),t("InterpreterDirective",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}}}),t("Directive",{visitor:["value"],fields:{value:{validate:(0, e.assertNodeType)("DirectiveLiteral")}}}),t("DirectiveLiteral",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}}}),t("BlockStatement",{builder:["body","directives"],visitor:["directives","body"],fields:{directives:{validate:(0, e.arrayOfType)("Directive"),default:[]},body:(0, e.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","Block","Statement"]}),t("BreakStatement",{visitor:["label"],fields:{label:{validate:(0, e.assertNodeType)("Identifier"),optional:true}},aliases:["Statement","Terminatorless","CompletionStatement"]}),t("CallExpression",{visitor:["callee","arguments","typeParameters","typeArguments"],builder:["callee","arguments"],aliases:["Expression"],fields:Object.assign({callee:{validate:(0, e.assertNodeType)("Expression","Super","V8IntrinsicIdentifier")},arguments:(0, e.validateArrayOfType)("Expression","SpreadElement","ArgumentPlaceholder"),typeArguments:{validate:(0, e.assertNodeType)("TypeParameterInstantiation"),optional:true}},{optional:{validate:(0, e.assertValueType)("boolean"),optional:true},typeParameters:{validate:(0, e.assertNodeType)("TSTypeParameterInstantiation"),optional:true}},process.env.BABEL_TYPES_8_BREAKING?{}:{optional:{validate:(0, e.assertValueType)("boolean"),optional:true}})}),t("CatchClause",{visitor:["param","body"],fields:{param:{validate:(0, e.assertNodeType)("Identifier","ArrayPattern","ObjectPattern"),optional:true},body:{validate:(0, e.assertNodeType)("BlockStatement")}},aliases:["Scopable","BlockParent"]}),t("ConditionalExpression",{visitor:["test","consequent","alternate"],fields:{test:{validate:(0, e.assertNodeType)("Expression")},consequent:{validate:(0, e.assertNodeType)("Expression")},alternate:{validate:(0, e.assertNodeType)("Expression")}},aliases:["Expression","Conditional"]}),t("ContinueStatement",{visitor:["label"],fields:{label:{validate:(0, e.assertNodeType)("Identifier"),optional:true}},aliases:["Statement","Terminatorless","CompletionStatement"]}),t("DebuggerStatement",{aliases:["Statement"]}),t("DoWhileStatement",{builder:["test","body"],visitor:["body","test"],fields:{test:{validate:(0, e.assertNodeType)("Expression")},body:{validate:(0, e.assertNodeType)("Statement")}},aliases:["Statement","BlockParent","Loop","While","Scopable"]}),t("EmptyStatement",{aliases:["Statement"]}),t("ExpressionStatement",{visitor:["expression"],fields:{expression:{validate:(0, e.assertNodeType)("Expression")}},aliases:["Statement","ExpressionWrapper"]}),t("File",{builder:["program","comments","tokens"],visitor:["program"],fields:{program:{validate:(0, e.assertNodeType)("Program")},comments:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertEach)((0, e.assertNodeType)("CommentBlock","CommentLine")):Object.assign(()=>{},{each:{oneOfNodeTypes:["CommentBlock","CommentLine"]}}),optional:true},tokens:{validate:(0, e.assertEach)(Object.assign(()=>{},{type:"any"})),optional:true}}}),t("ForInStatement",{visitor:["left","right","body"],aliases:["Scopable","Statement","For","BlockParent","Loop","ForXStatement"],fields:{left:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertNodeType)("VariableDeclaration","Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0, e.assertNodeType)("VariableDeclaration","LVal")},right:{validate:(0, e.assertNodeType)("Expression")},body:{validate:(0, e.assertNodeType)("Statement")}}}),t("ForStatement",{visitor:["init","test","update","body"],aliases:["Scopable","Statement","For","BlockParent","Loop"],fields:{init:{validate:(0, e.assertNodeType)("VariableDeclaration","Expression"),optional:true},test:{validate:(0, e.assertNodeType)("Expression"),optional:true},update:{validate:(0, e.assertNodeType)("Expression"),optional:true},body:{validate:(0, e.assertNodeType)("Statement")}}});const c=()=>({params:(0, e.validateArrayOfType)("Identifier","Pattern","RestElement"),generator:{default:false},async:{default:false}});r$r.functionCommon=c;const d=()=>({returnType:{validate:(0, e.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:true},typeParameters:{validate:(0, e.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:true}});r$r.functionTypeAnnotationCommon=d;const b=()=>Object.assign({},c(),{declare:{validate:(0, e.assertValueType)("boolean"),optional:true},id:{validate:(0, e.assertNodeType)("Identifier"),optional:true}});r$r.functionDeclarationCommon=b,t("FunctionDeclaration",{builder:["id","params","body","generator","async"],visitor:["id","typeParameters","params","predicate","returnType","body"],fields:Object.assign({},b(),d(),{body:{validate:(0, e.assertNodeType)("BlockStatement")},predicate:{validate:(0, e.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:true}}),aliases:["Scopable","Function","BlockParent","FunctionParent","Statement","Pureish","Declaration"],validate:process.env.BABEL_TYPES_8_BREAKING?function(){const r=(0, e.assertNodeType)("Identifier");return function(s,a,i){(0, p.default)("ExportDefaultDeclaration",s)||r(i,"id",i.id);}}():void 0}),t("FunctionExpression",{inherits:"FunctionDeclaration",aliases:["Scopable","Function","BlockParent","FunctionParent","Expression","Pureish"],fields:Object.assign({},c(),d(),{id:{validate:(0, e.assertNodeType)("Identifier"),optional:true},body:{validate:(0, e.assertNodeType)("BlockStatement")},predicate:{validate:(0, e.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:true}})});const u=()=>({typeAnnotation:{validate:(0, e.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:true},optional:{validate:(0, e.assertValueType)("boolean"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true}});r$r.patternLikeCommon=u,t("Identifier",{builder:["name"],visitor:["typeAnnotation","decorators"],aliases:["Expression","PatternLike","LVal","TSEntityName"],fields:Object.assign({},u(),{name:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("string"),Object.assign(function(r,s,a){if(!(0, S.default)(a,false))throw new TypeError(`"${a}" is not a valid identifier name`)},{type:"string"})):(0, e.assertValueType)("string")}}),validate:process.env.BABEL_TYPES_8_BREAKING?function(r,s,a){const i=/\.(\w+)$/.exec(s);if(!i)return;const[,o]=i,n={computed:false};if(o==="property"){if((0, p.default)("MemberExpression",r,n)||(0, p.default)("OptionalMemberExpression",r,n))return}else if(o==="key"){if((0, p.default)("Property",r,n)||(0, p.default)("Method",r,n))return}else if(o==="exported"){if((0, p.default)("ExportSpecifier",r))return}else if(o==="imported"){if((0, p.default)("ImportSpecifier",r,{imported:a}))return}else if(o==="meta"&&(0, p.default)("MetaProperty",r,{meta:a}))return;if(((0, E.isKeyword)(a.name)||(0, E.isReservedWord)(a.name,false))&&a.name!=="this")throw new TypeError(`"${a.name}" is not a valid identifier`)}:void 0}),t("IfStatement",{visitor:["test","consequent","alternate"],aliases:["Statement","Conditional"],fields:{test:{validate:(0, e.assertNodeType)("Expression")},consequent:{validate:(0, e.assertNodeType)("Statement")},alternate:{optional:true,validate:(0, e.assertNodeType)("Statement")}}}),t("LabeledStatement",{visitor:["label","body"],aliases:["Statement"],fields:{label:{validate:(0, e.assertNodeType)("Identifier")},body:{validate:(0, e.assertNodeType)("Statement")}}}),t("StringLiteral",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]}),t("NumericLiteral",{builder:["value"],deprecatedAlias:"NumberLiteral",fields:{value:{validate:(0, e.chain)((0, e.assertValueType)("number"),Object.assign(function(r,s,a){},{type:"number"}))}},aliases:["Expression","Pureish","Literal","Immutable"]}),t("NullLiteral",{aliases:["Expression","Pureish","Literal","Immutable"]}),t("BooleanLiteral",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("boolean")}},aliases:["Expression","Pureish","Literal","Immutable"]}),t("RegExpLiteral",{builder:["pattern","flags"],deprecatedAlias:"RegexLiteral",aliases:["Expression","Pureish","Literal"],fields:{pattern:{validate:(0, e.assertValueType)("string")},flags:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("string"),Object.assign(function(r,s,a){const i=/[^gimsuy]/.exec(a);if(i)throw new TypeError(`"${i[0]}" is not a valid RegExp flag`)},{type:"string"})):(0, e.assertValueType)("string"),default:""}}}),t("LogicalExpression",{builder:["operator","left","right"],visitor:["left","right"],aliases:["Binary","Expression"],fields:{operator:{validate:(0, e.assertOneOf)(...f.LOGICAL_OPERATORS)},left:{validate:(0, e.assertNodeType)("Expression")},right:{validate:(0, e.assertNodeType)("Expression")}}}),t("MemberExpression",{builder:["object","property","computed",...process.env.BABEL_TYPES_8_BREAKING?[]:["optional"]],visitor:["object","property"],aliases:["Expression","LVal"],fields:Object.assign({object:{validate:(0, e.assertNodeType)("Expression","Super")},property:{validate:function(){const r=(0, e.assertNodeType)("Identifier","PrivateName"),s=(0, e.assertNodeType)("Expression"),a=function(i,o,n){(i.computed?s:r)(i,o,n);};return a.oneOfNodeTypes=["Expression","Identifier","PrivateName"],a}()},computed:{default:false}},process.env.BABEL_TYPES_8_BREAKING?{}:{optional:{validate:(0, e.assertValueType)("boolean"),optional:true}})}),t("NewExpression",{inherits:"CallExpression"}),t("Program",{visitor:["directives","body"],builder:["body","directives","sourceType","interpreter"],fields:{sourceType:{validate:(0, e.assertOneOf)("script","module"),default:"script"},interpreter:{validate:(0, e.assertNodeType)("InterpreterDirective"),default:null,optional:true},directives:{validate:(0, e.arrayOfType)("Directive"),default:[]},body:(0, e.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","Block"]}),t("ObjectExpression",{visitor:["properties"],aliases:["Expression"],fields:{properties:(0, e.validateArrayOfType)("ObjectMethod","ObjectProperty","SpreadElement")}}),t("ObjectMethod",{builder:["kind","key","params","body","computed","generator","async"],visitor:["decorators","key","typeParameters","params","returnType","body"],fields:Object.assign({},c(),d(),{kind:Object.assign({validate:(0, e.assertOneOf)("method","get","set")},process.env.BABEL_TYPES_8_BREAKING?{}:{default:"method"}),computed:{default:false},key:{validate:function(){const r=(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral"),s=(0, e.assertNodeType)("Expression"),a=function(i,o,n){(i.computed?s:r)(i,o,n);};return a.oneOfNodeTypes=["Expression","Identifier","StringLiteral","NumericLiteral","BigIntLiteral"],a}()},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},body:{validate:(0, e.assertNodeType)("BlockStatement")}}),aliases:["UserWhitespacable","Function","Scopable","BlockParent","FunctionParent","Method","ObjectMember"]}),t("ObjectProperty",{builder:["key","value","computed","shorthand",...process.env.BABEL_TYPES_8_BREAKING?[]:["decorators"]],fields:{computed:{default:false},key:{validate:function(){const r=(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","DecimalLiteral","PrivateName"),s=(0, e.assertNodeType)("Expression");return Object.assign(function(i,o,n){(i.computed?s:r)(i,o,n);},{oneOfNodeTypes:["Expression","Identifier","StringLiteral","NumericLiteral","BigIntLiteral","DecimalLiteral","PrivateName"]})}()},value:{validate:(0, e.assertNodeType)("Expression","PatternLike")},shorthand:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("boolean"),Object.assign(function(r,s,a){if(a){if(r.computed)throw new TypeError("Property shorthand of ObjectProperty cannot be true if computed is true");if(!(0, p.default)("Identifier",r.key))throw new TypeError("Property shorthand of ObjectProperty cannot be true if key is not an Identifier")}},{type:"boolean"})):(0, e.assertValueType)("boolean"),default:false},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true}},visitor:["key","value","decorators"],aliases:["UserWhitespacable","Property","ObjectMember"],validate:process.env.BABEL_TYPES_8_BREAKING?function(){const r=(0, e.assertNodeType)("Identifier","Pattern","TSAsExpression","TSSatisfiesExpression","TSNonNullExpression","TSTypeAssertion"),s=(0, e.assertNodeType)("Expression");return function(a,i,o){((0, p.default)("ObjectPattern",a)?r:s)(o,"value",o.value);}}():void 0}),t("RestElement",{visitor:["argument","typeAnnotation"],builder:["argument"],aliases:["LVal","PatternLike"],deprecatedAlias:"RestProperty",fields:Object.assign({},u(),{argument:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertNodeType)("Identifier","ArrayPattern","ObjectPattern","MemberExpression","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0, e.assertNodeType)("LVal")}}),validate:process.env.BABEL_TYPES_8_BREAKING?function(r,s){const a=/(\w+)\[(\d+)\]/.exec(s);if(!a)throw new Error("Internal Babel error: malformed key.");const[,i,o]=a;if(r[i].length>+o+1)throw new TypeError(`RestElement must be last element of ${i}`)}:void 0}),t("ReturnStatement",{visitor:["argument"],aliases:["Statement","Terminatorless","CompletionStatement"],fields:{argument:{validate:(0, e.assertNodeType)("Expression"),optional:true}}}),t("SequenceExpression",{visitor:["expressions"],fields:{expressions:(0, e.validateArrayOfType)("Expression")},aliases:["Expression"]}),t("ParenthesizedExpression",{visitor:["expression"],aliases:["Expression","ExpressionWrapper"],fields:{expression:{validate:(0, e.assertNodeType)("Expression")}}}),t("SwitchCase",{visitor:["test","consequent"],fields:{test:{validate:(0, e.assertNodeType)("Expression"),optional:true},consequent:(0, e.validateArrayOfType)("Statement")}}),t("SwitchStatement",{visitor:["discriminant","cases"],aliases:["Statement","BlockParent","Scopable"],fields:{discriminant:{validate:(0, e.assertNodeType)("Expression")},cases:(0, e.validateArrayOfType)("SwitchCase")}}),t("ThisExpression",{aliases:["Expression"]}),t("ThrowStatement",{visitor:["argument"],aliases:["Statement","Terminatorless","CompletionStatement"],fields:{argument:{validate:(0, e.assertNodeType)("Expression")}}}),t("TryStatement",{visitor:["block","handler","finalizer"],aliases:["Statement"],fields:{block:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertNodeType)("BlockStatement"),Object.assign(function(r){if(!r.handler&&!r.finalizer)throw new TypeError("TryStatement expects either a handler or finalizer, or both")},{oneOfNodeTypes:["BlockStatement"]})):(0, e.assertNodeType)("BlockStatement")},handler:{optional:true,validate:(0, e.assertNodeType)("CatchClause")},finalizer:{optional:true,validate:(0, e.assertNodeType)("BlockStatement")}}}),t("UnaryExpression",{builder:["operator","argument","prefix"],fields:{prefix:{default:true},argument:{validate:(0, e.assertNodeType)("Expression")},operator:{validate:(0, e.assertOneOf)(...f.UNARY_OPERATORS)}},visitor:["argument"],aliases:["UnaryLike","Expression"]}),t("UpdateExpression",{builder:["operator","argument","prefix"],fields:{prefix:{default:false},argument:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertNodeType)("Identifier","MemberExpression"):(0, e.assertNodeType)("Expression")},operator:{validate:(0, e.assertOneOf)(...f.UPDATE_OPERATORS)}},visitor:["argument"],aliases:["Expression"]}),t("VariableDeclaration",{builder:["kind","declarations"],visitor:["declarations"],aliases:["Statement","Declaration"],fields:{declare:{validate:(0, e.assertValueType)("boolean"),optional:true},kind:{validate:(0, e.assertOneOf)("var","let","const","using","await using")},declarations:(0, e.validateArrayOfType)("VariableDeclarator")},validate:process.env.BABEL_TYPES_8_BREAKING?(()=>{const r=(0, e.assertNodeType)("Identifier");return function(s,a,i){if((0, p.default)("ForXStatement",s,{left:i})){if(i.declarations.length!==1)throw new TypeError(`Exactly one VariableDeclarator is required in the VariableDeclaration of a ${s.type}`)}else i.declarations.forEach(o=>{o.init||r(o,"id",o.id);});}})():void 0}),t("VariableDeclarator",{visitor:["id","init"],fields:{id:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.assertNodeType)("Identifier","ArrayPattern","ObjectPattern"):(0, e.assertNodeType)("LVal")},definite:{optional:true,validate:(0, e.assertValueType)("boolean")},init:{optional:true,validate:(0, e.assertNodeType)("Expression")}}}),t("WhileStatement",{visitor:["test","body"],aliases:["Statement","BlockParent","Loop","While","Scopable"],fields:{test:{validate:(0, e.assertNodeType)("Expression")},body:{validate:(0, e.assertNodeType)("Statement")}}}),t("WithStatement",{visitor:["object","body"],aliases:["Statement"],fields:{object:{validate:(0, e.assertNodeType)("Expression")},body:{validate:(0, e.assertNodeType)("Statement")}}}),t("AssignmentPattern",{visitor:["left","right","decorators"],builder:["left","right"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},u(),{left:{validate:(0, e.assertNodeType)("Identifier","ObjectPattern","ArrayPattern","MemberExpression","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression")},right:{validate:(0, e.assertNodeType)("Expression")},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true}})}),t("ArrayPattern",{visitor:["elements","typeAnnotation"],builder:["elements"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},u(),{elements:{validate:(0, e.chain)((0, e.assertValueType)("array"),(0, e.assertEach)((0, e.assertNodeOrValueType)("null","PatternLike","LVal")))}})}),t("ArrowFunctionExpression",{builder:["params","body","async"],visitor:["typeParameters","params","predicate","returnType","body"],aliases:["Scopable","Function","BlockParent","FunctionParent","Expression","Pureish"],fields:Object.assign({},c(),d(),{expression:{validate:(0, e.assertValueType)("boolean")},body:{validate:(0, e.assertNodeType)("BlockStatement","Expression")},predicate:{validate:(0, e.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:true}})}),t("ClassBody",{visitor:["body"],fields:{body:(0, e.validateArrayOfType)("ClassMethod","ClassPrivateMethod","ClassProperty","ClassPrivateProperty","ClassAccessorProperty","TSDeclareMethod","TSIndexSignature","StaticBlock")}}),t("ClassExpression",{builder:["id","superClass","body","decorators"],visitor:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body"],aliases:["Scopable","Class","Expression"],fields:{id:{validate:(0, e.assertNodeType)("Identifier"),optional:true},typeParameters:{validate:(0, e.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:true},body:{validate:(0, e.assertNodeType)("ClassBody")},superClass:{optional:true,validate:(0, e.assertNodeType)("Expression")},superTypeParameters:{validate:(0, e.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:true},implements:{validate:(0, e.arrayOfType)("TSExpressionWithTypeArguments","ClassImplements"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},mixins:{validate:(0, e.assertNodeType)("InterfaceExtends"),optional:true}}}),t("ClassDeclaration",{inherits:"ClassExpression",aliases:["Scopable","Class","Statement","Declaration"],fields:{id:{validate:(0, e.assertNodeType)("Identifier"),optional:true},typeParameters:{validate:(0, e.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:true},body:{validate:(0, e.assertNodeType)("ClassBody")},superClass:{optional:true,validate:(0, e.assertNodeType)("Expression")},superTypeParameters:{validate:(0, e.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:true},implements:{validate:(0, e.arrayOfType)("TSExpressionWithTypeArguments","ClassImplements"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},mixins:{validate:(0, e.assertNodeType)("InterfaceExtends"),optional:true},declare:{validate:(0, e.assertValueType)("boolean"),optional:true},abstract:{validate:(0, e.assertValueType)("boolean"),optional:true}},validate:process.env.BABEL_TYPES_8_BREAKING?function(){const r=(0, e.assertNodeType)("Identifier");return function(s,a,i){(0, p.default)("ExportDefaultDeclaration",s)||r(i,"id",i.id);}}():void 0});const m=r$r.importAttributes={attributes:{optional:true,validate:(0, e.arrayOfType)("ImportAttribute")},assertions:{deprecated:true,optional:true,validate:(0, e.arrayOfType)("ImportAttribute")}};t("ExportAllDeclaration",{builder:["source"],visitor:["source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:Object.assign({source:{validate:(0, e.assertNodeType)("StringLiteral")},exportKind:(0, e.validateOptional)((0, e.assertOneOf)("type","value"))},m)}),t("ExportDefaultDeclaration",{visitor:["declaration"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:{declaration:(0, e.validateType)("TSDeclareFunction","FunctionDeclaration","ClassDeclaration","Expression"),exportKind:(0, e.validateOptional)((0, e.assertOneOf)("value"))}}),t("ExportNamedDeclaration",{builder:["declaration","specifiers","source"],visitor:process.env?["declaration","specifiers","source","attributes"]:["declaration","specifiers","source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:Object.assign({declaration:{optional:true,validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertNodeType)("Declaration"),Object.assign(function(r,s,a){if(a&&r.specifiers.length)throw new TypeError("Only declaration or specifiers is allowed on ExportNamedDeclaration");if(a&&r.source)throw new TypeError("Cannot export a declaration from a source")},{oneOfNodeTypes:["Declaration"]})):(0, e.assertNodeType)("Declaration")}},m,{specifiers:{default:[],validate:(0, e.arrayOf)(function(){const r=(0, e.assertNodeType)("ExportSpecifier","ExportDefaultSpecifier","ExportNamespaceSpecifier"),s=(0, e.assertNodeType)("ExportSpecifier");return process.env.BABEL_TYPES_8_BREAKING?Object.assign(function(a,i,o){(a.source?r:s)(a,i,o);},{oneOfNodeTypes:["ExportSpecifier","ExportDefaultSpecifier","ExportNamespaceSpecifier"]}):r}())},source:{validate:(0, e.assertNodeType)("StringLiteral"),optional:true},exportKind:(0, e.validateOptional)((0, e.assertOneOf)("type","value"))})}),t("ExportSpecifier",{visitor:["local","exported"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0, e.assertNodeType)("Identifier")},exported:{validate:(0, e.assertNodeType)("Identifier","StringLiteral")},exportKind:{validate:(0, e.assertOneOf)("type","value"),optional:true}}}),t("ForOfStatement",{visitor:["left","right","body"],builder:["left","right","body","await"],aliases:["Scopable","Statement","For","BlockParent","Loop","ForXStatement"],fields:{left:{validate:function(){if(!process.env.BABEL_TYPES_8_BREAKING)return (0, e.assertNodeType)("VariableDeclaration","LVal");const r=(0, e.assertNodeType)("VariableDeclaration"),s=(0, e.assertNodeType)("Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression");return Object.assign(function(a,i,o){(0, p.default)("VariableDeclaration",o)?r(a,i,o):s(a,i,o);},{oneOfNodeTypes:["VariableDeclaration","Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"]})}()},right:{validate:(0, e.assertNodeType)("Expression")},body:{validate:(0, e.assertNodeType)("Statement")},await:{default:false}}}),t("ImportDeclaration",{builder:["specifiers","source"],visitor:["specifiers","source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration"],fields:Object.assign({},m,{module:{optional:true,validate:(0, e.assertValueType)("boolean")},phase:{default:null,validate:(0, e.assertOneOf)("source","defer")},specifiers:(0, e.validateArrayOfType)("ImportSpecifier","ImportDefaultSpecifier","ImportNamespaceSpecifier"),source:{validate:(0, e.assertNodeType)("StringLiteral")},importKind:{validate:(0, e.assertOneOf)("type","typeof","value"),optional:true}})}),t("ImportDefaultSpecifier",{visitor:["local"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0, e.assertNodeType)("Identifier")}}}),t("ImportNamespaceSpecifier",{visitor:["local"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0, e.assertNodeType)("Identifier")}}}),t("ImportSpecifier",{visitor:["imported","local"],builder:["local","imported"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0, e.assertNodeType)("Identifier")},imported:{validate:(0, e.assertNodeType)("Identifier","StringLiteral")},importKind:{validate:(0, e.assertOneOf)("type","typeof","value"),optional:true}}}),t("ImportExpression",{visitor:["source","options"],aliases:["Expression"],fields:{phase:{default:null,validate:(0, e.assertOneOf)("source","defer")},source:{validate:(0, e.assertNodeType)("Expression")},options:{validate:(0, e.assertNodeType)("Expression"),optional:true}}}),t("MetaProperty",{visitor:["meta","property"],aliases:["Expression"],fields:{meta:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertNodeType)("Identifier"),Object.assign(function(r,s,a){let i;switch(a.name){case "function":i="sent";break;case "new":i="target";break;case "import":i="meta";break}if(!(0, p.default)("Identifier",r.property,{name:i}))throw new TypeError("Unrecognised MetaProperty")},{oneOfNodeTypes:["Identifier"]})):(0, e.assertNodeType)("Identifier")},property:{validate:(0, e.assertNodeType)("Identifier")}}});const v=()=>({abstract:{validate:(0, e.assertValueType)("boolean"),optional:true},accessibility:{validate:(0, e.assertOneOf)("public","private","protected"),optional:true},static:{default:false},override:{default:false},computed:{default:false},optional:{validate:(0, e.assertValueType)("boolean"),optional:true},key:{validate:(0, e.chain)(function(){const r=(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral"),s=(0, e.assertNodeType)("Expression");return function(a,i,o){(a.computed?s:r)(a,i,o);}}(),(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","Expression"))}});r$r.classMethodOrPropertyCommon=v;const T=()=>Object.assign({},c(),v(),{params:(0, e.validateArrayOfType)("Identifier","Pattern","RestElement","TSParameterProperty"),kind:{validate:(0, e.assertOneOf)("get","set","method","constructor"),default:"method"},access:{validate:(0, e.chain)((0, e.assertValueType)("string"),(0, e.assertOneOf)("public","private","protected")),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true}});return r$r.classMethodOrDeclareMethodCommon=T,t("ClassMethod",{aliases:["Function","Scopable","BlockParent","FunctionParent","Method"],builder:["kind","key","params","body","computed","static","generator","async"],visitor:["decorators","key","typeParameters","params","returnType","body"],fields:Object.assign({},T(),d(),{body:{validate:(0, e.assertNodeType)("BlockStatement")}})}),t("ObjectPattern",{visitor:["properties","typeAnnotation","decorators"],builder:["properties"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},u(),{properties:(0, e.validateArrayOfType)("RestElement","ObjectProperty")})}),t("SpreadElement",{visitor:["argument"],aliases:["UnaryLike"],deprecatedAlias:"SpreadProperty",fields:{argument:{validate:(0, e.assertNodeType)("Expression")}}}),t("Super",{aliases:["Expression"]}),t("TaggedTemplateExpression",{visitor:["tag","typeParameters","quasi"],builder:["tag","quasi"],aliases:["Expression"],fields:{tag:{validate:(0, e.assertNodeType)("Expression")},quasi:{validate:(0, e.assertNodeType)("TemplateLiteral")},typeParameters:{validate:(0, e.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:true}}}),t("TemplateElement",{builder:["value","tail"],fields:{value:{validate:(0, e.chain)((0, e.assertShape)({raw:{validate:(0, e.assertValueType)("string")},cooked:{validate:(0, e.assertValueType)("string"),optional:true}}),function(s){const a=s.value.raw;let i=false;const o=()=>{throw new Error("Internal @babel/types error.")},{str:n,firstInvalidLoc:y}=(0, x.readStringContents)("template",a,0,0,0,{unterminated(){i=true;},strictNumericEscape:o,invalidEscapeSequence:o,numericSeparatorInEscapeSequence:o,unexpectedNumericSeparator:o,invalidDigit:o,invalidCodePoint:o});if(!i)throw new Error("Invalid raw");s.value.cooked=y?null:n;})},tail:{default:false}}}),t("TemplateLiteral",{visitor:["quasis","expressions"],aliases:["Expression","Literal"],fields:{quasis:(0, e.validateArrayOfType)("TemplateElement"),expressions:{validate:(0, e.chain)((0, e.assertValueType)("array"),(0, e.assertEach)((0, e.assertNodeType)("Expression","TSType")),function(r,s,a){if(r.quasis.length!==a.length+1)throw new TypeError(`Number of ${r.type} quasis should be exactly one more than the number of expressions.
Expected ${a.length+1} quasis but got ${r.quasis.length}`)})}}}),t("YieldExpression",{builder:["argument","delegate"],visitor:["argument"],aliases:["Expression","Terminatorless"],fields:{delegate:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("boolean"),Object.assign(function(r,s,a){if(a&&!r.argument)throw new TypeError("Property delegate of YieldExpression cannot be true if there is no argument")},{type:"boolean"})):(0, e.assertValueType)("boolean"),default:false},argument:{optional:true,validate:(0, e.assertNodeType)("Expression")}}}),t("AwaitExpression",{builder:["argument"],visitor:["argument"],aliases:["Expression","Terminatorless"],fields:{argument:{validate:(0, e.assertNodeType)("Expression")}}}),t("Import",{aliases:["Expression"]}),t("BigIntLiteral",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]}),t("ExportNamespaceSpecifier",{visitor:["exported"],aliases:["ModuleSpecifier"],fields:{exported:{validate:(0, e.assertNodeType)("Identifier")}}}),t("OptionalMemberExpression",{builder:["object","property","computed","optional"],visitor:["object","property"],aliases:["Expression"],fields:{object:{validate:(0, e.assertNodeType)("Expression")},property:{validate:function(){const r=(0, e.assertNodeType)("Identifier"),s=(0, e.assertNodeType)("Expression");return Object.assign(function(i,o,n){(i.computed?s:r)(i,o,n);},{oneOfNodeTypes:["Expression","Identifier"]})}()},computed:{default:false},optional:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("boolean"),(0, e.assertOptionalChainStart)()):(0, e.assertValueType)("boolean")}}}),t("OptionalCallExpression",{visitor:["callee","arguments","typeParameters","typeArguments"],builder:["callee","arguments","optional"],aliases:["Expression"],fields:Object.assign({callee:{validate:(0, e.assertNodeType)("Expression")},arguments:(0, e.validateArrayOfType)("Expression","SpreadElement","ArgumentPlaceholder"),optional:{validate:process.env.BABEL_TYPES_8_BREAKING?(0, e.chain)((0, e.assertValueType)("boolean"),(0, e.assertOptionalChainStart)()):(0, e.assertValueType)("boolean")},typeArguments:{validate:(0, e.assertNodeType)("TypeParameterInstantiation"),optional:true}},{typeParameters:{validate:(0, e.assertNodeType)("TSTypeParameterInstantiation"),optional:true}})}),t("ClassProperty",{visitor:["decorators","variance","key","typeAnnotation","value"],builder:["key","value","typeAnnotation","decorators","computed","static"],aliases:["Property"],fields:Object.assign({},v(),{value:{validate:(0, e.assertNodeType)("Expression"),optional:true},definite:{validate:(0, e.assertValueType)("boolean"),optional:true},typeAnnotation:{validate:(0, e.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},readonly:{validate:(0, e.assertValueType)("boolean"),optional:true},declare:{validate:(0, e.assertValueType)("boolean"),optional:true},variance:{validate:(0, e.assertNodeType)("Variance"),optional:true}})}),t("ClassAccessorProperty",{visitor:["decorators","key","typeAnnotation","value"],builder:["key","value","typeAnnotation","decorators","computed","static"],aliases:["Property","Accessor"],fields:Object.assign({},v(),{key:{validate:(0, e.chain)(function(){const r=(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","PrivateName"),s=(0, e.assertNodeType)("Expression");return function(a,i,o){(a.computed?s:r)(a,i,o);}}(),(0, e.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","Expression","PrivateName"))},value:{validate:(0, e.assertNodeType)("Expression"),optional:true},definite:{validate:(0, e.assertValueType)("boolean"),optional:true},typeAnnotation:{validate:(0, e.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},readonly:{validate:(0, e.assertValueType)("boolean"),optional:true},declare:{validate:(0, e.assertValueType)("boolean"),optional:true},variance:{validate:(0, e.assertNodeType)("Variance"),optional:true}})}),t("ClassPrivateProperty",{visitor:["decorators","variance","key","typeAnnotation","value"],builder:["key","value","decorators","static"],aliases:["Property","Private"],fields:{key:{validate:(0, e.assertNodeType)("PrivateName")},value:{validate:(0, e.assertNodeType)("Expression"),optional:true},typeAnnotation:{validate:(0, e.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true},static:{validate:(0, e.assertValueType)("boolean"),default:false},readonly:{validate:(0, e.assertValueType)("boolean"),optional:true},optional:{validate:(0, e.assertValueType)("boolean"),optional:true},definite:{validate:(0, e.assertValueType)("boolean"),optional:true},variance:{validate:(0, e.assertNodeType)("Variance"),optional:true}}}),t("ClassPrivateMethod",{builder:["kind","key","params","body","static"],visitor:["decorators","key","typeParameters","params","returnType","body"],aliases:["Function","Scopable","BlockParent","FunctionParent","Method","Private"],fields:Object.assign({},T(),d(),{kind:{validate:(0, e.assertOneOf)("get","set","method"),default:"method"},key:{validate:(0, e.assertNodeType)("PrivateName")},body:{validate:(0, e.assertNodeType)("BlockStatement")}})}),t("PrivateName",{visitor:["id"],aliases:["Private"],fields:{id:{validate:(0, e.assertNodeType)("Identifier")}}}),t("StaticBlock",{visitor:["body"],fields:{body:(0, e.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","FunctionParent"]}),r$r}

var o$k={};

var o$j;function y$2(){if(o$j)return o$k;o$j=1;var t=B(),e=J();const a=(0, e.defineAliasedType)("Flow"),i=l=>{const r=l==="DeclareClass";a(l,{builder:["id","typeParameters","extends","body"],visitor:["id","typeParameters","extends",...r?["mixins","implements"]:[],"body"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),extends:(0, e.validateOptional)((0, e.arrayOfType)("InterfaceExtends"))},r?{mixins:(0, e.validateOptional)((0, e.arrayOfType)("InterfaceExtends")),implements:(0, e.validateOptional)((0, e.arrayOfType)("ClassImplements"))}:{},{body:(0, e.validateType)("ObjectTypeAnnotation")})});};return a("AnyTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("ArrayTypeAnnotation",{visitor:["elementType"],aliases:["FlowType"],fields:{elementType:(0, e.validateType)("FlowType")}}),a("BooleanTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("BooleanLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("NullLiteralTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("ClassImplements",{visitor:["id","typeParameters"],fields:{id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterInstantiation")}}),i("DeclareClass"),a("DeclareFunction",{builder:["id"],visitor:["id","predicate"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier"),predicate:(0, e.validateOptionalType)("DeclaredPredicate")}}),i("DeclareInterface"),a("DeclareModule",{builder:["id","body","kind"],visitor:["id","body"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier","StringLiteral"),body:(0, e.validateType)("BlockStatement"),kind:(0, e.validateOptional)((0, e.assertOneOf)("CommonJS","ES"))}}),a("DeclareModuleExports",{visitor:["typeAnnotation"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{typeAnnotation:(0, e.validateType)("TypeAnnotation")}}),a("DeclareTypeAlias",{visitor:["id","typeParameters","right"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),right:(0, e.validateType)("FlowType")}}),a("DeclareOpaqueType",{visitor:["id","typeParameters","supertype"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),supertype:(0, e.validateOptionalType)("FlowType"),impltype:(0, e.validateOptionalType)("FlowType")}}),a("DeclareVariable",{visitor:["id"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier")}}),a("DeclareExportDeclaration",{visitor:["declaration","specifiers","source","attributes"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({declaration:(0, e.validateOptionalType)("Flow"),specifiers:(0, e.validateOptional)((0, e.arrayOfType)("ExportSpecifier","ExportNamespaceSpecifier")),source:(0, e.validateOptionalType)("StringLiteral"),default:(0, e.validateOptional)((0, e.assertValueType)("boolean"))},t.importAttributes)}),a("DeclareExportAllDeclaration",{visitor:["source","attributes"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({source:(0, e.validateType)("StringLiteral"),exportKind:(0, e.validateOptional)((0, e.assertOneOf)("type","value"))},t.importAttributes)}),a("DeclaredPredicate",{visitor:["value"],aliases:["FlowPredicate"],fields:{value:(0, e.validateType)("Flow")}}),a("ExistsTypeAnnotation",{aliases:["FlowType"]}),a("FunctionTypeAnnotation",{builder:["typeParameters","params","rest","returnType"],visitor:["typeParameters","this","params","rest","returnType"],aliases:["FlowType"],fields:{typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),params:(0, e.validateArrayOfType)("FunctionTypeParam"),rest:(0, e.validateOptionalType)("FunctionTypeParam"),this:(0, e.validateOptionalType)("FunctionTypeParam"),returnType:(0, e.validateType)("FlowType")}}),a("FunctionTypeParam",{visitor:["name","typeAnnotation"],fields:{name:(0, e.validateOptionalType)("Identifier"),typeAnnotation:(0, e.validateType)("FlowType"),optional:(0, e.validateOptional)((0, e.assertValueType)("boolean"))}}),a("GenericTypeAnnotation",{visitor:["id","typeParameters"],aliases:["FlowType"],fields:{id:(0, e.validateType)("Identifier","QualifiedTypeIdentifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterInstantiation")}}),a("InferredPredicate",{aliases:["FlowPredicate"]}),a("InterfaceExtends",{visitor:["id","typeParameters"],fields:{id:(0, e.validateType)("Identifier","QualifiedTypeIdentifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterInstantiation")}}),i("InterfaceDeclaration"),a("InterfaceTypeAnnotation",{visitor:["extends","body"],aliases:["FlowType"],fields:{extends:(0, e.validateOptional)((0, e.arrayOfType)("InterfaceExtends")),body:(0, e.validateType)("ObjectTypeAnnotation")}}),a("IntersectionTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0, e.validate)((0, e.arrayOfType)("FlowType"))}}),a("MixedTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("EmptyTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("NullableTypeAnnotation",{visitor:["typeAnnotation"],aliases:["FlowType"],fields:{typeAnnotation:(0, e.validateType)("FlowType")}}),a("NumberLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0, e.validate)((0, e.assertValueType)("number"))}}),a("NumberTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("ObjectTypeAnnotation",{visitor:["properties","indexers","callProperties","internalSlots"],aliases:["FlowType"],builder:["properties","indexers","callProperties","internalSlots","exact"],fields:{properties:(0, e.validate)((0, e.arrayOfType)("ObjectTypeProperty","ObjectTypeSpreadProperty")),indexers:{validate:(0, e.arrayOfType)("ObjectTypeIndexer"),optional:true,default:[]},callProperties:{validate:(0, e.arrayOfType)("ObjectTypeCallProperty"),optional:true,default:[]},internalSlots:{validate:(0, e.arrayOfType)("ObjectTypeInternalSlot"),optional:true,default:[]},exact:{validate:(0, e.assertValueType)("boolean"),default:false},inexact:(0, e.validateOptional)((0, e.assertValueType)("boolean"))}}),a("ObjectTypeInternalSlot",{visitor:["id","value"],builder:["id","value","optional","static","method"],aliases:["UserWhitespacable"],fields:{id:(0, e.validateType)("Identifier"),value:(0, e.validateType)("FlowType"),optional:(0, e.validate)((0, e.assertValueType)("boolean")),static:(0, e.validate)((0, e.assertValueType)("boolean")),method:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("ObjectTypeCallProperty",{visitor:["value"],aliases:["UserWhitespacable"],fields:{value:(0, e.validateType)("FlowType"),static:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("ObjectTypeIndexer",{visitor:["variance","id","key","value"],builder:["id","key","value","variance"],aliases:["UserWhitespacable"],fields:{id:(0, e.validateOptionalType)("Identifier"),key:(0, e.validateType)("FlowType"),value:(0, e.validateType)("FlowType"),static:(0, e.validate)((0, e.assertValueType)("boolean")),variance:(0, e.validateOptionalType)("Variance")}}),a("ObjectTypeProperty",{visitor:["key","value","variance"],aliases:["UserWhitespacable"],fields:{key:(0, e.validateType)("Identifier","StringLiteral"),value:(0, e.validateType)("FlowType"),kind:(0, e.validate)((0, e.assertOneOf)("init","get","set")),static:(0, e.validate)((0, e.assertValueType)("boolean")),proto:(0, e.validate)((0, e.assertValueType)("boolean")),optional:(0, e.validate)((0, e.assertValueType)("boolean")),variance:(0, e.validateOptionalType)("Variance"),method:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("ObjectTypeSpreadProperty",{visitor:["argument"],aliases:["UserWhitespacable"],fields:{argument:(0, e.validateType)("FlowType")}}),a("OpaqueType",{visitor:["id","typeParameters","supertype","impltype"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),supertype:(0, e.validateOptionalType)("FlowType"),impltype:(0, e.validateType)("FlowType")}}),a("QualifiedTypeIdentifier",{visitor:["qualification","id"],builder:["id","qualification"],fields:{id:(0, e.validateType)("Identifier"),qualification:(0, e.validateType)("Identifier","QualifiedTypeIdentifier")}}),a("StringLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0, e.validate)((0, e.assertValueType)("string"))}}),a("StringTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("SymbolTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("ThisTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("TupleTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0, e.validate)((0, e.arrayOfType)("FlowType"))}}),a("TypeofTypeAnnotation",{visitor:["argument"],aliases:["FlowType"],fields:{argument:(0, e.validateType)("FlowType")}}),a("TypeAlias",{visitor:["id","typeParameters","right"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TypeParameterDeclaration"),right:(0, e.validateType)("FlowType")}}),a("TypeAnnotation",{visitor:["typeAnnotation"],fields:{typeAnnotation:(0, e.validateType)("FlowType")}}),a("TypeCastExpression",{visitor:["expression","typeAnnotation"],aliases:["ExpressionWrapper","Expression"],fields:{expression:(0, e.validateType)("Expression"),typeAnnotation:(0, e.validateType)("TypeAnnotation")}}),a("TypeParameter",{visitor:["bound","default","variance"],fields:{name:(0, e.validate)((0, e.assertValueType)("string")),bound:(0, e.validateOptionalType)("TypeAnnotation"),default:(0, e.validateOptionalType)("FlowType"),variance:(0, e.validateOptionalType)("Variance")}}),a("TypeParameterDeclaration",{visitor:["params"],fields:{params:(0, e.validate)((0, e.arrayOfType)("TypeParameter"))}}),a("TypeParameterInstantiation",{visitor:["params"],fields:{params:(0, e.validate)((0, e.arrayOfType)("FlowType"))}}),a("UnionTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0, e.validate)((0, e.arrayOfType)("FlowType"))}}),a("Variance",{builder:["kind"],fields:{kind:(0, e.validate)((0, e.assertOneOf)("minus","plus"))}}),a("VoidTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]}),a("EnumDeclaration",{aliases:["Statement","Declaration"],visitor:["id","body"],fields:{id:(0, e.validateType)("Identifier"),body:(0, e.validateType)("EnumBooleanBody","EnumNumberBody","EnumStringBody","EnumSymbolBody")}}),a("EnumBooleanBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0, e.validate)((0, e.assertValueType)("boolean")),members:(0, e.validateArrayOfType)("EnumBooleanMember"),hasUnknownMembers:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("EnumNumberBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0, e.validate)((0, e.assertValueType)("boolean")),members:(0, e.validateArrayOfType)("EnumNumberMember"),hasUnknownMembers:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("EnumStringBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0, e.validate)((0, e.assertValueType)("boolean")),members:(0, e.validateArrayOfType)("EnumStringMember","EnumDefaultedMember"),hasUnknownMembers:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("EnumSymbolBody",{aliases:["EnumBody"],visitor:["members"],fields:{members:(0, e.validateArrayOfType)("EnumDefaultedMember"),hasUnknownMembers:(0, e.validate)((0, e.assertValueType)("boolean"))}}),a("EnumBooleanMember",{aliases:["EnumMember"],builder:["id"],visitor:["id","init"],fields:{id:(0, e.validateType)("Identifier"),init:(0, e.validateType)("BooleanLiteral")}}),a("EnumNumberMember",{aliases:["EnumMember"],visitor:["id","init"],fields:{id:(0, e.validateType)("Identifier"),init:(0, e.validateType)("NumericLiteral")}}),a("EnumStringMember",{aliases:["EnumMember"],visitor:["id","init"],fields:{id:(0, e.validateType)("Identifier"),init:(0, e.validateType)("StringLiteral")}}),a("EnumDefaultedMember",{aliases:["EnumMember"],visitor:["id"],fields:{id:(0, e.validateType)("Identifier")}}),a("IndexedAccessType",{visitor:["objectType","indexType"],aliases:["FlowType"],fields:{objectType:(0, e.validateType)("FlowType"),indexType:(0, e.validateType)("FlowType")}}),a("OptionalIndexedAccessType",{visitor:["objectType","indexType"],aliases:["FlowType"],fields:{objectType:(0, e.validateType)("FlowType"),indexType:(0, e.validateType)("FlowType"),optional:(0, e.validate)((0, e.assertValueType)("boolean"))}}),o$k}

var r$l={};

var i$i;function r$k(){if(i$i)return r$l;i$i=1;var e=J();const a=(0, e.defineAliasedType)("JSX");return a("JSXAttribute",{visitor:["name","value"],aliases:["Immutable"],fields:{name:{validate:(0, e.assertNodeType)("JSXIdentifier","JSXNamespacedName")},value:{optional:true,validate:(0, e.assertNodeType)("JSXElement","JSXFragment","StringLiteral","JSXExpressionContainer")}}}),a("JSXClosingElement",{visitor:["name"],aliases:["Immutable"],fields:{name:{validate:(0, e.assertNodeType)("JSXIdentifier","JSXMemberExpression","JSXNamespacedName")}}}),a("JSXElement",{builder:["openingElement","closingElement","children","selfClosing"],visitor:["openingElement","children","closingElement"],aliases:["Immutable","Expression"],fields:Object.assign({openingElement:{validate:(0, e.assertNodeType)("JSXOpeningElement")},closingElement:{optional:true,validate:(0, e.assertNodeType)("JSXClosingElement")},children:(0, e.validateArrayOfType)("JSXText","JSXExpressionContainer","JSXSpreadChild","JSXElement","JSXFragment")},{selfClosing:{validate:(0, e.assertValueType)("boolean"),optional:true}})}),a("JSXEmptyExpression",{}),a("JSXExpressionContainer",{visitor:["expression"],aliases:["Immutable"],fields:{expression:{validate:(0, e.assertNodeType)("Expression","JSXEmptyExpression")}}}),a("JSXSpreadChild",{visitor:["expression"],aliases:["Immutable"],fields:{expression:{validate:(0, e.assertNodeType)("Expression")}}}),a("JSXIdentifier",{builder:["name"],fields:{name:{validate:(0, e.assertValueType)("string")}}}),a("JSXMemberExpression",{visitor:["object","property"],fields:{object:{validate:(0, e.assertNodeType)("JSXMemberExpression","JSXIdentifier")},property:{validate:(0, e.assertNodeType)("JSXIdentifier")}}}),a("JSXNamespacedName",{visitor:["namespace","name"],fields:{namespace:{validate:(0, e.assertNodeType)("JSXIdentifier")},name:{validate:(0, e.assertNodeType)("JSXIdentifier")}}}),a("JSXOpeningElement",{builder:["name","attributes","selfClosing"],visitor:["name","typeParameters","typeArguments","attributes"],aliases:["Immutable"],fields:Object.assign({name:{validate:(0, e.assertNodeType)("JSXIdentifier","JSXMemberExpression","JSXNamespacedName")},selfClosing:{default:false},attributes:(0, e.validateArrayOfType)("JSXAttribute","JSXSpreadAttribute"),typeArguments:{validate:(0, e.assertNodeType)("TypeParameterInstantiation"),optional:true}},{typeParameters:{validate:(0, e.assertNodeType)("TSTypeParameterInstantiation"),optional:true}})}),a("JSXSpreadAttribute",{visitor:["argument"],fields:{argument:{validate:(0, e.assertNodeType)("Expression")}}}),a("JSXText",{aliases:["Immutable"],builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}}}),a("JSXFragment",{builder:["openingFragment","closingFragment","children"],visitor:["openingFragment","children","closingFragment"],aliases:["Immutable","Expression"],fields:{openingFragment:{validate:(0, e.assertNodeType)("JSXOpeningFragment")},closingFragment:{validate:(0, e.assertNodeType)("JSXClosingFragment")},children:(0, e.validateArrayOfType)("JSXText","JSXExpressionContainer","JSXSpreadChild","JSXElement","JSXFragment")}}),a("JSXOpeningFragment",{aliases:["Immutable"]}),a("JSXClosingFragment",{aliases:["Immutable"]}),r$l}

var r$j={};

var e$G={};

var a$d;function i$h(){if(a$d)return e$G;a$d=1,Object.defineProperty(e$G,"__esModule",{value:true}),e$G.PLACEHOLDERS_FLIPPED_ALIAS=e$G.PLACEHOLDERS_ALIAS=e$G.PLACEHOLDERS=void 0;var s=J();const n=e$G.PLACEHOLDERS=["Identifier","StringLiteral","Expression","Statement","Declaration","BlockStatement","ClassBody","Pattern"],o=e$G.PLACEHOLDERS_ALIAS={Declaration:["Statement"],Pattern:["PatternLike","LVal"]};for(const t of n){const r=s.ALIAS_KEYS[t];r!=null&&r.length&&(o[t]=r);}const L=e$G.PLACEHOLDERS_FLIPPED_ALIAS={};return Object.keys(o).forEach(t=>{o[t].forEach(r=>{hasOwnProperty.call(L,r)||(L[r]=[]),L[r].push(t);});}),e$G}

var s$5;function d$1(){if(s$5)return r$j;s$5=1;var e=J(),a=i$h(),t=B();const r=(0, e.defineAliasedType)("Miscellaneous");return r("Noop",{visitor:[]}),r("Placeholder",{visitor:[],builder:["expectedNode","name"],fields:Object.assign({name:{validate:(0, e.assertNodeType)("Identifier")},expectedNode:{validate:(0, e.assertOneOf)(...a.PLACEHOLDERS)}},(0, t.patternLikeCommon)())}),r("V8IntrinsicIdentifier",{builder:["name"],fields:{name:{validate:(0, e.assertValueType)("string")}}}),r$j}

var e$F={};

var i$g;function r$i(){if(i$g)return e$F;i$g=1;var e=J();return (0, e.default)("ArgumentPlaceholder",{}),(0, e.default)("BindExpression",{visitor:["object","callee"],aliases:["Expression"],fields:process.env.BABEL_TYPES_8_BREAKING?{object:{validate:(0, e.assertNodeType)("Expression")},callee:{validate:(0, e.assertNodeType)("Expression")}}:{object:{validate:Object.assign(()=>{},{oneOfNodeTypes:["Expression"]})},callee:{validate:Object.assign(()=>{},{oneOfNodeTypes:["Expression"]})}}}),(0, e.default)("ImportAttribute",{visitor:["key","value"],fields:{key:{validate:(0, e.assertNodeType)("Identifier","StringLiteral")},value:{validate:(0, e.assertNodeType)("StringLiteral")}}}),(0, e.default)("Decorator",{visitor:["expression"],fields:{expression:{validate:(0, e.assertNodeType)("Expression")}}}),(0, e.default)("DoExpression",{visitor:["body"],builder:["body","async"],aliases:["Expression"],fields:{body:{validate:(0, e.assertNodeType)("BlockStatement")},async:{validate:(0, e.assertValueType)("boolean"),default:false}}}),(0, e.default)("ExportDefaultSpecifier",{visitor:["exported"],aliases:["ModuleSpecifier"],fields:{exported:{validate:(0, e.assertNodeType)("Identifier")}}}),(0, e.default)("RecordExpression",{visitor:["properties"],aliases:["Expression"],fields:{properties:(0, e.validateArrayOfType)("ObjectProperty","SpreadElement")}}),(0, e.default)("TupleExpression",{fields:{elements:{validate:(0, e.arrayOfType)("Expression","SpreadElement"),default:[]}},visitor:["elements"],aliases:["Expression"]}),(0, e.default)("DecimalLiteral",{builder:["value"],fields:{value:{validate:(0, e.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]}),(0, e.default)("ModuleExpression",{visitor:["body"],fields:{body:{validate:(0, e.assertNodeType)("Program")}},aliases:["Expression"]}),(0, e.default)("TopicReference",{aliases:["Expression"]}),(0, e.default)("PipelineTopicExpression",{builder:["expression"],visitor:["expression"],fields:{expression:{validate:(0, e.assertNodeType)("Expression")}},aliases:["Expression"]}),(0, e.default)("PipelineBareFunction",{builder:["callee"],visitor:["callee"],fields:{callee:{validate:(0, e.assertNodeType)("Expression")}},aliases:["Expression"]}),(0, e.default)("PipelinePrimaryTopicReference",{aliases:["Expression"]}),e$F}

var p$1={};

var u$9;function g$1(){if(u$9)return p$1;u$9=1;var e=J(),p=B(),c=c$7();const a=(0, e.defineAliasedType)("TypeScript"),t=(0, e.assertValueType)("boolean"),o=()=>({returnType:{validate:(0, e.assertNodeType)("TSTypeAnnotation","Noop"),optional:true},typeParameters:{validate:(0, e.assertNodeType)("TSTypeParameterDeclaration","Noop"),optional:true}});a("TSParameterProperty",{aliases:["LVal"],visitor:["parameter"],fields:{accessibility:{validate:(0, e.assertOneOf)("public","private","protected"),optional:true},readonly:{validate:(0, e.assertValueType)("boolean"),optional:true},parameter:{validate:(0, e.assertNodeType)("Identifier","AssignmentPattern")},override:{validate:(0, e.assertValueType)("boolean"),optional:true},decorators:{validate:(0, e.arrayOfType)("Decorator"),optional:true}}}),a("TSDeclareFunction",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","params","returnType"],fields:Object.assign({},(0, p.functionDeclarationCommon)(),o())}),a("TSDeclareMethod",{visitor:["decorators","key","typeParameters","params","returnType"],fields:Object.assign({},(0, p.classMethodOrDeclareMethodCommon)(),o())}),a("TSQualifiedName",{aliases:["TSEntityName"],visitor:["left","right"],fields:{left:(0, e.validateType)("TSEntityName"),right:(0, e.validateType)("Identifier")}});const s=()=>({typeParameters:(0, e.validateOptionalType)("TSTypeParameterDeclaration"),parameters:(0, e.validateArrayOfType)("ArrayPattern","Identifier","ObjectPattern","RestElement"),typeAnnotation:(0, e.validateOptionalType)("TSTypeAnnotation")}),l={aliases:["TSTypeElement"],visitor:["typeParameters","parameters","typeAnnotation"],fields:s()};a("TSCallSignatureDeclaration",l),a("TSConstructSignatureDeclaration",l);const T=()=>({key:(0, e.validateType)("Expression"),computed:{default:false},optional:(0, e.validateOptional)(t)});a("TSPropertySignature",{aliases:["TSTypeElement"],visitor:["key","typeAnnotation"],fields:Object.assign({},T(),{readonly:(0, e.validateOptional)(t),typeAnnotation:(0, e.validateOptionalType)("TSTypeAnnotation"),kind:{optional:true,validate:(0, e.assertOneOf)("get","set")}})}),a("TSMethodSignature",{aliases:["TSTypeElement"],visitor:["key","typeParameters","parameters","typeAnnotation"],fields:Object.assign({},s(),T(),{kind:{validate:(0, e.assertOneOf)("method","get","set")}})}),a("TSIndexSignature",{aliases:["TSTypeElement"],visitor:["parameters","typeAnnotation"],fields:{readonly:(0, e.validateOptional)(t),static:(0, e.validateOptional)(t),parameters:(0, e.validateArrayOfType)("Identifier"),typeAnnotation:(0, e.validateOptionalType)("TSTypeAnnotation")}});const O=["TSAnyKeyword","TSBooleanKeyword","TSBigIntKeyword","TSIntrinsicKeyword","TSNeverKeyword","TSNullKeyword","TSNumberKeyword","TSObjectKeyword","TSStringKeyword","TSSymbolKeyword","TSUndefinedKeyword","TSUnknownKeyword","TSVoidKeyword"];for(const i of O)a(i,{aliases:["TSType","TSBaseType"],visitor:[],fields:{}});a("TSThisType",{aliases:["TSType","TSBaseType"],visitor:[],fields:{}});const y={aliases:["TSType"],visitor:["typeParameters","parameters","typeAnnotation"]};a("TSFunctionType",Object.assign({},y,{fields:s()})),a("TSConstructorType",Object.assign({},y,{fields:Object.assign({},s(),{abstract:(0, e.validateOptional)(t)})})),a("TSTypeReference",{aliases:["TSType"],visitor:["typeName","typeParameters"],fields:{typeName:(0, e.validateType)("TSEntityName"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterInstantiation")}}),a("TSTypePredicate",{aliases:["TSType"],visitor:["parameterName","typeAnnotation"],builder:["parameterName","typeAnnotation","asserts"],fields:{parameterName:(0, e.validateType)("Identifier","TSThisType"),typeAnnotation:(0, e.validateOptionalType)("TSTypeAnnotation"),asserts:(0, e.validateOptional)(t)}}),a("TSTypeQuery",{aliases:["TSType"],visitor:["exprName","typeParameters"],fields:{exprName:(0, e.validateType)("TSEntityName","TSImportType"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterInstantiation")}}),a("TSTypeLiteral",{aliases:["TSType"],visitor:["members"],fields:{members:(0, e.validateArrayOfType)("TSTypeElement")}}),a("TSArrayType",{aliases:["TSType"],visitor:["elementType"],fields:{elementType:(0, e.validateType)("TSType")}}),a("TSTupleType",{aliases:["TSType"],visitor:["elementTypes"],fields:{elementTypes:(0, e.validateArrayOfType)("TSType","TSNamedTupleMember")}}),a("TSOptionalType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0, e.validateType)("TSType")}}),a("TSRestType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0, e.validateType)("TSType")}}),a("TSNamedTupleMember",{visitor:["label","elementType"],builder:["label","elementType","optional"],fields:{label:(0, e.validateType)("Identifier"),optional:{validate:t,default:false},elementType:(0, e.validateType)("TSType")}});const d={aliases:["TSType"],visitor:["types"],fields:{types:(0, e.validateArrayOfType)("TSType")}};a("TSUnionType",d),a("TSIntersectionType",d),a("TSConditionalType",{aliases:["TSType"],visitor:["checkType","extendsType","trueType","falseType"],fields:{checkType:(0, e.validateType)("TSType"),extendsType:(0, e.validateType)("TSType"),trueType:(0, e.validateType)("TSType"),falseType:(0, e.validateType)("TSType")}}),a("TSInferType",{aliases:["TSType"],visitor:["typeParameter"],fields:{typeParameter:(0, e.validateType)("TSTypeParameter")}}),a("TSParenthesizedType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0, e.validateType)("TSType")}}),a("TSTypeOperator",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{operator:(0, e.validate)((0, e.assertValueType)("string")),typeAnnotation:(0, e.validateType)("TSType")}}),a("TSIndexedAccessType",{aliases:["TSType"],visitor:["objectType","indexType"],fields:{objectType:(0, e.validateType)("TSType"),indexType:(0, e.validateType)("TSType")}}),a("TSMappedType",{aliases:["TSType"],visitor:["typeParameter","nameType","typeAnnotation"],builder:["typeParameter","typeAnnotation","nameType"],fields:Object.assign({},{typeParameter:(0, e.validateType)("TSTypeParameter")},{readonly:(0, e.validateOptional)((0, e.assertOneOf)(true,false,"+","-")),optional:(0, e.validateOptional)((0, e.assertOneOf)(true,false,"+","-")),typeAnnotation:(0, e.validateOptionalType)("TSType"),nameType:(0, e.validateOptionalType)("TSType")})}),a("TSTemplateLiteralType",{aliases:["TSType","TSBaseType"],visitor:["quasis","types"],fields:{quasis:(0, e.validateArrayOfType)("TemplateElement"),types:{validate:(0, e.chain)((0, e.assertValueType)("array"),(0, e.assertEach)((0, e.assertNodeType)("TSType")),function(i,m,n){if(i.quasis.length!==n.length+1)throw new TypeError(`Number of ${i.type} quasis should be exactly one more than the number of types.
Expected ${n.length+1} quasis but got ${i.quasis.length}`)})}}}),a("TSLiteralType",{aliases:["TSType","TSBaseType"],visitor:["literal"],fields:{literal:{validate:function(){const i=(0, e.assertNodeType)("NumericLiteral","BigIntLiteral"),m=(0, e.assertOneOf)("-"),n=(0, e.assertNodeType)("NumericLiteral","StringLiteral","BooleanLiteral","BigIntLiteral","TemplateLiteral");function v(b,A,r){(0, c.default)("UnaryExpression",r)?(m(r,"operator",r.operator),i(r,"argument",r.argument)):n(b,A,r);}return v.oneOfNodeTypes=["NumericLiteral","StringLiteral","BooleanLiteral","BigIntLiteral","TemplateLiteral","UnaryExpression"],v}()}}}),a("TSExpressionWithTypeArguments",{aliases:["TSType"],visitor:["expression","typeParameters"],fields:{expression:(0, e.validateType)("TSEntityName"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterInstantiation")}}),a("TSInterfaceDeclaration",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","extends","body"],fields:{declare:(0, e.validateOptional)(t),id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterDeclaration"),extends:(0, e.validateOptional)((0, e.arrayOfType)("TSExpressionWithTypeArguments")),body:(0, e.validateType)("TSInterfaceBody")}}),a("TSInterfaceBody",{visitor:["body"],fields:{body:(0, e.validateArrayOfType)("TSTypeElement")}}),a("TSTypeAliasDeclaration",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","typeAnnotation"],fields:{declare:(0, e.validateOptional)(t),id:(0, e.validateType)("Identifier"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterDeclaration"),typeAnnotation:(0, e.validateType)("TSType")}}),a("TSInstantiationExpression",{aliases:["Expression"],visitor:["expression","typeParameters"],fields:{expression:(0, e.validateType)("Expression"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterInstantiation")}});const S={aliases:["Expression","LVal","PatternLike"],visitor:["expression","typeAnnotation"],fields:{expression:(0, e.validateType)("Expression"),typeAnnotation:(0, e.validateType)("TSType")}};return a("TSAsExpression",S),a("TSSatisfiesExpression",S),a("TSTypeAssertion",{aliases:["Expression","LVal","PatternLike"],visitor:["typeAnnotation","expression"],fields:{typeAnnotation:(0, e.validateType)("TSType"),expression:(0, e.validateType)("Expression")}}),a("TSEnumBody",{visitor:["members"],fields:{members:(0, e.validateArrayOfType)("TSEnumMember")}}),a("TSEnumDeclaration",{aliases:["Statement","Declaration"],visitor:["id","members"],fields:{declare:(0, e.validateOptional)(t),const:(0, e.validateOptional)(t),id:(0, e.validateType)("Identifier"),members:(0, e.validateArrayOfType)("TSEnumMember"),initializer:(0, e.validateOptionalType)("Expression"),body:(0, e.validateOptionalType)("TSEnumBody")}}),a("TSEnumMember",{visitor:["id","initializer"],fields:{id:(0, e.validateType)("Identifier","StringLiteral"),initializer:(0, e.validateOptionalType)("Expression")}}),a("TSModuleDeclaration",{aliases:["Statement","Declaration"],visitor:["id","body"],fields:Object.assign({kind:{validate:(0, e.assertOneOf)("global","module","namespace")},declare:(0, e.validateOptional)(t)},{global:(0, e.validateOptional)(t)},{id:(0, e.validateType)("Identifier","StringLiteral"),body:(0, e.validateType)("TSModuleBlock","TSModuleDeclaration")})}),a("TSModuleBlock",{aliases:["Scopable","Block","BlockParent","FunctionParent"],visitor:["body"],fields:{body:(0, e.validateArrayOfType)("Statement")}}),a("TSImportType",{aliases:["TSType"],builder:["argument","qualifier","typeParameters"],visitor:["argument","options","qualifier","typeParameters"],fields:{argument:(0, e.validateType)("StringLiteral"),qualifier:(0, e.validateOptionalType)("TSEntityName"),typeParameters:(0, e.validateOptionalType)("TSTypeParameterInstantiation"),options:{validate:(0, e.assertNodeType)("Expression"),optional:true}}}),a("TSImportEqualsDeclaration",{aliases:["Statement","Declaration"],visitor:["id","moduleReference"],fields:Object.assign({},{isExport:(0, e.validate)(t)},{id:(0, e.validateType)("Identifier"),moduleReference:(0, e.validateType)("TSEntityName","TSExternalModuleReference"),importKind:{validate:(0, e.assertOneOf)("type","value"),optional:true}})}),a("TSExternalModuleReference",{visitor:["expression"],fields:{expression:(0, e.validateType)("StringLiteral")}}),a("TSNonNullExpression",{aliases:["Expression","LVal","PatternLike"],visitor:["expression"],fields:{expression:(0, e.validateType)("Expression")}}),a("TSExportAssignment",{aliases:["Statement"],visitor:["expression"],fields:{expression:(0, e.validateType)("Expression")}}),a("TSNamespaceExportDeclaration",{aliases:["Statement"],visitor:["id"],fields:{id:(0, e.validateType)("Identifier")}}),a("TSTypeAnnotation",{visitor:["typeAnnotation"],fields:{typeAnnotation:{validate:(0, e.assertNodeType)("TSType")}}}),a("TSTypeParameterInstantiation",{visitor:["params"],fields:{params:(0, e.validateArrayOfType)("TSType")}}),a("TSTypeParameterDeclaration",{visitor:["params"],fields:{params:(0, e.validateArrayOfType)("TSTypeParameter")}}),a("TSTypeParameter",{builder:["constraint","default","name"],visitor:["constraint","default"],fields:{name:{validate:(0, e.assertValueType)("string")},in:{validate:(0, e.assertValueType)("boolean"),optional:true},out:{validate:(0, e.assertValueType)("boolean"),optional:true},const:{validate:(0, e.assertValueType)("boolean"),optional:true},constraint:{validate:(0, e.assertNodeType)("TSType"),optional:true},default:{validate:(0, e.assertNodeType)("TSType"),optional:true}}}),p$1}

var e$E={};

var r$h;function t$6(){if(r$h)return e$E;r$h=1,Object.defineProperty(e$E,"__esModule",{value:true}),e$E.DEPRECATED_ALIASES=void 0;e$E.DEPRECATED_ALIASES={ModuleDeclaration:"ImportOrExportDeclaration"};return e$E}

var i$f;function I$1(){return i$f?e$L:(i$f=1,function(e){Object.defineProperty(e,"__esModule",{value:true}),Object.defineProperty(e,"ALIAS_KEYS",{enumerable:true,get:function(){return r.ALIAS_KEYS}}),Object.defineProperty(e,"BUILDER_KEYS",{enumerable:true,get:function(){return r.BUILDER_KEYS}}),Object.defineProperty(e,"DEPRECATED_ALIASES",{enumerable:true,get:function(){return u.DEPRECATED_ALIASES}}),Object.defineProperty(e,"DEPRECATED_KEYS",{enumerable:true,get:function(){return r.DEPRECATED_KEYS}}),Object.defineProperty(e,"FLIPPED_ALIAS_KEYS",{enumerable:true,get:function(){return r.FLIPPED_ALIAS_KEYS}}),Object.defineProperty(e,"NODE_FIELDS",{enumerable:true,get:function(){return r.NODE_FIELDS}}),Object.defineProperty(e,"NODE_PARENT_VALIDATIONS",{enumerable:true,get:function(){return r.NODE_PARENT_VALIDATIONS}}),Object.defineProperty(e,"PLACEHOLDERS",{enumerable:true,get:function(){return t.PLACEHOLDERS}}),Object.defineProperty(e,"PLACEHOLDERS_ALIAS",{enumerable:true,get:function(){return t.PLACEHOLDERS_ALIAS}}),Object.defineProperty(e,"PLACEHOLDERS_FLIPPED_ALIAS",{enumerable:true,get:function(){return t.PLACEHOLDERS_FLIPPED_ALIAS}}),e.TYPES=void 0,Object.defineProperty(e,"VISITOR_KEYS",{enumerable:true,get:function(){return r.VISITOR_KEYS}}),B(),y$2(),r$k(),d$1(),r$i(),g$1();var r=J(),t=i$h(),u=t$6();Object.keys(u.DEPRECATED_ALIASES).forEach(n=>{r.FLIPPED_ALIAS_KEYS[n]=r.FLIPPED_ALIAS_KEYS[u.DEPRECATED_ALIASES[n]];});e.TYPES=[].concat(Object.keys(r.VISITOR_KEYS),Object.keys(r.FLIPPED_ALIAS_KEYS),Object.keys(r.DEPRECATED_KEYS));}(e$L),e$L)}

var f$9;function N$1(){if(f$9)return a$f;f$9=1,Object.defineProperty(a$f,"__esModule",{value:true}),a$f.default=s,a$f.validateChild=o,a$f.validateField=u,a$f.validateInternal=c;var l=I$1();function s(r,i,e){if(!r)return;const t=l.NODE_FIELDS[r.type];if(!t)return;const n=t[i];u(r,i,e,n),o(r,i,e);}function c(r,i,e,t,n){if(r!=null&&r.validate&&!(r.optional&&t==null)&&(r.validate(i,e,t),n)){var _;const d=t.type;if(d==null)return;(_=l.NODE_PARENT_VALIDATIONS[d])==null||_.call(l.NODE_PARENT_VALIDATIONS,i,e,t);}}function u(r,i,e,t){t!=null&&t.validate&&(t.optional&&e==null||t.validate(r,i,e));}function o(r,i,e){var t;const n=e?.type;n!=null&&((t=l.NODE_PARENT_VALIDATIONS[n])==null||t.call(l.NODE_PARENT_VALIDATIONS,r,i,e));}return a$f}

var x;function cr(){if(x)return e$M;x=1,Object.defineProperty(e$M,"__esModule",{value:true}),e$M.anyTypeAnnotation=He,e$M.argumentPlaceholder=jn,e$M.arrayExpression=A,e$M.arrayPattern=Te,e$M.arrayTypeAnnotation=Ze,e$M.arrowFunctionExpression=Se,e$M.assignmentExpression=g,e$M.assignmentPattern=me,e$M.awaitExpression=Xe,e$M.bigIntLiteral=qe,e$M.binaryExpression=P,e$M.bindExpression=Mn,e$M.blockStatement=j,e$M.booleanLiteral=z,e$M.booleanLiteralTypeAnnotation=et,e$M.booleanTypeAnnotation=_e,e$M.breakStatement=M,e$M.callExpression=w,e$M.catchClause=C,e$M.classAccessorProperty=Qe,e$M.classBody=xe,e$M.classDeclaration=be,e$M.classExpression=Ee,e$M.classImplements=nt,e$M.classMethod=Le,e$M.classPrivateMethod=$e,e$M.classPrivateProperty=ze,e$M.classProperty=We,e$M.conditionalExpression=N,e$M.continueStatement=L,e$M.debuggerStatement=v,e$M.decimalLiteral=On,e$M.declareClass=ot,e$M.declareExportAllDeclaration=dt,e$M.declareExportDeclaration=ut,e$M.declareFunction=rt,e$M.declareInterface=st,e$M.declareModule=it,e$M.declareModuleExports=at,e$M.declareOpaqueType=pt,e$M.declareTypeAlias=ct,e$M.declareVariable=lt,e$M.declaredPredicate=yt,e$M.decorator=Cn,e$M.directive=I,e$M.directiveLiteral=h,e$M.doExpression=Nn,e$M.doWhileStatement=B,e$M.emptyStatement=O,e$M.emptyTypeAnnotation=Dt,e$M.enumBooleanBody=Zt,e$M.enumBooleanMember=nn,e$M.enumDeclaration=Ht,e$M.enumDefaultedMember=sn,e$M.enumNumberBody=_t,e$M.enumNumberMember=on,e$M.enumStringBody=en,e$M.enumStringMember=rn,e$M.enumSymbolBody=tn,e$M.existsTypeAnnotation=ft,e$M.exportAllDeclaration=Ae,e$M.exportDefaultDeclaration=ge,e$M.exportDefaultSpecifier=Ln,e$M.exportNamedDeclaration=Pe,e$M.exportNamespaceSpecifier=Je,e$M.exportSpecifier=De,e$M.expressionStatement=F,e$M.file=K,e$M.forInStatement=k,e$M.forOfStatement=Ie,e$M.forStatement=X,e$M.functionDeclaration=R,e$M.functionExpression=q,e$M.functionTypeAnnotation=mt,e$M.functionTypeParam=Tt,e$M.genericTypeAnnotation=St,e$M.identifier=J$1,e$M.ifStatement=U,e$M.import=Re,e$M.importAttribute=wn,e$M.importDeclaration=he,e$M.importDefaultSpecifier=je,e$M.importExpression=Ce,e$M.importNamespaceSpecifier=Me,e$M.importSpecifier=we,e$M.indexedAccessType=an,e$M.inferredPredicate=xt,e$M.interfaceDeclaration=bt,e$M.interfaceExtends=Et,e$M.interfaceTypeAnnotation=At,e$M.interpreterDirective=D,e$M.intersectionTypeAnnotation=gt,e$M.jSXAttribute=e$M.jsxAttribute=pn,e$M.jSXClosingElement=e$M.jsxClosingElement=ln,e$M.jSXClosingFragment=e$M.jsxClosingFragment=Pn,e$M.jSXElement=e$M.jsxElement=un,e$M.jSXEmptyExpression=e$M.jsxEmptyExpression=dn,e$M.jSXExpressionContainer=e$M.jsxExpressionContainer=yn,e$M.jSXFragment=e$M.jsxFragment=An,e$M.jSXIdentifier=e$M.jsxIdentifier=mn,e$M.jSXMemberExpression=e$M.jsxMemberExpression=Tn,e$M.jSXNamespacedName=e$M.jsxNamespacedName=Sn,e$M.jSXOpeningElement=e$M.jsxOpeningElement=xn,e$M.jSXOpeningFragment=e$M.jsxOpeningFragment=gn,e$M.jSXSpreadAttribute=e$M.jsxSpreadAttribute=En,e$M.jSXSpreadChild=e$M.jsxSpreadChild=fn,e$M.jSXText=e$M.jsxText=bn,e$M.labeledStatement=V,e$M.logicalExpression=$,e$M.memberExpression=G,e$M.metaProperty=Ne,e$M.mixedTypeAnnotation=Pt,e$M.moduleExpression=Fn,e$M.newExpression=Y,e$M.noop=Dn,e$M.nullLiteral=Q,e$M.nullLiteralTypeAnnotation=tt,e$M.nullableTypeAnnotation=It,e$M.numberLiteral=tr,e$M.numberLiteralTypeAnnotation=ht,e$M.numberTypeAnnotation=jt,e$M.numericLiteral=f,e$M.objectExpression=Z,e$M.objectMethod=_,e$M.objectPattern=ve,e$M.objectProperty=ee,e$M.objectTypeAnnotation=Mt,e$M.objectTypeCallProperty=Ct,e$M.objectTypeIndexer=Nt,e$M.objectTypeInternalSlot=wt,e$M.objectTypeProperty=Lt,e$M.objectTypeSpreadProperty=vt,e$M.opaqueType=Bt,e$M.optionalCallExpression=Ve,e$M.optionalIndexedAccessType=cn,e$M.optionalMemberExpression=Ue,e$M.parenthesizedExpression=oe,e$M.pipelineBareFunction=Xn,e$M.pipelinePrimaryTopicReference=Rn,e$M.pipelineTopicExpression=kn,e$M.placeholder=In,e$M.privateName=Ge,e$M.program=H,e$M.qualifiedTypeIdentifier=Ot,e$M.recordExpression=vn,e$M.regExpLiteral=m,e$M.regexLiteral=nr,e$M.restElement=T,e$M.restProperty=or,e$M.returnStatement=te,e$M.sequenceExpression=ne,e$M.spreadElement=S,e$M.spreadProperty=rr,e$M.staticBlock=Ye,e$M.stringLiteral=W,e$M.stringLiteralTypeAnnotation=Ft,e$M.stringTypeAnnotation=Kt,e$M.super=Be,e$M.switchCase=re,e$M.switchStatement=se,e$M.symbolTypeAnnotation=kt,e$M.taggedTemplateExpression=Oe,e$M.templateElement=Fe,e$M.templateLiteral=Ke,e$M.thisExpression=ie,e$M.thisTypeAnnotation=Xt,e$M.throwStatement=ae,e$M.topicReference=Kn,e$M.tryStatement=ce,e$M.tSAnyKeyword=e$M.tsAnyKeyword=Yn,e$M.tSArrayType=e$M.tsArrayType=So,e$M.tSAsExpression=e$M.tsAsExpression=Ko,e$M.tSBigIntKeyword=e$M.tsBigIntKeyword=Zn,e$M.tSBooleanKeyword=e$M.tsBooleanKeyword=Hn,e$M.tSCallSignatureDeclaration=e$M.tsCallSignatureDeclaration=Wn,e$M.tSConditionalType=e$M.tsConditionalType=Do,e$M.tSConstructSignatureDeclaration=e$M.tsConstructSignatureDeclaration=Qn,e$M.tSConstructorType=e$M.tsConstructorType=uo,e$M.tSDeclareFunction=e$M.tsDeclareFunction=Jn,e$M.tSDeclareMethod=e$M.tsDeclareMethod=Un,e$M.tSEnumBody=e$M.tsEnumBody=Ro,e$M.tSEnumDeclaration=e$M.tsEnumDeclaration=qo,e$M.tSEnumMember=e$M.tsEnumMember=Jo,e$M.tSExportAssignment=e$M.tsExportAssignment=Go,e$M.tSExpressionWithTypeArguments=e$M.tsExpressionWithTypeArguments=Lo,e$M.tSExternalModuleReference=e$M.tsExternalModuleReference=zo,e$M.tSFunctionType=e$M.tsFunctionType=lo,e$M.tSImportEqualsDeclaration=e$M.tsImportEqualsDeclaration=Qo,e$M.tSImportType=e$M.tsImportType=Wo,e$M.tSIndexSignature=e$M.tsIndexSignature=Gn,e$M.tSIndexedAccessType=e$M.tsIndexedAccessType=Mo,e$M.tSInferType=e$M.tsInferType=Io,e$M.tSInstantiationExpression=e$M.tsInstantiationExpression=Fo,e$M.tSInterfaceBody=e$M.tsInterfaceBody=Bo,e$M.tSInterfaceDeclaration=e$M.tsInterfaceDeclaration=vo,e$M.tSIntersectionType=e$M.tsIntersectionType=Po,e$M.tSIntrinsicKeyword=e$M.tsIntrinsicKeyword=_n,e$M.tSLiteralType=e$M.tsLiteralType=No,e$M.tSMappedType=e$M.tsMappedType=wo,e$M.tSMethodSignature=e$M.tsMethodSignature=$n,e$M.tSModuleBlock=e$M.tsModuleBlock=Vo,e$M.tSModuleDeclaration=e$M.tsModuleDeclaration=Uo,e$M.tSNamedTupleMember=e$M.tsNamedTupleMember=Ao,e$M.tSNamespaceExportDeclaration=e$M.tsNamespaceExportDeclaration=Yo,e$M.tSNeverKeyword=e$M.tsNeverKeyword=eo,e$M.tSNonNullExpression=e$M.tsNonNullExpression=$o,e$M.tSNullKeyword=e$M.tsNullKeyword=to,e$M.tSNumberKeyword=e$M.tsNumberKeyword=no,e$M.tSObjectKeyword=e$M.tsObjectKeyword=oo,e$M.tSOptionalType=e$M.tsOptionalType=Eo,e$M.tSParameterProperty=e$M.tsParameterProperty=qn,e$M.tSParenthesizedType=e$M.tsParenthesizedType=ho,e$M.tSPropertySignature=e$M.tsPropertySignature=zn,e$M.tSQualifiedName=e$M.tsQualifiedName=Vn,e$M.tSRestType=e$M.tsRestType=bo,e$M.tSSatisfiesExpression=e$M.tsSatisfiesExpression=ko,e$M.tSStringKeyword=e$M.tsStringKeyword=ro,e$M.tSSymbolKeyword=e$M.tsSymbolKeyword=so,e$M.tSTemplateLiteralType=e$M.tsTemplateLiteralType=Co,e$M.tSThisType=e$M.tsThisType=po,e$M.tSTupleType=e$M.tsTupleType=xo,e$M.tSTypeAliasDeclaration=e$M.tsTypeAliasDeclaration=Oo,e$M.tSTypeAnnotation=e$M.tsTypeAnnotation=Ho,e$M.tSTypeAssertion=e$M.tsTypeAssertion=Xo,e$M.tSTypeLiteral=e$M.tsTypeLiteral=To,e$M.tSTypeOperator=e$M.tsTypeOperator=jo,e$M.tSTypeParameter=e$M.tsTypeParameter=er,e$M.tSTypeParameterDeclaration=e$M.tsTypeParameterDeclaration=_o,e$M.tSTypeParameterInstantiation=e$M.tsTypeParameterInstantiation=Zo,e$M.tSTypePredicate=e$M.tsTypePredicate=fo,e$M.tSTypeQuery=e$M.tsTypeQuery=mo,e$M.tSTypeReference=e$M.tsTypeReference=yo,e$M.tSUndefinedKeyword=e$M.tsUndefinedKeyword=io,e$M.tSUnionType=e$M.tsUnionType=go,e$M.tSUnknownKeyword=e$M.tsUnknownKeyword=ao,e$M.tSVoidKeyword=e$M.tsVoidKeyword=co,e$M.tupleExpression=Bn,e$M.tupleTypeAnnotation=Rt,e$M.typeAlias=Jt,e$M.typeAnnotation=Ut,e$M.typeCastExpression=Vt,e$M.typeParameter=Wt,e$M.typeParameterDeclaration=Qt,e$M.typeParameterInstantiation=zt,e$M.typeofTypeAnnotation=qt,e$M.unaryExpression=pe,e$M.unionTypeAnnotation=$t,e$M.updateExpression=le,e$M.v8IntrinsicIdentifier=hn,e$M.variableDeclaration=ue,e$M.variableDeclarator=de,e$M.variance=Gt,e$M.voidTypeAnnotation=Yt,e$M.whileStatement=ye,e$M.withStatement=fe,e$M.yieldExpression=ke;var E=N$1(),y=m$4(),b=J();const{validateInternal:o}=E,{NODE_FIELDS:a}=b;function A(e=[]){const t={type:"ArrayExpression",elements:e},n=a.ArrayExpression;return o(n.elements,t,"elements",e,1),t}function g(e,t,n){const r={type:"AssignmentExpression",operator:e,left:t,right:n},i=a.AssignmentExpression;return o(i.operator,r,"operator",e),o(i.left,r,"left",t,1),o(i.right,r,"right",n,1),r}function P(e,t,n){const r={type:"BinaryExpression",operator:e,left:t,right:n},i=a.BinaryExpression;return o(i.operator,r,"operator",e),o(i.left,r,"left",t,1),o(i.right,r,"right",n,1),r}function D(e){const t={type:"InterpreterDirective",value:e},n=a.InterpreterDirective;return o(n.value,t,"value",e),t}function I(e){const t={type:"Directive",value:e},n=a.Directive;return o(n.value,t,"value",e,1),t}function h(e){const t={type:"DirectiveLiteral",value:e},n=a.DirectiveLiteral;return o(n.value,t,"value",e),t}function j(e,t=[]){const n={type:"BlockStatement",body:e,directives:t},r=a.BlockStatement;return o(r.body,n,"body",e,1),o(r.directives,n,"directives",t,1),n}function M(e=null){const t={type:"BreakStatement",label:e},n=a.BreakStatement;return o(n.label,t,"label",e,1),t}function w(e,t){const n={type:"CallExpression",callee:e,arguments:t},r=a.CallExpression;return o(r.callee,n,"callee",e,1),o(r.arguments,n,"arguments",t,1),n}function C(e=null,t){const n={type:"CatchClause",param:e,body:t},r=a.CatchClause;return o(r.param,n,"param",e,1),o(r.body,n,"body",t,1),n}function N(e,t,n){const r={type:"ConditionalExpression",test:e,consequent:t,alternate:n},i=a.ConditionalExpression;return o(i.test,r,"test",e,1),o(i.consequent,r,"consequent",t,1),o(i.alternate,r,"alternate",n,1),r}function L(e=null){const t={type:"ContinueStatement",label:e},n=a.ContinueStatement;return o(n.label,t,"label",e,1),t}function v(){return {type:"DebuggerStatement"}}function B(e,t){const n={type:"DoWhileStatement",test:e,body:t},r=a.DoWhileStatement;return o(r.test,n,"test",e,1),o(r.body,n,"body",t,1),n}function O(){return {type:"EmptyStatement"}}function F(e){const t={type:"ExpressionStatement",expression:e},n=a.ExpressionStatement;return o(n.expression,t,"expression",e,1),t}function K(e,t=null,n=null){const r={type:"File",program:e,comments:t,tokens:n},i=a.File;return o(i.program,r,"program",e,1),o(i.comments,r,"comments",t,1),o(i.tokens,r,"tokens",n),r}function k(e,t,n){const r={type:"ForInStatement",left:e,right:t,body:n},i=a.ForInStatement;return o(i.left,r,"left",e,1),o(i.right,r,"right",t,1),o(i.body,r,"body",n,1),r}function X(e=null,t=null,n=null,r){const i={type:"ForStatement",init:e,test:t,update:n,body:r},c=a.ForStatement;return o(c.init,i,"init",e,1),o(c.test,i,"test",t,1),o(c.update,i,"update",n,1),o(c.body,i,"body",r,1),i}function R(e=null,t,n,r=false,i=false){const c={type:"FunctionDeclaration",id:e,params:t,body:n,generator:r,async:i},p=a.FunctionDeclaration;return o(p.id,c,"id",e,1),o(p.params,c,"params",t,1),o(p.body,c,"body",n,1),o(p.generator,c,"generator",r),o(p.async,c,"async",i),c}function q(e=null,t,n,r=false,i=false){const c={type:"FunctionExpression",id:e,params:t,body:n,generator:r,async:i},p=a.FunctionExpression;return o(p.id,c,"id",e,1),o(p.params,c,"params",t,1),o(p.body,c,"body",n,1),o(p.generator,c,"generator",r),o(p.async,c,"async",i),c}function J$1(e){const t={type:"Identifier",name:e},n=a.Identifier;return o(n.name,t,"name",e),t}function U(e,t,n=null){const r={type:"IfStatement",test:e,consequent:t,alternate:n},i=a.IfStatement;return o(i.test,r,"test",e,1),o(i.consequent,r,"consequent",t,1),o(i.alternate,r,"alternate",n,1),r}function V(e,t){const n={type:"LabeledStatement",label:e,body:t},r=a.LabeledStatement;return o(r.label,n,"label",e,1),o(r.body,n,"body",t,1),n}function W(e){const t={type:"StringLiteral",value:e},n=a.StringLiteral;return o(n.value,t,"value",e),t}function f(e){const t={type:"NumericLiteral",value:e},n=a.NumericLiteral;return o(n.value,t,"value",e),t}function Q(){return {type:"NullLiteral"}}function z(e){const t={type:"BooleanLiteral",value:e},n=a.BooleanLiteral;return o(n.value,t,"value",e),t}function m(e,t=""){const n={type:"RegExpLiteral",pattern:e,flags:t},r=a.RegExpLiteral;return o(r.pattern,n,"pattern",e),o(r.flags,n,"flags",t),n}function $(e,t,n){const r={type:"LogicalExpression",operator:e,left:t,right:n},i=a.LogicalExpression;return o(i.operator,r,"operator",e),o(i.left,r,"left",t,1),o(i.right,r,"right",n,1),r}function G(e,t,n=false,r=null){const i={type:"MemberExpression",object:e,property:t,computed:n,optional:r},c=a.MemberExpression;return o(c.object,i,"object",e,1),o(c.property,i,"property",t,1),o(c.computed,i,"computed",n),o(c.optional,i,"optional",r),i}function Y(e,t){const n={type:"NewExpression",callee:e,arguments:t},r=a.NewExpression;return o(r.callee,n,"callee",e,1),o(r.arguments,n,"arguments",t,1),n}function H(e,t=[],n="script",r=null){const i={type:"Program",body:e,directives:t,sourceType:n,interpreter:r},c=a.Program;return o(c.body,i,"body",e,1),o(c.directives,i,"directives",t,1),o(c.sourceType,i,"sourceType",n),o(c.interpreter,i,"interpreter",r,1),i}function Z(e){const t={type:"ObjectExpression",properties:e},n=a.ObjectExpression;return o(n.properties,t,"properties",e,1),t}function _(e="method",t,n,r,i=false,c=false,p=false){const l={type:"ObjectMethod",kind:e,key:t,params:n,body:r,computed:i,generator:c,async:p},u=a.ObjectMethod;return o(u.kind,l,"kind",e),o(u.key,l,"key",t,1),o(u.params,l,"params",n,1),o(u.body,l,"body",r,1),o(u.computed,l,"computed",i),o(u.generator,l,"generator",c),o(u.async,l,"async",p),l}function ee(e,t,n=false,r=false,i=null){const c={type:"ObjectProperty",key:e,value:t,computed:n,shorthand:r,decorators:i},p=a.ObjectProperty;return o(p.key,c,"key",e,1),o(p.value,c,"value",t,1),o(p.computed,c,"computed",n),o(p.shorthand,c,"shorthand",r),o(p.decorators,c,"decorators",i,1),c}function T(e){const t={type:"RestElement",argument:e},n=a.RestElement;return o(n.argument,t,"argument",e,1),t}function te(e=null){const t={type:"ReturnStatement",argument:e},n=a.ReturnStatement;return o(n.argument,t,"argument",e,1),t}function ne(e){const t={type:"SequenceExpression",expressions:e},n=a.SequenceExpression;return o(n.expressions,t,"expressions",e,1),t}function oe(e){const t={type:"ParenthesizedExpression",expression:e},n=a.ParenthesizedExpression;return o(n.expression,t,"expression",e,1),t}function re(e=null,t){const n={type:"SwitchCase",test:e,consequent:t},r=a.SwitchCase;return o(r.test,n,"test",e,1),o(r.consequent,n,"consequent",t,1),n}function se(e,t){const n={type:"SwitchStatement",discriminant:e,cases:t},r=a.SwitchStatement;return o(r.discriminant,n,"discriminant",e,1),o(r.cases,n,"cases",t,1),n}function ie(){return {type:"ThisExpression"}}function ae(e){const t={type:"ThrowStatement",argument:e},n=a.ThrowStatement;return o(n.argument,t,"argument",e,1),t}function ce(e,t=null,n=null){const r={type:"TryStatement",block:e,handler:t,finalizer:n},i=a.TryStatement;return o(i.block,r,"block",e,1),o(i.handler,r,"handler",t,1),o(i.finalizer,r,"finalizer",n,1),r}function pe(e,t,n=true){const r={type:"UnaryExpression",operator:e,argument:t,prefix:n},i=a.UnaryExpression;return o(i.operator,r,"operator",e),o(i.argument,r,"argument",t,1),o(i.prefix,r,"prefix",n),r}function le(e,t,n=false){const r={type:"UpdateExpression",operator:e,argument:t,prefix:n},i=a.UpdateExpression;return o(i.operator,r,"operator",e),o(i.argument,r,"argument",t,1),o(i.prefix,r,"prefix",n),r}function ue(e,t){const n={type:"VariableDeclaration",kind:e,declarations:t},r=a.VariableDeclaration;return o(r.kind,n,"kind",e),o(r.declarations,n,"declarations",t,1),n}function de(e,t=null){const n={type:"VariableDeclarator",id:e,init:t},r=a.VariableDeclarator;return o(r.id,n,"id",e,1),o(r.init,n,"init",t,1),n}function ye(e,t){const n={type:"WhileStatement",test:e,body:t},r=a.WhileStatement;return o(r.test,n,"test",e,1),o(r.body,n,"body",t,1),n}function fe(e,t){const n={type:"WithStatement",object:e,body:t},r=a.WithStatement;return o(r.object,n,"object",e,1),o(r.body,n,"body",t,1),n}function me(e,t){const n={type:"AssignmentPattern",left:e,right:t},r=a.AssignmentPattern;return o(r.left,n,"left",e,1),o(r.right,n,"right",t,1),n}function Te(e){const t={type:"ArrayPattern",elements:e},n=a.ArrayPattern;return o(n.elements,t,"elements",e,1),t}function Se(e,t,n=false){const r={type:"ArrowFunctionExpression",params:e,body:t,async:n,expression:null},i=a.ArrowFunctionExpression;return o(i.params,r,"params",e,1),o(i.body,r,"body",t,1),o(i.async,r,"async",n),r}function xe(e){const t={type:"ClassBody",body:e},n=a.ClassBody;return o(n.body,t,"body",e,1),t}function Ee(e=null,t=null,n,r=null){const i={type:"ClassExpression",id:e,superClass:t,body:n,decorators:r},c=a.ClassExpression;return o(c.id,i,"id",e,1),o(c.superClass,i,"superClass",t,1),o(c.body,i,"body",n,1),o(c.decorators,i,"decorators",r,1),i}function be(e=null,t=null,n,r=null){const i={type:"ClassDeclaration",id:e,superClass:t,body:n,decorators:r},c=a.ClassDeclaration;return o(c.id,i,"id",e,1),o(c.superClass,i,"superClass",t,1),o(c.body,i,"body",n,1),o(c.decorators,i,"decorators",r,1),i}function Ae(e){const t={type:"ExportAllDeclaration",source:e},n=a.ExportAllDeclaration;return o(n.source,t,"source",e,1),t}function ge(e){const t={type:"ExportDefaultDeclaration",declaration:e},n=a.ExportDefaultDeclaration;return o(n.declaration,t,"declaration",e,1),t}function Pe(e=null,t=[],n=null){const r={type:"ExportNamedDeclaration",declaration:e,specifiers:t,source:n},i=a.ExportNamedDeclaration;return o(i.declaration,r,"declaration",e,1),o(i.specifiers,r,"specifiers",t,1),o(i.source,r,"source",n,1),r}function De(e,t){const n={type:"ExportSpecifier",local:e,exported:t},r=a.ExportSpecifier;return o(r.local,n,"local",e,1),o(r.exported,n,"exported",t,1),n}function Ie(e,t,n,r=false){const i={type:"ForOfStatement",left:e,right:t,body:n,await:r},c=a.ForOfStatement;return o(c.left,i,"left",e,1),o(c.right,i,"right",t,1),o(c.body,i,"body",n,1),o(c.await,i,"await",r),i}function he(e,t){const n={type:"ImportDeclaration",specifiers:e,source:t},r=a.ImportDeclaration;return o(r.specifiers,n,"specifiers",e,1),o(r.source,n,"source",t,1),n}function je(e){const t={type:"ImportDefaultSpecifier",local:e},n=a.ImportDefaultSpecifier;return o(n.local,t,"local",e,1),t}function Me(e){const t={type:"ImportNamespaceSpecifier",local:e},n=a.ImportNamespaceSpecifier;return o(n.local,t,"local",e,1),t}function we(e,t){const n={type:"ImportSpecifier",local:e,imported:t},r=a.ImportSpecifier;return o(r.local,n,"local",e,1),o(r.imported,n,"imported",t,1),n}function Ce(e,t=null){const n={type:"ImportExpression",source:e,options:t},r=a.ImportExpression;return o(r.source,n,"source",e,1),o(r.options,n,"options",t,1),n}function Ne(e,t){const n={type:"MetaProperty",meta:e,property:t},r=a.MetaProperty;return o(r.meta,n,"meta",e,1),o(r.property,n,"property",t,1),n}function Le(e="method",t,n,r,i=false,c=false,p=false,l=false){const u={type:"ClassMethod",kind:e,key:t,params:n,body:r,computed:i,static:c,generator:p,async:l},d=a.ClassMethod;return o(d.kind,u,"kind",e),o(d.key,u,"key",t,1),o(d.params,u,"params",n,1),o(d.body,u,"body",r,1),o(d.computed,u,"computed",i),o(d.static,u,"static",c),o(d.generator,u,"generator",p),o(d.async,u,"async",l),u}function ve(e){const t={type:"ObjectPattern",properties:e},n=a.ObjectPattern;return o(n.properties,t,"properties",e,1),t}function S(e){const t={type:"SpreadElement",argument:e},n=a.SpreadElement;return o(n.argument,t,"argument",e,1),t}function Be(){return {type:"Super"}}function Oe(e,t){const n={type:"TaggedTemplateExpression",tag:e,quasi:t},r=a.TaggedTemplateExpression;return o(r.tag,n,"tag",e,1),o(r.quasi,n,"quasi",t,1),n}function Fe(e,t=false){const n={type:"TemplateElement",value:e,tail:t},r=a.TemplateElement;return o(r.value,n,"value",e),o(r.tail,n,"tail",t),n}function Ke(e,t){const n={type:"TemplateLiteral",quasis:e,expressions:t},r=a.TemplateLiteral;return o(r.quasis,n,"quasis",e,1),o(r.expressions,n,"expressions",t,1),n}function ke(e=null,t=false){const n={type:"YieldExpression",argument:e,delegate:t},r=a.YieldExpression;return o(r.argument,n,"argument",e,1),o(r.delegate,n,"delegate",t),n}function Xe(e){const t={type:"AwaitExpression",argument:e},n=a.AwaitExpression;return o(n.argument,t,"argument",e,1),t}function Re(){return {type:"Import"}}function qe(e){const t={type:"BigIntLiteral",value:e},n=a.BigIntLiteral;return o(n.value,t,"value",e),t}function Je(e){const t={type:"ExportNamespaceSpecifier",exported:e},n=a.ExportNamespaceSpecifier;return o(n.exported,t,"exported",e,1),t}function Ue(e,t,n=false,r){const i={type:"OptionalMemberExpression",object:e,property:t,computed:n,optional:r},c=a.OptionalMemberExpression;return o(c.object,i,"object",e,1),o(c.property,i,"property",t,1),o(c.computed,i,"computed",n),o(c.optional,i,"optional",r),i}function Ve(e,t,n){const r={type:"OptionalCallExpression",callee:e,arguments:t,optional:n},i=a.OptionalCallExpression;return o(i.callee,r,"callee",e,1),o(i.arguments,r,"arguments",t,1),o(i.optional,r,"optional",n),r}function We(e,t=null,n=null,r=null,i=false,c=false){const p={type:"ClassProperty",key:e,value:t,typeAnnotation:n,decorators:r,computed:i,static:c},l=a.ClassProperty;return o(l.key,p,"key",e,1),o(l.value,p,"value",t,1),o(l.typeAnnotation,p,"typeAnnotation",n,1),o(l.decorators,p,"decorators",r,1),o(l.computed,p,"computed",i),o(l.static,p,"static",c),p}function Qe(e,t=null,n=null,r=null,i=false,c=false){const p={type:"ClassAccessorProperty",key:e,value:t,typeAnnotation:n,decorators:r,computed:i,static:c},l=a.ClassAccessorProperty;return o(l.key,p,"key",e,1),o(l.value,p,"value",t,1),o(l.typeAnnotation,p,"typeAnnotation",n,1),o(l.decorators,p,"decorators",r,1),o(l.computed,p,"computed",i),o(l.static,p,"static",c),p}function ze(e,t=null,n=null,r=false){const i={type:"ClassPrivateProperty",key:e,value:t,decorators:n,static:r},c=a.ClassPrivateProperty;return o(c.key,i,"key",e,1),o(c.value,i,"value",t,1),o(c.decorators,i,"decorators",n,1),o(c.static,i,"static",r),i}function $e(e="method",t,n,r,i=false){const c={type:"ClassPrivateMethod",kind:e,key:t,params:n,body:r,static:i},p=a.ClassPrivateMethod;return o(p.kind,c,"kind",e),o(p.key,c,"key",t,1),o(p.params,c,"params",n,1),o(p.body,c,"body",r,1),o(p.static,c,"static",i),c}function Ge(e){const t={type:"PrivateName",id:e},n=a.PrivateName;return o(n.id,t,"id",e,1),t}function Ye(e){const t={type:"StaticBlock",body:e},n=a.StaticBlock;return o(n.body,t,"body",e,1),t}function He(){return {type:"AnyTypeAnnotation"}}function Ze(e){const t={type:"ArrayTypeAnnotation",elementType:e},n=a.ArrayTypeAnnotation;return o(n.elementType,t,"elementType",e,1),t}function _e(){return {type:"BooleanTypeAnnotation"}}function et(e){const t={type:"BooleanLiteralTypeAnnotation",value:e},n=a.BooleanLiteralTypeAnnotation;return o(n.value,t,"value",e),t}function tt(){return {type:"NullLiteralTypeAnnotation"}}function nt(e,t=null){const n={type:"ClassImplements",id:e,typeParameters:t},r=a.ClassImplements;return o(r.id,n,"id",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function ot(e,t=null,n=null,r){const i={type:"DeclareClass",id:e,typeParameters:t,extends:n,body:r},c=a.DeclareClass;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.extends,i,"extends",n,1),o(c.body,i,"body",r,1),i}function rt(e){const t={type:"DeclareFunction",id:e},n=a.DeclareFunction;return o(n.id,t,"id",e,1),t}function st(e,t=null,n=null,r){const i={type:"DeclareInterface",id:e,typeParameters:t,extends:n,body:r},c=a.DeclareInterface;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.extends,i,"extends",n,1),o(c.body,i,"body",r,1),i}function it(e,t,n=null){const r={type:"DeclareModule",id:e,body:t,kind:n},i=a.DeclareModule;return o(i.id,r,"id",e,1),o(i.body,r,"body",t,1),o(i.kind,r,"kind",n),r}function at(e){const t={type:"DeclareModuleExports",typeAnnotation:e},n=a.DeclareModuleExports;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function ct(e,t=null,n){const r={type:"DeclareTypeAlias",id:e,typeParameters:t,right:n},i=a.DeclareTypeAlias;return o(i.id,r,"id",e,1),o(i.typeParameters,r,"typeParameters",t,1),o(i.right,r,"right",n,1),r}function pt(e,t=null,n=null){const r={type:"DeclareOpaqueType",id:e,typeParameters:t,supertype:n},i=a.DeclareOpaqueType;return o(i.id,r,"id",e,1),o(i.typeParameters,r,"typeParameters",t,1),o(i.supertype,r,"supertype",n,1),r}function lt(e){const t={type:"DeclareVariable",id:e},n=a.DeclareVariable;return o(n.id,t,"id",e,1),t}function ut(e=null,t=null,n=null,r=null){const i={type:"DeclareExportDeclaration",declaration:e,specifiers:t,source:n,attributes:r},c=a.DeclareExportDeclaration;return o(c.declaration,i,"declaration",e,1),o(c.specifiers,i,"specifiers",t,1),o(c.source,i,"source",n,1),o(c.attributes,i,"attributes",r,1),i}function dt(e,t=null){const n={type:"DeclareExportAllDeclaration",source:e,attributes:t},r=a.DeclareExportAllDeclaration;return o(r.source,n,"source",e,1),o(r.attributes,n,"attributes",t,1),n}function yt(e){const t={type:"DeclaredPredicate",value:e},n=a.DeclaredPredicate;return o(n.value,t,"value",e,1),t}function ft(){return {type:"ExistsTypeAnnotation"}}function mt(e=null,t,n=null,r){const i={type:"FunctionTypeAnnotation",typeParameters:e,params:t,rest:n,returnType:r},c=a.FunctionTypeAnnotation;return o(c.typeParameters,i,"typeParameters",e,1),o(c.params,i,"params",t,1),o(c.rest,i,"rest",n,1),o(c.returnType,i,"returnType",r,1),i}function Tt(e=null,t){const n={type:"FunctionTypeParam",name:e,typeAnnotation:t},r=a.FunctionTypeParam;return o(r.name,n,"name",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function St(e,t=null){const n={type:"GenericTypeAnnotation",id:e,typeParameters:t},r=a.GenericTypeAnnotation;return o(r.id,n,"id",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function xt(){return {type:"InferredPredicate"}}function Et(e,t=null){const n={type:"InterfaceExtends",id:e,typeParameters:t},r=a.InterfaceExtends;return o(r.id,n,"id",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function bt(e,t=null,n=null,r){const i={type:"InterfaceDeclaration",id:e,typeParameters:t,extends:n,body:r},c=a.InterfaceDeclaration;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.extends,i,"extends",n,1),o(c.body,i,"body",r,1),i}function At(e=null,t){const n={type:"InterfaceTypeAnnotation",extends:e,body:t},r=a.InterfaceTypeAnnotation;return o(r.extends,n,"extends",e,1),o(r.body,n,"body",t,1),n}function gt(e){const t={type:"IntersectionTypeAnnotation",types:e},n=a.IntersectionTypeAnnotation;return o(n.types,t,"types",e,1),t}function Pt(){return {type:"MixedTypeAnnotation"}}function Dt(){return {type:"EmptyTypeAnnotation"}}function It(e){const t={type:"NullableTypeAnnotation",typeAnnotation:e},n=a.NullableTypeAnnotation;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function ht(e){const t={type:"NumberLiteralTypeAnnotation",value:e},n=a.NumberLiteralTypeAnnotation;return o(n.value,t,"value",e),t}function jt(){return {type:"NumberTypeAnnotation"}}function Mt(e,t=[],n=[],r=[],i=false){const c={type:"ObjectTypeAnnotation",properties:e,indexers:t,callProperties:n,internalSlots:r,exact:i},p=a.ObjectTypeAnnotation;return o(p.properties,c,"properties",e,1),o(p.indexers,c,"indexers",t,1),o(p.callProperties,c,"callProperties",n,1),o(p.internalSlots,c,"internalSlots",r,1),o(p.exact,c,"exact",i),c}function wt(e,t,n,r,i){const c={type:"ObjectTypeInternalSlot",id:e,value:t,optional:n,static:r,method:i},p=a.ObjectTypeInternalSlot;return o(p.id,c,"id",e,1),o(p.value,c,"value",t,1),o(p.optional,c,"optional",n),o(p.static,c,"static",r),o(p.method,c,"method",i),c}function Ct(e){const t={type:"ObjectTypeCallProperty",value:e,static:null},n=a.ObjectTypeCallProperty;return o(n.value,t,"value",e,1),t}function Nt(e=null,t,n,r=null){const i={type:"ObjectTypeIndexer",id:e,key:t,value:n,variance:r,static:null},c=a.ObjectTypeIndexer;return o(c.id,i,"id",e,1),o(c.key,i,"key",t,1),o(c.value,i,"value",n,1),o(c.variance,i,"variance",r,1),i}function Lt(e,t,n=null){const r={type:"ObjectTypeProperty",key:e,value:t,variance:n,kind:null,method:null,optional:null,proto:null,static:null},i=a.ObjectTypeProperty;return o(i.key,r,"key",e,1),o(i.value,r,"value",t,1),o(i.variance,r,"variance",n,1),r}function vt(e){const t={type:"ObjectTypeSpreadProperty",argument:e},n=a.ObjectTypeSpreadProperty;return o(n.argument,t,"argument",e,1),t}function Bt(e,t=null,n=null,r){const i={type:"OpaqueType",id:e,typeParameters:t,supertype:n,impltype:r},c=a.OpaqueType;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.supertype,i,"supertype",n,1),o(c.impltype,i,"impltype",r,1),i}function Ot(e,t){const n={type:"QualifiedTypeIdentifier",id:e,qualification:t},r=a.QualifiedTypeIdentifier;return o(r.id,n,"id",e,1),o(r.qualification,n,"qualification",t,1),n}function Ft(e){const t={type:"StringLiteralTypeAnnotation",value:e},n=a.StringLiteralTypeAnnotation;return o(n.value,t,"value",e),t}function Kt(){return {type:"StringTypeAnnotation"}}function kt(){return {type:"SymbolTypeAnnotation"}}function Xt(){return {type:"ThisTypeAnnotation"}}function Rt(e){const t={type:"TupleTypeAnnotation",types:e},n=a.TupleTypeAnnotation;return o(n.types,t,"types",e,1),t}function qt(e){const t={type:"TypeofTypeAnnotation",argument:e},n=a.TypeofTypeAnnotation;return o(n.argument,t,"argument",e,1),t}function Jt(e,t=null,n){const r={type:"TypeAlias",id:e,typeParameters:t,right:n},i=a.TypeAlias;return o(i.id,r,"id",e,1),o(i.typeParameters,r,"typeParameters",t,1),o(i.right,r,"right",n,1),r}function Ut(e){const t={type:"TypeAnnotation",typeAnnotation:e},n=a.TypeAnnotation;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function Vt(e,t){const n={type:"TypeCastExpression",expression:e,typeAnnotation:t},r=a.TypeCastExpression;return o(r.expression,n,"expression",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function Wt(e=null,t=null,n=null){const r={type:"TypeParameter",bound:e,default:t,variance:n,name:null},i=a.TypeParameter;return o(i.bound,r,"bound",e,1),o(i.default,r,"default",t,1),o(i.variance,r,"variance",n,1),r}function Qt(e){const t={type:"TypeParameterDeclaration",params:e},n=a.TypeParameterDeclaration;return o(n.params,t,"params",e,1),t}function zt(e){const t={type:"TypeParameterInstantiation",params:e},n=a.TypeParameterInstantiation;return o(n.params,t,"params",e,1),t}function $t(e){const t={type:"UnionTypeAnnotation",types:e},n=a.UnionTypeAnnotation;return o(n.types,t,"types",e,1),t}function Gt(e){const t={type:"Variance",kind:e},n=a.Variance;return o(n.kind,t,"kind",e),t}function Yt(){return {type:"VoidTypeAnnotation"}}function Ht(e,t){const n={type:"EnumDeclaration",id:e,body:t},r=a.EnumDeclaration;return o(r.id,n,"id",e,1),o(r.body,n,"body",t,1),n}function Zt(e){const t={type:"EnumBooleanBody",members:e,explicitType:null,hasUnknownMembers:null},n=a.EnumBooleanBody;return o(n.members,t,"members",e,1),t}function _t(e){const t={type:"EnumNumberBody",members:e,explicitType:null,hasUnknownMembers:null},n=a.EnumNumberBody;return o(n.members,t,"members",e,1),t}function en(e){const t={type:"EnumStringBody",members:e,explicitType:null,hasUnknownMembers:null},n=a.EnumStringBody;return o(n.members,t,"members",e,1),t}function tn(e){const t={type:"EnumSymbolBody",members:e,hasUnknownMembers:null},n=a.EnumSymbolBody;return o(n.members,t,"members",e,1),t}function nn(e){const t={type:"EnumBooleanMember",id:e,init:null},n=a.EnumBooleanMember;return o(n.id,t,"id",e,1),t}function on(e,t){const n={type:"EnumNumberMember",id:e,init:t},r=a.EnumNumberMember;return o(r.id,n,"id",e,1),o(r.init,n,"init",t,1),n}function rn(e,t){const n={type:"EnumStringMember",id:e,init:t},r=a.EnumStringMember;return o(r.id,n,"id",e,1),o(r.init,n,"init",t,1),n}function sn(e){const t={type:"EnumDefaultedMember",id:e},n=a.EnumDefaultedMember;return o(n.id,t,"id",e,1),t}function an(e,t){const n={type:"IndexedAccessType",objectType:e,indexType:t},r=a.IndexedAccessType;return o(r.objectType,n,"objectType",e,1),o(r.indexType,n,"indexType",t,1),n}function cn(e,t){const n={type:"OptionalIndexedAccessType",objectType:e,indexType:t,optional:null},r=a.OptionalIndexedAccessType;return o(r.objectType,n,"objectType",e,1),o(r.indexType,n,"indexType",t,1),n}function pn(e,t=null){const n={type:"JSXAttribute",name:e,value:t},r=a.JSXAttribute;return o(r.name,n,"name",e,1),o(r.value,n,"value",t,1),n}function ln(e){const t={type:"JSXClosingElement",name:e},n=a.JSXClosingElement;return o(n.name,t,"name",e,1),t}function un(e,t=null,n,r=null){const i={type:"JSXElement",openingElement:e,closingElement:t,children:n,selfClosing:r},c=a.JSXElement;return o(c.openingElement,i,"openingElement",e,1),o(c.closingElement,i,"closingElement",t,1),o(c.children,i,"children",n,1),o(c.selfClosing,i,"selfClosing",r),i}function dn(){return {type:"JSXEmptyExpression"}}function yn(e){const t={type:"JSXExpressionContainer",expression:e},n=a.JSXExpressionContainer;return o(n.expression,t,"expression",e,1),t}function fn(e){const t={type:"JSXSpreadChild",expression:e},n=a.JSXSpreadChild;return o(n.expression,t,"expression",e,1),t}function mn(e){const t={type:"JSXIdentifier",name:e},n=a.JSXIdentifier;return o(n.name,t,"name",e),t}function Tn(e,t){const n={type:"JSXMemberExpression",object:e,property:t},r=a.JSXMemberExpression;return o(r.object,n,"object",e,1),o(r.property,n,"property",t,1),n}function Sn(e,t){const n={type:"JSXNamespacedName",namespace:e,name:t},r=a.JSXNamespacedName;return o(r.namespace,n,"namespace",e,1),o(r.name,n,"name",t,1),n}function xn(e,t,n=false){const r={type:"JSXOpeningElement",name:e,attributes:t,selfClosing:n},i=a.JSXOpeningElement;return o(i.name,r,"name",e,1),o(i.attributes,r,"attributes",t,1),o(i.selfClosing,r,"selfClosing",n),r}function En(e){const t={type:"JSXSpreadAttribute",argument:e},n=a.JSXSpreadAttribute;return o(n.argument,t,"argument",e,1),t}function bn(e){const t={type:"JSXText",value:e},n=a.JSXText;return o(n.value,t,"value",e),t}function An(e,t,n){const r={type:"JSXFragment",openingFragment:e,closingFragment:t,children:n},i=a.JSXFragment;return o(i.openingFragment,r,"openingFragment",e,1),o(i.closingFragment,r,"closingFragment",t,1),o(i.children,r,"children",n,1),r}function gn(){return {type:"JSXOpeningFragment"}}function Pn(){return {type:"JSXClosingFragment"}}function Dn(){return {type:"Noop"}}function In(e,t){const n={type:"Placeholder",expectedNode:e,name:t},r=a.Placeholder;return o(r.expectedNode,n,"expectedNode",e),o(r.name,n,"name",t,1),n}function hn(e){const t={type:"V8IntrinsicIdentifier",name:e},n=a.V8IntrinsicIdentifier;return o(n.name,t,"name",e),t}function jn(){return {type:"ArgumentPlaceholder"}}function Mn(e,t){const n={type:"BindExpression",object:e,callee:t},r=a.BindExpression;return o(r.object,n,"object",e,1),o(r.callee,n,"callee",t,1),n}function wn(e,t){const n={type:"ImportAttribute",key:e,value:t},r=a.ImportAttribute;return o(r.key,n,"key",e,1),o(r.value,n,"value",t,1),n}function Cn(e){const t={type:"Decorator",expression:e},n=a.Decorator;return o(n.expression,t,"expression",e,1),t}function Nn(e,t=false){const n={type:"DoExpression",body:e,async:t},r=a.DoExpression;return o(r.body,n,"body",e,1),o(r.async,n,"async",t),n}function Ln(e){const t={type:"ExportDefaultSpecifier",exported:e},n=a.ExportDefaultSpecifier;return o(n.exported,t,"exported",e,1),t}function vn(e){const t={type:"RecordExpression",properties:e},n=a.RecordExpression;return o(n.properties,t,"properties",e,1),t}function Bn(e=[]){const t={type:"TupleExpression",elements:e},n=a.TupleExpression;return o(n.elements,t,"elements",e,1),t}function On(e){const t={type:"DecimalLiteral",value:e},n=a.DecimalLiteral;return o(n.value,t,"value",e),t}function Fn(e){const t={type:"ModuleExpression",body:e},n=a.ModuleExpression;return o(n.body,t,"body",e,1),t}function Kn(){return {type:"TopicReference"}}function kn(e){const t={type:"PipelineTopicExpression",expression:e},n=a.PipelineTopicExpression;return o(n.expression,t,"expression",e,1),t}function Xn(e){const t={type:"PipelineBareFunction",callee:e},n=a.PipelineBareFunction;return o(n.callee,t,"callee",e,1),t}function Rn(){return {type:"PipelinePrimaryTopicReference"}}function qn(e){const t={type:"TSParameterProperty",parameter:e},n=a.TSParameterProperty;return o(n.parameter,t,"parameter",e,1),t}function Jn(e=null,t=null,n,r=null){const i={type:"TSDeclareFunction",id:e,typeParameters:t,params:n,returnType:r},c=a.TSDeclareFunction;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.params,i,"params",n,1),o(c.returnType,i,"returnType",r,1),i}function Un(e=null,t,n=null,r,i=null){const c={type:"TSDeclareMethod",decorators:e,key:t,typeParameters:n,params:r,returnType:i},p=a.TSDeclareMethod;return o(p.decorators,c,"decorators",e,1),o(p.key,c,"key",t,1),o(p.typeParameters,c,"typeParameters",n,1),o(p.params,c,"params",r,1),o(p.returnType,c,"returnType",i,1),c}function Vn(e,t){const n={type:"TSQualifiedName",left:e,right:t},r=a.TSQualifiedName;return o(r.left,n,"left",e,1),o(r.right,n,"right",t,1),n}function Wn(e=null,t,n=null){const r={type:"TSCallSignatureDeclaration",typeParameters:e,parameters:t,typeAnnotation:n},i=a.TSCallSignatureDeclaration;return o(i.typeParameters,r,"typeParameters",e,1),o(i.parameters,r,"parameters",t,1),o(i.typeAnnotation,r,"typeAnnotation",n,1),r}function Qn(e=null,t,n=null){const r={type:"TSConstructSignatureDeclaration",typeParameters:e,parameters:t,typeAnnotation:n},i=a.TSConstructSignatureDeclaration;return o(i.typeParameters,r,"typeParameters",e,1),o(i.parameters,r,"parameters",t,1),o(i.typeAnnotation,r,"typeAnnotation",n,1),r}function zn(e,t=null){const n={type:"TSPropertySignature",key:e,typeAnnotation:t},r=a.TSPropertySignature;return o(r.key,n,"key",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function $n(e,t=null,n,r=null){const i={type:"TSMethodSignature",key:e,typeParameters:t,parameters:n,typeAnnotation:r,kind:null},c=a.TSMethodSignature;return o(c.key,i,"key",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.parameters,i,"parameters",n,1),o(c.typeAnnotation,i,"typeAnnotation",r,1),i}function Gn(e,t=null){const n={type:"TSIndexSignature",parameters:e,typeAnnotation:t},r=a.TSIndexSignature;return o(r.parameters,n,"parameters",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function Yn(){return {type:"TSAnyKeyword"}}function Hn(){return {type:"TSBooleanKeyword"}}function Zn(){return {type:"TSBigIntKeyword"}}function _n(){return {type:"TSIntrinsicKeyword"}}function eo(){return {type:"TSNeverKeyword"}}function to(){return {type:"TSNullKeyword"}}function no(){return {type:"TSNumberKeyword"}}function oo(){return {type:"TSObjectKeyword"}}function ro(){return {type:"TSStringKeyword"}}function so(){return {type:"TSSymbolKeyword"}}function io(){return {type:"TSUndefinedKeyword"}}function ao(){return {type:"TSUnknownKeyword"}}function co(){return {type:"TSVoidKeyword"}}function po(){return {type:"TSThisType"}}function lo(e=null,t,n=null){const r={type:"TSFunctionType",typeParameters:e,parameters:t,typeAnnotation:n},i=a.TSFunctionType;return o(i.typeParameters,r,"typeParameters",e,1),o(i.parameters,r,"parameters",t,1),o(i.typeAnnotation,r,"typeAnnotation",n,1),r}function uo(e=null,t,n=null){const r={type:"TSConstructorType",typeParameters:e,parameters:t,typeAnnotation:n},i=a.TSConstructorType;return o(i.typeParameters,r,"typeParameters",e,1),o(i.parameters,r,"parameters",t,1),o(i.typeAnnotation,r,"typeAnnotation",n,1),r}function yo(e,t=null){const n={type:"TSTypeReference",typeName:e,typeParameters:t},r=a.TSTypeReference;return o(r.typeName,n,"typeName",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function fo(e,t=null,n=null){const r={type:"TSTypePredicate",parameterName:e,typeAnnotation:t,asserts:n},i=a.TSTypePredicate;return o(i.parameterName,r,"parameterName",e,1),o(i.typeAnnotation,r,"typeAnnotation",t,1),o(i.asserts,r,"asserts",n),r}function mo(e,t=null){const n={type:"TSTypeQuery",exprName:e,typeParameters:t},r=a.TSTypeQuery;return o(r.exprName,n,"exprName",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function To(e){const t={type:"TSTypeLiteral",members:e},n=a.TSTypeLiteral;return o(n.members,t,"members",e,1),t}function So(e){const t={type:"TSArrayType",elementType:e},n=a.TSArrayType;return o(n.elementType,t,"elementType",e,1),t}function xo(e){const t={type:"TSTupleType",elementTypes:e},n=a.TSTupleType;return o(n.elementTypes,t,"elementTypes",e,1),t}function Eo(e){const t={type:"TSOptionalType",typeAnnotation:e},n=a.TSOptionalType;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function bo(e){const t={type:"TSRestType",typeAnnotation:e},n=a.TSRestType;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function Ao(e,t,n=false){const r={type:"TSNamedTupleMember",label:e,elementType:t,optional:n},i=a.TSNamedTupleMember;return o(i.label,r,"label",e,1),o(i.elementType,r,"elementType",t,1),o(i.optional,r,"optional",n),r}function go(e){const t={type:"TSUnionType",types:e},n=a.TSUnionType;return o(n.types,t,"types",e,1),t}function Po(e){const t={type:"TSIntersectionType",types:e},n=a.TSIntersectionType;return o(n.types,t,"types",e,1),t}function Do(e,t,n,r){const i={type:"TSConditionalType",checkType:e,extendsType:t,trueType:n,falseType:r},c=a.TSConditionalType;return o(c.checkType,i,"checkType",e,1),o(c.extendsType,i,"extendsType",t,1),o(c.trueType,i,"trueType",n,1),o(c.falseType,i,"falseType",r,1),i}function Io(e){const t={type:"TSInferType",typeParameter:e},n=a.TSInferType;return o(n.typeParameter,t,"typeParameter",e,1),t}function ho(e){const t={type:"TSParenthesizedType",typeAnnotation:e},n=a.TSParenthesizedType;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function jo(e){const t={type:"TSTypeOperator",typeAnnotation:e,operator:null},n=a.TSTypeOperator;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function Mo(e,t){const n={type:"TSIndexedAccessType",objectType:e,indexType:t},r=a.TSIndexedAccessType;return o(r.objectType,n,"objectType",e,1),o(r.indexType,n,"indexType",t,1),n}function wo(e,t=null,n=null){const r={type:"TSMappedType",typeParameter:e,typeAnnotation:t,nameType:n},i=a.TSMappedType;return o(i.typeParameter,r,"typeParameter",e,1),o(i.typeAnnotation,r,"typeAnnotation",t,1),o(i.nameType,r,"nameType",n,1),r}function Co(e,t){const n={type:"TSTemplateLiteralType",quasis:e,types:t},r=a.TSTemplateLiteralType;return o(r.quasis,n,"quasis",e,1),o(r.types,n,"types",t,1),n}function No(e){const t={type:"TSLiteralType",literal:e},n=a.TSLiteralType;return o(n.literal,t,"literal",e,1),t}function Lo(e,t=null){const n={type:"TSExpressionWithTypeArguments",expression:e,typeParameters:t},r=a.TSExpressionWithTypeArguments;return o(r.expression,n,"expression",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function vo(e,t=null,n=null,r){const i={type:"TSInterfaceDeclaration",id:e,typeParameters:t,extends:n,body:r},c=a.TSInterfaceDeclaration;return o(c.id,i,"id",e,1),o(c.typeParameters,i,"typeParameters",t,1),o(c.extends,i,"extends",n,1),o(c.body,i,"body",r,1),i}function Bo(e){const t={type:"TSInterfaceBody",body:e},n=a.TSInterfaceBody;return o(n.body,t,"body",e,1),t}function Oo(e,t=null,n){const r={type:"TSTypeAliasDeclaration",id:e,typeParameters:t,typeAnnotation:n},i=a.TSTypeAliasDeclaration;return o(i.id,r,"id",e,1),o(i.typeParameters,r,"typeParameters",t,1),o(i.typeAnnotation,r,"typeAnnotation",n,1),r}function Fo(e,t=null){const n={type:"TSInstantiationExpression",expression:e,typeParameters:t},r=a.TSInstantiationExpression;return o(r.expression,n,"expression",e,1),o(r.typeParameters,n,"typeParameters",t,1),n}function Ko(e,t){const n={type:"TSAsExpression",expression:e,typeAnnotation:t},r=a.TSAsExpression;return o(r.expression,n,"expression",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function ko(e,t){const n={type:"TSSatisfiesExpression",expression:e,typeAnnotation:t},r=a.TSSatisfiesExpression;return o(r.expression,n,"expression",e,1),o(r.typeAnnotation,n,"typeAnnotation",t,1),n}function Xo(e,t){const n={type:"TSTypeAssertion",typeAnnotation:e,expression:t},r=a.TSTypeAssertion;return o(r.typeAnnotation,n,"typeAnnotation",e,1),o(r.expression,n,"expression",t,1),n}function Ro(e){const t={type:"TSEnumBody",members:e},n=a.TSEnumBody;return o(n.members,t,"members",e,1),t}function qo(e,t){const n={type:"TSEnumDeclaration",id:e,members:t},r=a.TSEnumDeclaration;return o(r.id,n,"id",e,1),o(r.members,n,"members",t,1),n}function Jo(e,t=null){const n={type:"TSEnumMember",id:e,initializer:t},r=a.TSEnumMember;return o(r.id,n,"id",e,1),o(r.initializer,n,"initializer",t,1),n}function Uo(e,t){const n={type:"TSModuleDeclaration",id:e,body:t,kind:null},r=a.TSModuleDeclaration;return o(r.id,n,"id",e,1),o(r.body,n,"body",t,1),n}function Vo(e){const t={type:"TSModuleBlock",body:e},n=a.TSModuleBlock;return o(n.body,t,"body",e,1),t}function Wo(e,t=null,n=null){const r={type:"TSImportType",argument:e,qualifier:t,typeParameters:n},i=a.TSImportType;return o(i.argument,r,"argument",e,1),o(i.qualifier,r,"qualifier",t,1),o(i.typeParameters,r,"typeParameters",n,1),r}function Qo(e,t){const n={type:"TSImportEqualsDeclaration",id:e,moduleReference:t,isExport:null},r=a.TSImportEqualsDeclaration;return o(r.id,n,"id",e,1),o(r.moduleReference,n,"moduleReference",t,1),n}function zo(e){const t={type:"TSExternalModuleReference",expression:e},n=a.TSExternalModuleReference;return o(n.expression,t,"expression",e,1),t}function $o(e){const t={type:"TSNonNullExpression",expression:e},n=a.TSNonNullExpression;return o(n.expression,t,"expression",e,1),t}function Go(e){const t={type:"TSExportAssignment",expression:e},n=a.TSExportAssignment;return o(n.expression,t,"expression",e,1),t}function Yo(e){const t={type:"TSNamespaceExportDeclaration",id:e},n=a.TSNamespaceExportDeclaration;return o(n.id,t,"id",e,1),t}function Ho(e){const t={type:"TSTypeAnnotation",typeAnnotation:e},n=a.TSTypeAnnotation;return o(n.typeAnnotation,t,"typeAnnotation",e,1),t}function Zo(e){const t={type:"TSTypeParameterInstantiation",params:e},n=a.TSTypeParameterInstantiation;return o(n.params,t,"params",e,1),t}function _o(e){const t={type:"TSTypeParameterDeclaration",params:e},n=a.TSTypeParameterDeclaration;return o(n.params,t,"params",e,1),t}function er(e=null,t=null,n){const r={type:"TSTypeParameter",constraint:e,default:t,name:n},i=a.TSTypeParameter;return o(i.constraint,r,"constraint",e,1),o(i.default,r,"default",t,1),o(i.name,r,"name",n),r}function tr(e){return (0, y.default)("NumberLiteral","NumericLiteral","The node type "),f(e)}function nr(e,t=""){return (0, y.default)("RegexLiteral","RegExpLiteral","The node type "),m(e,t)}function or(e){return (0, y.default)("RestProperty","RestElement","The node type "),T(e)}function rr(e){return (0, y.default)("SpreadProperty","SpreadElement","The node type "),S(e)}return e$M}

var e$D={};

var r$g;function i$e(){return r$g?e$D:(r$g=1,function(e){Object.defineProperty(e,"__esModule",{value:true}),Object.defineProperty(e,"AnyTypeAnnotation",{enumerable:true,get:function(){return t.anyTypeAnnotation}}),Object.defineProperty(e,"ArgumentPlaceholder",{enumerable:true,get:function(){return t.argumentPlaceholder}}),Object.defineProperty(e,"ArrayExpression",{enumerable:true,get:function(){return t.arrayExpression}}),Object.defineProperty(e,"ArrayPattern",{enumerable:true,get:function(){return t.arrayPattern}}),Object.defineProperty(e,"ArrayTypeAnnotation",{enumerable:true,get:function(){return t.arrayTypeAnnotation}}),Object.defineProperty(e,"ArrowFunctionExpression",{enumerable:true,get:function(){return t.arrowFunctionExpression}}),Object.defineProperty(e,"AssignmentExpression",{enumerable:true,get:function(){return t.assignmentExpression}}),Object.defineProperty(e,"AssignmentPattern",{enumerable:true,get:function(){return t.assignmentPattern}}),Object.defineProperty(e,"AwaitExpression",{enumerable:true,get:function(){return t.awaitExpression}}),Object.defineProperty(e,"BigIntLiteral",{enumerable:true,get:function(){return t.bigIntLiteral}}),Object.defineProperty(e,"BinaryExpression",{enumerable:true,get:function(){return t.binaryExpression}}),Object.defineProperty(e,"BindExpression",{enumerable:true,get:function(){return t.bindExpression}}),Object.defineProperty(e,"BlockStatement",{enumerable:true,get:function(){return t.blockStatement}}),Object.defineProperty(e,"BooleanLiteral",{enumerable:true,get:function(){return t.booleanLiteral}}),Object.defineProperty(e,"BooleanLiteralTypeAnnotation",{enumerable:true,get:function(){return t.booleanLiteralTypeAnnotation}}),Object.defineProperty(e,"BooleanTypeAnnotation",{enumerable:true,get:function(){return t.booleanTypeAnnotation}}),Object.defineProperty(e,"BreakStatement",{enumerable:true,get:function(){return t.breakStatement}}),Object.defineProperty(e,"CallExpression",{enumerable:true,get:function(){return t.callExpression}}),Object.defineProperty(e,"CatchClause",{enumerable:true,get:function(){return t.catchClause}}),Object.defineProperty(e,"ClassAccessorProperty",{enumerable:true,get:function(){return t.classAccessorProperty}}),Object.defineProperty(e,"ClassBody",{enumerable:true,get:function(){return t.classBody}}),Object.defineProperty(e,"ClassDeclaration",{enumerable:true,get:function(){return t.classDeclaration}}),Object.defineProperty(e,"ClassExpression",{enumerable:true,get:function(){return t.classExpression}}),Object.defineProperty(e,"ClassImplements",{enumerable:true,get:function(){return t.classImplements}}),Object.defineProperty(e,"ClassMethod",{enumerable:true,get:function(){return t.classMethod}}),Object.defineProperty(e,"ClassPrivateMethod",{enumerable:true,get:function(){return t.classPrivateMethod}}),Object.defineProperty(e,"ClassPrivateProperty",{enumerable:true,get:function(){return t.classPrivateProperty}}),Object.defineProperty(e,"ClassProperty",{enumerable:true,get:function(){return t.classProperty}}),Object.defineProperty(e,"ConditionalExpression",{enumerable:true,get:function(){return t.conditionalExpression}}),Object.defineProperty(e,"ContinueStatement",{enumerable:true,get:function(){return t.continueStatement}}),Object.defineProperty(e,"DebuggerStatement",{enumerable:true,get:function(){return t.debuggerStatement}}),Object.defineProperty(e,"DecimalLiteral",{enumerable:true,get:function(){return t.decimalLiteral}}),Object.defineProperty(e,"DeclareClass",{enumerable:true,get:function(){return t.declareClass}}),Object.defineProperty(e,"DeclareExportAllDeclaration",{enumerable:true,get:function(){return t.declareExportAllDeclaration}}),Object.defineProperty(e,"DeclareExportDeclaration",{enumerable:true,get:function(){return t.declareExportDeclaration}}),Object.defineProperty(e,"DeclareFunction",{enumerable:true,get:function(){return t.declareFunction}}),Object.defineProperty(e,"DeclareInterface",{enumerable:true,get:function(){return t.declareInterface}}),Object.defineProperty(e,"DeclareModule",{enumerable:true,get:function(){return t.declareModule}}),Object.defineProperty(e,"DeclareModuleExports",{enumerable:true,get:function(){return t.declareModuleExports}}),Object.defineProperty(e,"DeclareOpaqueType",{enumerable:true,get:function(){return t.declareOpaqueType}}),Object.defineProperty(e,"DeclareTypeAlias",{enumerable:true,get:function(){return t.declareTypeAlias}}),Object.defineProperty(e,"DeclareVariable",{enumerable:true,get:function(){return t.declareVariable}}),Object.defineProperty(e,"DeclaredPredicate",{enumerable:true,get:function(){return t.declaredPredicate}}),Object.defineProperty(e,"Decorator",{enumerable:true,get:function(){return t.decorator}}),Object.defineProperty(e,"Directive",{enumerable:true,get:function(){return t.directive}}),Object.defineProperty(e,"DirectiveLiteral",{enumerable:true,get:function(){return t.directiveLiteral}}),Object.defineProperty(e,"DoExpression",{enumerable:true,get:function(){return t.doExpression}}),Object.defineProperty(e,"DoWhileStatement",{enumerable:true,get:function(){return t.doWhileStatement}}),Object.defineProperty(e,"EmptyStatement",{enumerable:true,get:function(){return t.emptyStatement}}),Object.defineProperty(e,"EmptyTypeAnnotation",{enumerable:true,get:function(){return t.emptyTypeAnnotation}}),Object.defineProperty(e,"EnumBooleanBody",{enumerable:true,get:function(){return t.enumBooleanBody}}),Object.defineProperty(e,"EnumBooleanMember",{enumerable:true,get:function(){return t.enumBooleanMember}}),Object.defineProperty(e,"EnumDeclaration",{enumerable:true,get:function(){return t.enumDeclaration}}),Object.defineProperty(e,"EnumDefaultedMember",{enumerable:true,get:function(){return t.enumDefaultedMember}}),Object.defineProperty(e,"EnumNumberBody",{enumerable:true,get:function(){return t.enumNumberBody}}),Object.defineProperty(e,"EnumNumberMember",{enumerable:true,get:function(){return t.enumNumberMember}}),Object.defineProperty(e,"EnumStringBody",{enumerable:true,get:function(){return t.enumStringBody}}),Object.defineProperty(e,"EnumStringMember",{enumerable:true,get:function(){return t.enumStringMember}}),Object.defineProperty(e,"EnumSymbolBody",{enumerable:true,get:function(){return t.enumSymbolBody}}),Object.defineProperty(e,"ExistsTypeAnnotation",{enumerable:true,get:function(){return t.existsTypeAnnotation}}),Object.defineProperty(e,"ExportAllDeclaration",{enumerable:true,get:function(){return t.exportAllDeclaration}}),Object.defineProperty(e,"ExportDefaultDeclaration",{enumerable:true,get:function(){return t.exportDefaultDeclaration}}),Object.defineProperty(e,"ExportDefaultSpecifier",{enumerable:true,get:function(){return t.exportDefaultSpecifier}}),Object.defineProperty(e,"ExportNamedDeclaration",{enumerable:true,get:function(){return t.exportNamedDeclaration}}),Object.defineProperty(e,"ExportNamespaceSpecifier",{enumerable:true,get:function(){return t.exportNamespaceSpecifier}}),Object.defineProperty(e,"ExportSpecifier",{enumerable:true,get:function(){return t.exportSpecifier}}),Object.defineProperty(e,"ExpressionStatement",{enumerable:true,get:function(){return t.expressionStatement}}),Object.defineProperty(e,"File",{enumerable:true,get:function(){return t.file}}),Object.defineProperty(e,"ForInStatement",{enumerable:true,get:function(){return t.forInStatement}}),Object.defineProperty(e,"ForOfStatement",{enumerable:true,get:function(){return t.forOfStatement}}),Object.defineProperty(e,"ForStatement",{enumerable:true,get:function(){return t.forStatement}}),Object.defineProperty(e,"FunctionDeclaration",{enumerable:true,get:function(){return t.functionDeclaration}}),Object.defineProperty(e,"FunctionExpression",{enumerable:true,get:function(){return t.functionExpression}}),Object.defineProperty(e,"FunctionTypeAnnotation",{enumerable:true,get:function(){return t.functionTypeAnnotation}}),Object.defineProperty(e,"FunctionTypeParam",{enumerable:true,get:function(){return t.functionTypeParam}}),Object.defineProperty(e,"GenericTypeAnnotation",{enumerable:true,get:function(){return t.genericTypeAnnotation}}),Object.defineProperty(e,"Identifier",{enumerable:true,get:function(){return t.identifier}}),Object.defineProperty(e,"IfStatement",{enumerable:true,get:function(){return t.ifStatement}}),Object.defineProperty(e,"Import",{enumerable:true,get:function(){return t.import}}),Object.defineProperty(e,"ImportAttribute",{enumerable:true,get:function(){return t.importAttribute}}),Object.defineProperty(e,"ImportDeclaration",{enumerable:true,get:function(){return t.importDeclaration}}),Object.defineProperty(e,"ImportDefaultSpecifier",{enumerable:true,get:function(){return t.importDefaultSpecifier}}),Object.defineProperty(e,"ImportExpression",{enumerable:true,get:function(){return t.importExpression}}),Object.defineProperty(e,"ImportNamespaceSpecifier",{enumerable:true,get:function(){return t.importNamespaceSpecifier}}),Object.defineProperty(e,"ImportSpecifier",{enumerable:true,get:function(){return t.importSpecifier}}),Object.defineProperty(e,"IndexedAccessType",{enumerable:true,get:function(){return t.indexedAccessType}}),Object.defineProperty(e,"InferredPredicate",{enumerable:true,get:function(){return t.inferredPredicate}}),Object.defineProperty(e,"InterfaceDeclaration",{enumerable:true,get:function(){return t.interfaceDeclaration}}),Object.defineProperty(e,"InterfaceExtends",{enumerable:true,get:function(){return t.interfaceExtends}}),Object.defineProperty(e,"InterfaceTypeAnnotation",{enumerable:true,get:function(){return t.interfaceTypeAnnotation}}),Object.defineProperty(e,"InterpreterDirective",{enumerable:true,get:function(){return t.interpreterDirective}}),Object.defineProperty(e,"IntersectionTypeAnnotation",{enumerable:true,get:function(){return t.intersectionTypeAnnotation}}),Object.defineProperty(e,"JSXAttribute",{enumerable:true,get:function(){return t.jsxAttribute}}),Object.defineProperty(e,"JSXClosingElement",{enumerable:true,get:function(){return t.jsxClosingElement}}),Object.defineProperty(e,"JSXClosingFragment",{enumerable:true,get:function(){return t.jsxClosingFragment}}),Object.defineProperty(e,"JSXElement",{enumerable:true,get:function(){return t.jsxElement}}),Object.defineProperty(e,"JSXEmptyExpression",{enumerable:true,get:function(){return t.jsxEmptyExpression}}),Object.defineProperty(e,"JSXExpressionContainer",{enumerable:true,get:function(){return t.jsxExpressionContainer}}),Object.defineProperty(e,"JSXFragment",{enumerable:true,get:function(){return t.jsxFragment}}),Object.defineProperty(e,"JSXIdentifier",{enumerable:true,get:function(){return t.jsxIdentifier}}),Object.defineProperty(e,"JSXMemberExpression",{enumerable:true,get:function(){return t.jsxMemberExpression}}),Object.defineProperty(e,"JSXNamespacedName",{enumerable:true,get:function(){return t.jsxNamespacedName}}),Object.defineProperty(e,"JSXOpeningElement",{enumerable:true,get:function(){return t.jsxOpeningElement}}),Object.defineProperty(e,"JSXOpeningFragment",{enumerable:true,get:function(){return t.jsxOpeningFragment}}),Object.defineProperty(e,"JSXSpreadAttribute",{enumerable:true,get:function(){return t.jsxSpreadAttribute}}),Object.defineProperty(e,"JSXSpreadChild",{enumerable:true,get:function(){return t.jsxSpreadChild}}),Object.defineProperty(e,"JSXText",{enumerable:true,get:function(){return t.jsxText}}),Object.defineProperty(e,"LabeledStatement",{enumerable:true,get:function(){return t.labeledStatement}}),Object.defineProperty(e,"LogicalExpression",{enumerable:true,get:function(){return t.logicalExpression}}),Object.defineProperty(e,"MemberExpression",{enumerable:true,get:function(){return t.memberExpression}}),Object.defineProperty(e,"MetaProperty",{enumerable:true,get:function(){return t.metaProperty}}),Object.defineProperty(e,"MixedTypeAnnotation",{enumerable:true,get:function(){return t.mixedTypeAnnotation}}),Object.defineProperty(e,"ModuleExpression",{enumerable:true,get:function(){return t.moduleExpression}}),Object.defineProperty(e,"NewExpression",{enumerable:true,get:function(){return t.newExpression}}),Object.defineProperty(e,"Noop",{enumerable:true,get:function(){return t.noop}}),Object.defineProperty(e,"NullLiteral",{enumerable:true,get:function(){return t.nullLiteral}}),Object.defineProperty(e,"NullLiteralTypeAnnotation",{enumerable:true,get:function(){return t.nullLiteralTypeAnnotation}}),Object.defineProperty(e,"NullableTypeAnnotation",{enumerable:true,get:function(){return t.nullableTypeAnnotation}}),Object.defineProperty(e,"NumberLiteral",{enumerable:true,get:function(){return t.numberLiteral}}),Object.defineProperty(e,"NumberLiteralTypeAnnotation",{enumerable:true,get:function(){return t.numberLiteralTypeAnnotation}}),Object.defineProperty(e,"NumberTypeAnnotation",{enumerable:true,get:function(){return t.numberTypeAnnotation}}),Object.defineProperty(e,"NumericLiteral",{enumerable:true,get:function(){return t.numericLiteral}}),Object.defineProperty(e,"ObjectExpression",{enumerable:true,get:function(){return t.objectExpression}}),Object.defineProperty(e,"ObjectMethod",{enumerable:true,get:function(){return t.objectMethod}}),Object.defineProperty(e,"ObjectPattern",{enumerable:true,get:function(){return t.objectPattern}}),Object.defineProperty(e,"ObjectProperty",{enumerable:true,get:function(){return t.objectProperty}}),Object.defineProperty(e,"ObjectTypeAnnotation",{enumerable:true,get:function(){return t.objectTypeAnnotation}}),Object.defineProperty(e,"ObjectTypeCallProperty",{enumerable:true,get:function(){return t.objectTypeCallProperty}}),Object.defineProperty(e,"ObjectTypeIndexer",{enumerable:true,get:function(){return t.objectTypeIndexer}}),Object.defineProperty(e,"ObjectTypeInternalSlot",{enumerable:true,get:function(){return t.objectTypeInternalSlot}}),Object.defineProperty(e,"ObjectTypeProperty",{enumerable:true,get:function(){return t.objectTypeProperty}}),Object.defineProperty(e,"ObjectTypeSpreadProperty",{enumerable:true,get:function(){return t.objectTypeSpreadProperty}}),Object.defineProperty(e,"OpaqueType",{enumerable:true,get:function(){return t.opaqueType}}),Object.defineProperty(e,"OptionalCallExpression",{enumerable:true,get:function(){return t.optionalCallExpression}}),Object.defineProperty(e,"OptionalIndexedAccessType",{enumerable:true,get:function(){return t.optionalIndexedAccessType}}),Object.defineProperty(e,"OptionalMemberExpression",{enumerable:true,get:function(){return t.optionalMemberExpression}}),Object.defineProperty(e,"ParenthesizedExpression",{enumerable:true,get:function(){return t.parenthesizedExpression}}),Object.defineProperty(e,"PipelineBareFunction",{enumerable:true,get:function(){return t.pipelineBareFunction}}),Object.defineProperty(e,"PipelinePrimaryTopicReference",{enumerable:true,get:function(){return t.pipelinePrimaryTopicReference}}),Object.defineProperty(e,"PipelineTopicExpression",{enumerable:true,get:function(){return t.pipelineTopicExpression}}),Object.defineProperty(e,"Placeholder",{enumerable:true,get:function(){return t.placeholder}}),Object.defineProperty(e,"PrivateName",{enumerable:true,get:function(){return t.privateName}}),Object.defineProperty(e,"Program",{enumerable:true,get:function(){return t.program}}),Object.defineProperty(e,"QualifiedTypeIdentifier",{enumerable:true,get:function(){return t.qualifiedTypeIdentifier}}),Object.defineProperty(e,"RecordExpression",{enumerable:true,get:function(){return t.recordExpression}}),Object.defineProperty(e,"RegExpLiteral",{enumerable:true,get:function(){return t.regExpLiteral}}),Object.defineProperty(e,"RegexLiteral",{enumerable:true,get:function(){return t.regexLiteral}}),Object.defineProperty(e,"RestElement",{enumerable:true,get:function(){return t.restElement}}),Object.defineProperty(e,"RestProperty",{enumerable:true,get:function(){return t.restProperty}}),Object.defineProperty(e,"ReturnStatement",{enumerable:true,get:function(){return t.returnStatement}}),Object.defineProperty(e,"SequenceExpression",{enumerable:true,get:function(){return t.sequenceExpression}}),Object.defineProperty(e,"SpreadElement",{enumerable:true,get:function(){return t.spreadElement}}),Object.defineProperty(e,"SpreadProperty",{enumerable:true,get:function(){return t.spreadProperty}}),Object.defineProperty(e,"StaticBlock",{enumerable:true,get:function(){return t.staticBlock}}),Object.defineProperty(e,"StringLiteral",{enumerable:true,get:function(){return t.stringLiteral}}),Object.defineProperty(e,"StringLiteralTypeAnnotation",{enumerable:true,get:function(){return t.stringLiteralTypeAnnotation}}),Object.defineProperty(e,"StringTypeAnnotation",{enumerable:true,get:function(){return t.stringTypeAnnotation}}),Object.defineProperty(e,"Super",{enumerable:true,get:function(){return t.super}}),Object.defineProperty(e,"SwitchCase",{enumerable:true,get:function(){return t.switchCase}}),Object.defineProperty(e,"SwitchStatement",{enumerable:true,get:function(){return t.switchStatement}}),Object.defineProperty(e,"SymbolTypeAnnotation",{enumerable:true,get:function(){return t.symbolTypeAnnotation}}),Object.defineProperty(e,"TSAnyKeyword",{enumerable:true,get:function(){return t.tsAnyKeyword}}),Object.defineProperty(e,"TSArrayType",{enumerable:true,get:function(){return t.tsArrayType}}),Object.defineProperty(e,"TSAsExpression",{enumerable:true,get:function(){return t.tsAsExpression}}),Object.defineProperty(e,"TSBigIntKeyword",{enumerable:true,get:function(){return t.tsBigIntKeyword}}),Object.defineProperty(e,"TSBooleanKeyword",{enumerable:true,get:function(){return t.tsBooleanKeyword}}),Object.defineProperty(e,"TSCallSignatureDeclaration",{enumerable:true,get:function(){return t.tsCallSignatureDeclaration}}),Object.defineProperty(e,"TSConditionalType",{enumerable:true,get:function(){return t.tsConditionalType}}),Object.defineProperty(e,"TSConstructSignatureDeclaration",{enumerable:true,get:function(){return t.tsConstructSignatureDeclaration}}),Object.defineProperty(e,"TSConstructorType",{enumerable:true,get:function(){return t.tsConstructorType}}),Object.defineProperty(e,"TSDeclareFunction",{enumerable:true,get:function(){return t.tsDeclareFunction}}),Object.defineProperty(e,"TSDeclareMethod",{enumerable:true,get:function(){return t.tsDeclareMethod}}),Object.defineProperty(e,"TSEnumBody",{enumerable:true,get:function(){return t.tsEnumBody}}),Object.defineProperty(e,"TSEnumDeclaration",{enumerable:true,get:function(){return t.tsEnumDeclaration}}),Object.defineProperty(e,"TSEnumMember",{enumerable:true,get:function(){return t.tsEnumMember}}),Object.defineProperty(e,"TSExportAssignment",{enumerable:true,get:function(){return t.tsExportAssignment}}),Object.defineProperty(e,"TSExpressionWithTypeArguments",{enumerable:true,get:function(){return t.tsExpressionWithTypeArguments}}),Object.defineProperty(e,"TSExternalModuleReference",{enumerable:true,get:function(){return t.tsExternalModuleReference}}),Object.defineProperty(e,"TSFunctionType",{enumerable:true,get:function(){return t.tsFunctionType}}),Object.defineProperty(e,"TSImportEqualsDeclaration",{enumerable:true,get:function(){return t.tsImportEqualsDeclaration}}),Object.defineProperty(e,"TSImportType",{enumerable:true,get:function(){return t.tsImportType}}),Object.defineProperty(e,"TSIndexSignature",{enumerable:true,get:function(){return t.tsIndexSignature}}),Object.defineProperty(e,"TSIndexedAccessType",{enumerable:true,get:function(){return t.tsIndexedAccessType}}),Object.defineProperty(e,"TSInferType",{enumerable:true,get:function(){return t.tsInferType}}),Object.defineProperty(e,"TSInstantiationExpression",{enumerable:true,get:function(){return t.tsInstantiationExpression}}),Object.defineProperty(e,"TSInterfaceBody",{enumerable:true,get:function(){return t.tsInterfaceBody}}),Object.defineProperty(e,"TSInterfaceDeclaration",{enumerable:true,get:function(){return t.tsInterfaceDeclaration}}),Object.defineProperty(e,"TSIntersectionType",{enumerable:true,get:function(){return t.tsIntersectionType}}),Object.defineProperty(e,"TSIntrinsicKeyword",{enumerable:true,get:function(){return t.tsIntrinsicKeyword}}),Object.defineProperty(e,"TSLiteralType",{enumerable:true,get:function(){return t.tsLiteralType}}),Object.defineProperty(e,"TSMappedType",{enumerable:true,get:function(){return t.tsMappedType}}),Object.defineProperty(e,"TSMethodSignature",{enumerable:true,get:function(){return t.tsMethodSignature}}),Object.defineProperty(e,"TSModuleBlock",{enumerable:true,get:function(){return t.tsModuleBlock}}),Object.defineProperty(e,"TSModuleDeclaration",{enumerable:true,get:function(){return t.tsModuleDeclaration}}),Object.defineProperty(e,"TSNamedTupleMember",{enumerable:true,get:function(){return t.tsNamedTupleMember}}),Object.defineProperty(e,"TSNamespaceExportDeclaration",{enumerable:true,get:function(){return t.tsNamespaceExportDeclaration}}),Object.defineProperty(e,"TSNeverKeyword",{enumerable:true,get:function(){return t.tsNeverKeyword}}),Object.defineProperty(e,"TSNonNullExpression",{enumerable:true,get:function(){return t.tsNonNullExpression}}),Object.defineProperty(e,"TSNullKeyword",{enumerable:true,get:function(){return t.tsNullKeyword}}),Object.defineProperty(e,"TSNumberKeyword",{enumerable:true,get:function(){return t.tsNumberKeyword}}),Object.defineProperty(e,"TSObjectKeyword",{enumerable:true,get:function(){return t.tsObjectKeyword}}),Object.defineProperty(e,"TSOptionalType",{enumerable:true,get:function(){return t.tsOptionalType}}),Object.defineProperty(e,"TSParameterProperty",{enumerable:true,get:function(){return t.tsParameterProperty}}),Object.defineProperty(e,"TSParenthesizedType",{enumerable:true,get:function(){return t.tsParenthesizedType}}),Object.defineProperty(e,"TSPropertySignature",{enumerable:true,get:function(){return t.tsPropertySignature}}),Object.defineProperty(e,"TSQualifiedName",{enumerable:true,get:function(){return t.tsQualifiedName}}),Object.defineProperty(e,"TSRestType",{enumerable:true,get:function(){return t.tsRestType}}),Object.defineProperty(e,"TSSatisfiesExpression",{enumerable:true,get:function(){return t.tsSatisfiesExpression}}),Object.defineProperty(e,"TSStringKeyword",{enumerable:true,get:function(){return t.tsStringKeyword}}),Object.defineProperty(e,"TSSymbolKeyword",{enumerable:true,get:function(){return t.tsSymbolKeyword}}),Object.defineProperty(e,"TSTemplateLiteralType",{enumerable:true,get:function(){return t.tsTemplateLiteralType}}),Object.defineProperty(e,"TSThisType",{enumerable:true,get:function(){return t.tsThisType}}),Object.defineProperty(e,"TSTupleType",{enumerable:true,get:function(){return t.tsTupleType}}),Object.defineProperty(e,"TSTypeAliasDeclaration",{enumerable:true,get:function(){return t.tsTypeAliasDeclaration}}),Object.defineProperty(e,"TSTypeAnnotation",{enumerable:true,get:function(){return t.tsTypeAnnotation}}),Object.defineProperty(e,"TSTypeAssertion",{enumerable:true,get:function(){return t.tsTypeAssertion}}),Object.defineProperty(e,"TSTypeLiteral",{enumerable:true,get:function(){return t.tsTypeLiteral}}),Object.defineProperty(e,"TSTypeOperator",{enumerable:true,get:function(){return t.tsTypeOperator}}),Object.defineProperty(e,"TSTypeParameter",{enumerable:true,get:function(){return t.tsTypeParameter}}),Object.defineProperty(e,"TSTypeParameterDeclaration",{enumerable:true,get:function(){return t.tsTypeParameterDeclaration}}),Object.defineProperty(e,"TSTypeParameterInstantiation",{enumerable:true,get:function(){return t.tsTypeParameterInstantiation}}),Object.defineProperty(e,"TSTypePredicate",{enumerable:true,get:function(){return t.tsTypePredicate}}),Object.defineProperty(e,"TSTypeQuery",{enumerable:true,get:function(){return t.tsTypeQuery}}),Object.defineProperty(e,"TSTypeReference",{enumerable:true,get:function(){return t.tsTypeReference}}),Object.defineProperty(e,"TSUndefinedKeyword",{enumerable:true,get:function(){return t.tsUndefinedKeyword}}),Object.defineProperty(e,"TSUnionType",{enumerable:true,get:function(){return t.tsUnionType}}),Object.defineProperty(e,"TSUnknownKeyword",{enumerable:true,get:function(){return t.tsUnknownKeyword}}),Object.defineProperty(e,"TSVoidKeyword",{enumerable:true,get:function(){return t.tsVoidKeyword}}),Object.defineProperty(e,"TaggedTemplateExpression",{enumerable:true,get:function(){return t.taggedTemplateExpression}}),Object.defineProperty(e,"TemplateElement",{enumerable:true,get:function(){return t.templateElement}}),Object.defineProperty(e,"TemplateLiteral",{enumerable:true,get:function(){return t.templateLiteral}}),Object.defineProperty(e,"ThisExpression",{enumerable:true,get:function(){return t.thisExpression}}),Object.defineProperty(e,"ThisTypeAnnotation",{enumerable:true,get:function(){return t.thisTypeAnnotation}}),Object.defineProperty(e,"ThrowStatement",{enumerable:true,get:function(){return t.throwStatement}}),Object.defineProperty(e,"TopicReference",{enumerable:true,get:function(){return t.topicReference}}),Object.defineProperty(e,"TryStatement",{enumerable:true,get:function(){return t.tryStatement}}),Object.defineProperty(e,"TupleExpression",{enumerable:true,get:function(){return t.tupleExpression}}),Object.defineProperty(e,"TupleTypeAnnotation",{enumerable:true,get:function(){return t.tupleTypeAnnotation}}),Object.defineProperty(e,"TypeAlias",{enumerable:true,get:function(){return t.typeAlias}}),Object.defineProperty(e,"TypeAnnotation",{enumerable:true,get:function(){return t.typeAnnotation}}),Object.defineProperty(e,"TypeCastExpression",{enumerable:true,get:function(){return t.typeCastExpression}}),Object.defineProperty(e,"TypeParameter",{enumerable:true,get:function(){return t.typeParameter}}),Object.defineProperty(e,"TypeParameterDeclaration",{enumerable:true,get:function(){return t.typeParameterDeclaration}}),Object.defineProperty(e,"TypeParameterInstantiation",{enumerable:true,get:function(){return t.typeParameterInstantiation}}),Object.defineProperty(e,"TypeofTypeAnnotation",{enumerable:true,get:function(){return t.typeofTypeAnnotation}}),Object.defineProperty(e,"UnaryExpression",{enumerable:true,get:function(){return t.unaryExpression}}),Object.defineProperty(e,"UnionTypeAnnotation",{enumerable:true,get:function(){return t.unionTypeAnnotation}}),Object.defineProperty(e,"UpdateExpression",{enumerable:true,get:function(){return t.updateExpression}}),Object.defineProperty(e,"V8IntrinsicIdentifier",{enumerable:true,get:function(){return t.v8IntrinsicIdentifier}}),Object.defineProperty(e,"VariableDeclaration",{enumerable:true,get:function(){return t.variableDeclaration}}),Object.defineProperty(e,"VariableDeclarator",{enumerable:true,get:function(){return t.variableDeclarator}}),Object.defineProperty(e,"Variance",{enumerable:true,get:function(){return t.variance}}),Object.defineProperty(e,"VoidTypeAnnotation",{enumerable:true,get:function(){return t.voidTypeAnnotation}}),Object.defineProperty(e,"WhileStatement",{enumerable:true,get:function(){return t.whileStatement}}),Object.defineProperty(e,"WithStatement",{enumerable:true,get:function(){return t.withStatement}}),Object.defineProperty(e,"YieldExpression",{enumerable:true,get:function(){return t.yieldExpression}});var t=cr();}(e$D),e$D)}

var i$d;function o$i(){return i$d?e$N:(i$d=1,function(r){Object.defineProperty(r,"__esModule",{value:true});var u=cr();Object.keys(u).forEach(function(e){e==="default"||e==="__esModule"||e in r&&r[e]===u[e]||Object.defineProperty(r,e,{enumerable:true,get:function(){return u[e]}});});var t=i$e();Object.keys(t).forEach(function(e){e==="default"||e==="__esModule"||e in r&&r[e]===t[e]||Object.defineProperty(r,e,{enumerable:true,get:function(){return t[e]}});});}(e$N),e$N)}

var a$c;function q$1(){if(a$c)return e$O;a$c=1,Object.defineProperty(e$O,"__esModule",{value:true}),e$O.default=m;var o=o$i(),u=Mr();function m(l,f){const t=l.value.split(/\r\n|\n|\r/);let s=0;for(let e=0;e<t.length;e++)/[^ \t]/.exec(t[e])&&(s=e);let n="";for(let e=0;e<t.length;e++){const c=t[e],p=e===0,_=e===t.length-1,L=e===s;let r=c.replace(/\t/g," ");p||(r=r.replace(/^ +/,"")),_||(r=r.replace(/ +$/,"")),r&&(L||(r+=" "),n+=r);}n&&f.push((0, u.inherits)((0, o.stringLiteral)(n),l));}return e$O}

var l$4;function f$8(){if(l$4)return r$s;l$4=1,Object.defineProperty(r$s,"__esModule",{value:true}),r$s.default=o;var i=qn(),s=q$1();function o(u){const t=[];for(let n=0;n<u.children.length;n++){let e=u.children[n];if((0, i.isJSXText)(e)){(0, s.default)(e,t);continue}(0, i.isJSXExpressionContainer)(e)&&(e=e.expression),!(0, i.isJSXEmptyExpression)(e)&&t.push(e);}return t}return r$s}

var e$C={};

var e$B={};

var o$h;function s$4(){if(o$h)return e$B;o$h=1,Object.defineProperty(e$B,"__esModule",{value:true}),e$B.default=u;var t=I$1();function u(r){return !!(r&&t.VISITOR_KEYS[r.type])}return e$B}

var o$g;function f$7(){if(o$g)return e$C;o$g=1,Object.defineProperty(e$C,"__esModule",{value:true}),e$C.default=i;var s=s$4();function i(r){if(!(0, s.default)(r)){var t;const u=(t=r?.type)!=null?t:JSON.stringify(r);throw new TypeError(`Not a valid node of type "${u}"`)}}return e$C}

var e$A={};

var o$f;function zn(){if(o$f)return e$A;o$f=1,Object.defineProperty(e$A,"__esModule",{value:true}),e$A.assertAccessor=gn,e$A.assertAnyTypeAnnotation=Ue,e$A.assertArgumentPlaceholder=ds,e$A.assertArrayExpression=c,e$A.assertArrayPattern=oe,e$A.assertArrayTypeAnnotation=Ve,e$A.assertArrowFunctionExpression=ie,e$A.assertAssignmentExpression=p,e$A.assertAssignmentPattern=ae,e$A.assertAwaitExpression=Le,e$A.assertBigIntLiteral=we,e$A.assertBinary=_r,e$A.assertBinaryExpression=l,e$A.assertBindExpression=Es,e$A.assertBlock=$r,e$A.assertBlockParent=Qr,e$A.assertBlockStatement=f,e$A.assertBooleanLiteral=X,e$A.assertBooleanLiteralTypeAnnotation=qe,e$A.assertBooleanTypeAnnotation=We,e$A.assertBreakStatement=y,e$A.assertCallExpression=m,e$A.assertCatchClause=d,e$A.assertClass=Dn,e$A.assertClassAccessorProperty=Ke,e$A.assertClassBody=ce,e$A.assertClassDeclaration=le,e$A.assertClassExpression=pe,e$A.assertClassImplements=ze,e$A.assertClassMethod=Pe,e$A.assertClassPrivateMethod=Re,e$A.assertClassPrivateProperty=je,e$A.assertClassProperty=Je,e$A.assertCompletionStatement=Hr,e$A.assertConditional=Zr,e$A.assertConditionalExpression=E,e$A.assertContinueStatement=x,e$A.assertDebuggerStatement=A,e$A.assertDecimalLiteral=gs,e$A.assertDeclaration=pn,e$A.assertDeclareClass=Qe,e$A.assertDeclareExportAllDeclaration=rt,e$A.assertDeclareExportDeclaration=st,e$A.assertDeclareFunction=$e,e$A.assertDeclareInterface=Ge,e$A.assertDeclareModule=Ye,e$A.assertDeclareModuleExports=He,e$A.assertDeclareOpaqueType=et,e$A.assertDeclareTypeAlias=Ze,e$A.assertDeclareVariable=tt,e$A.assertDeclaredPredicate=nt,e$A.assertDecorator=As,e$A.assertDirective=T,e$A.assertDirectiveLiteral=S,e$A.assertDoExpression=Ds,e$A.assertDoWhileStatement=D,e$A.assertEmptyStatement=P,e$A.assertEmptyTypeAnnotation=yt,e$A.assertEnumBody=wn,e$A.assertEnumBooleanBody=Vt,e$A.assertEnumBooleanMember=zt,e$A.assertEnumDeclaration=Ut,e$A.assertEnumDefaultedMember=Gt,e$A.assertEnumMember=Fn,e$A.assertEnumNumberBody=Wt,e$A.assertEnumNumberMember=Qt,e$A.assertEnumStringBody=qt,e$A.assertEnumStringMember=$t,e$A.assertEnumSymbolBody=_t,e$A.assertExistsTypeAnnotation=at,e$A.assertExportAllDeclaration=ue,e$A.assertExportDeclaration=bn,e$A.assertExportDefaultDeclaration=Te,e$A.assertExportDefaultSpecifier=Ps,e$A.assertExportNamedDeclaration=Se,e$A.assertExportNamespaceSpecifier=Fe,e$A.assertExportSpecifier=fe,e$A.assertExpression=qr,e$A.assertExpressionStatement=b,e$A.assertExpressionWrapper=sn,e$A.assertFile=I,e$A.assertFlow=Mn,e$A.assertFlowBaseAnnotation=Nn,e$A.assertFlowDeclaration=Ln,e$A.assertFlowPredicate=hn,e$A.assertFlowType=Bn,e$A.assertFor=rn,e$A.assertForInStatement=g,e$A.assertForOfStatement=ye,e$A.assertForStatement=C,e$A.assertForXStatement=nn,e$A.assertFunction=an,e$A.assertFunctionDeclaration=M,e$A.assertFunctionExpression=B,e$A.assertFunctionParent=on,e$A.assertFunctionTypeAnnotation=ot,e$A.assertFunctionTypeParam=it,e$A.assertGenericTypeAnnotation=ct,e$A.assertIdentifier=N,e$A.assertIfStatement=L,e$A.assertImmutable=fn,e$A.assertImport=he,e$A.assertImportAttribute=xs,e$A.assertImportDeclaration=me,e$A.assertImportDefaultSpecifier=de,e$A.assertImportExpression=Ae,e$A.assertImportNamespaceSpecifier=Ee,e$A.assertImportOrExportDeclaration=Pn,e$A.assertImportSpecifier=xe,e$A.assertIndexedAccessType=Yt,e$A.assertInferredPredicate=pt,e$A.assertInterfaceDeclaration=ut,e$A.assertInterfaceExtends=lt,e$A.assertInterfaceTypeAnnotation=Tt,e$A.assertInterpreterDirective=u,e$A.assertIntersectionTypeAnnotation=St,e$A.assertJSX=On,e$A.assertJSXAttribute=Zt,e$A.assertJSXClosingElement=es,e$A.assertJSXClosingFragment=Ss,e$A.assertJSXElement=ts,e$A.assertJSXEmptyExpression=ss,e$A.assertJSXExpressionContainer=rs,e$A.assertJSXFragment=us,e$A.assertJSXIdentifier=as,e$A.assertJSXMemberExpression=os,e$A.assertJSXNamespacedName=is,e$A.assertJSXOpeningElement=cs,e$A.assertJSXOpeningFragment=Ts,e$A.assertJSXSpreadAttribute=ps,e$A.assertJSXSpreadChild=ns,e$A.assertJSXText=ls,e$A.assertLVal=un,e$A.assertLabeledStatement=h,e$A.assertLiteral=Sn,e$A.assertLogicalExpression=K,e$A.assertLoop=en,e$A.assertMemberExpression=j,e$A.assertMetaProperty=De,e$A.assertMethod=mn,e$A.assertMiscellaneous=Xn,e$A.assertMixedTypeAnnotation=ft,e$A.assertModuleDeclaration=Wn,e$A.assertModuleExpression=Cs,e$A.assertModuleSpecifier=In,e$A.assertNewExpression=R,e$A.assertNoop=fs,e$A.assertNullLiteral=O,e$A.assertNullLiteralTypeAnnotation=_e,e$A.assertNullableTypeAnnotation=mt,e$A.assertNumberLiteral=vn,e$A.assertNumberLiteralTypeAnnotation=dt,e$A.assertNumberTypeAnnotation=Et,e$A.assertNumericLiteral=F,e$A.assertObjectExpression=k,e$A.assertObjectMember=dn,e$A.assertObjectMethod=U,e$A.assertObjectPattern=be,e$A.assertObjectProperty=V,e$A.assertObjectTypeAnnotation=xt,e$A.assertObjectTypeCallProperty=Dt,e$A.assertObjectTypeIndexer=Pt,e$A.assertObjectTypeInternalSlot=At,e$A.assertObjectTypeProperty=bt,e$A.assertObjectTypeSpreadProperty=It,e$A.assertOpaqueType=gt,e$A.assertOptionalCallExpression=Xe,e$A.assertOptionalIndexedAccessType=Ht,e$A.assertOptionalMemberExpression=Oe,e$A.assertParenthesizedExpression=z,e$A.assertPattern=An,e$A.assertPatternLike=ln,e$A.assertPipelineBareFunction=Ns,e$A.assertPipelinePrimaryTopicReference=Ls,e$A.assertPipelineTopicExpression=Bs,e$A.assertPlaceholder=ys,e$A.assertPrivate=Cn,e$A.assertPrivateName=ve,e$A.assertProgram=v,e$A.assertProperty=En,e$A.assertPureish=cn,e$A.assertQualifiedTypeIdentifier=Ct,e$A.assertRecordExpression=bs,e$A.assertRegExpLiteral=J,e$A.assertRegexLiteral=kn,e$A.assertRestElement=W,e$A.assertRestProperty=Un,e$A.assertReturnStatement=q,e$A.assertScopable=zr,e$A.assertSequenceExpression=_,e$A.assertSpreadElement=Ie,e$A.assertSpreadProperty=Vn,e$A.assertStandardized=Wr,e$A.assertStatement=Gr,e$A.assertStaticBlock=ke,e$A.assertStringLiteral=w,e$A.assertStringLiteralTypeAnnotation=Mt,e$A.assertStringTypeAnnotation=Bt,e$A.assertSuper=ge,e$A.assertSwitchCase=Q,e$A.assertSwitchStatement=$,e$A.assertSymbolTypeAnnotation=Nt,e$A.assertTSAnyKeyword=vs,e$A.assertTSArrayType=or,e$A.assertTSAsExpression=Cr,e$A.assertTSBaseType=Rn,e$A.assertTSBigIntKeyword=Us,e$A.assertTSBooleanKeyword=ks,e$A.assertTSCallSignatureDeclaration=Xs,e$A.assertTSConditionalType=Sr,e$A.assertTSConstructSignatureDeclaration=Js,e$A.assertTSConstructorType=tr,e$A.assertTSDeclareFunction=ws,e$A.assertTSDeclareMethod=Fs,e$A.assertTSEntityName=Tn,e$A.assertTSEnumBody=Nr,e$A.assertTSEnumDeclaration=Lr,e$A.assertTSEnumMember=hr,e$A.assertTSExportAssignment=jr,e$A.assertTSExpressionWithTypeArguments=Dr,e$A.assertTSExternalModuleReference=Jr,e$A.assertTSFunctionType=er,e$A.assertTSImportEqualsDeclaration=Xr,e$A.assertTSImportType=Or,e$A.assertTSIndexSignature=Rs,e$A.assertTSIndexedAccessType=dr,e$A.assertTSInferType=fr,e$A.assertTSInstantiationExpression=gr,e$A.assertTSInterfaceBody=br,e$A.assertTSInterfaceDeclaration=Pr,e$A.assertTSIntersectionType=Tr,e$A.assertTSIntrinsicKeyword=Vs,e$A.assertTSLiteralType=Ar,e$A.assertTSMappedType=Er,e$A.assertTSMethodSignature=js,e$A.assertTSModuleBlock=Fr,e$A.assertTSModuleDeclaration=wr,e$A.assertTSNamedTupleMember=lr,e$A.assertTSNamespaceExportDeclaration=Rr,e$A.assertTSNeverKeyword=Ws,e$A.assertTSNonNullExpression=Kr,e$A.assertTSNullKeyword=qs,e$A.assertTSNumberKeyword=_s,e$A.assertTSObjectKeyword=zs,e$A.assertTSOptionalType=cr,e$A.assertTSParameterProperty=hs,e$A.assertTSParenthesizedType=yr,e$A.assertTSPropertySignature=Ks,e$A.assertTSQualifiedName=Os,e$A.assertTSRestType=pr,e$A.assertTSSatisfiesExpression=Mr,e$A.assertTSStringKeyword=Qs,e$A.assertTSSymbolKeyword=$s,e$A.assertTSTemplateLiteralType=xr,e$A.assertTSThisType=Zs,e$A.assertTSTupleType=ir,e$A.assertTSType=jn,e$A.assertTSTypeAliasDeclaration=Ir,e$A.assertTSTypeAnnotation=vr,e$A.assertTSTypeAssertion=Br,e$A.assertTSTypeElement=Kn,e$A.assertTSTypeLiteral=ar,e$A.assertTSTypeOperator=mr,e$A.assertTSTypeParameter=Vr,e$A.assertTSTypeParameterDeclaration=Ur,e$A.assertTSTypeParameterInstantiation=kr,e$A.assertTSTypePredicate=rr,e$A.assertTSTypeQuery=nr,e$A.assertTSTypeReference=sr,e$A.assertTSUndefinedKeyword=Gs,e$A.assertTSUnionType=ur,e$A.assertTSUnknownKeyword=Ys,e$A.assertTSVoidKeyword=Hs,e$A.assertTaggedTemplateExpression=Ce,e$A.assertTemplateElement=Me,e$A.assertTemplateLiteral=Be,e$A.assertTerminatorless=Yr,e$A.assertThisExpression=G,e$A.assertThisTypeAnnotation=Lt,e$A.assertThrowStatement=Y,e$A.assertTopicReference=Ms,e$A.assertTryStatement=H,e$A.assertTupleExpression=Is,e$A.assertTupleTypeAnnotation=ht,e$A.assertTypeAlias=Ft,e$A.assertTypeAnnotation=Ot,e$A.assertTypeCastExpression=Xt,e$A.assertTypeParameter=Jt,e$A.assertTypeParameterDeclaration=Kt,e$A.assertTypeParameterInstantiation=jt,e$A.assertTypeScript=Jn,e$A.assertTypeofTypeAnnotation=wt,e$A.assertUnaryExpression=Z,e$A.assertUnaryLike=xn,e$A.assertUnionTypeAnnotation=Rt,e$A.assertUpdateExpression=ee,e$A.assertUserWhitespacable=yn,e$A.assertV8IntrinsicIdentifier=ms,e$A.assertVariableDeclaration=te,e$A.assertVariableDeclarator=se,e$A.assertVariance=vt,e$A.assertVoidTypeAnnotation=kt,e$A.assertWhile=tn,e$A.assertWhileStatement=re,e$A.assertWithStatement=ne,e$A.assertYieldExpression=Ne;var i=c$7(),n=m$4();function r(e,t,a){if(!(0, i.default)(e,t,a))throw new Error(`Expected type "${e}" with option ${JSON.stringify(a)}, but instead got "${t.type}".`)}function c(e,t){r("ArrayExpression",e,t);}function p(e,t){r("AssignmentExpression",e,t);}function l(e,t){r("BinaryExpression",e,t);}function u(e,t){r("InterpreterDirective",e,t);}function T(e,t){r("Directive",e,t);}function S(e,t){r("DirectiveLiteral",e,t);}function f(e,t){r("BlockStatement",e,t);}function y(e,t){r("BreakStatement",e,t);}function m(e,t){r("CallExpression",e,t);}function d(e,t){r("CatchClause",e,t);}function E(e,t){r("ConditionalExpression",e,t);}function x(e,t){r("ContinueStatement",e,t);}function A(e,t){r("DebuggerStatement",e,t);}function D(e,t){r("DoWhileStatement",e,t);}function P(e,t){r("EmptyStatement",e,t);}function b(e,t){r("ExpressionStatement",e,t);}function I(e,t){r("File",e,t);}function g(e,t){r("ForInStatement",e,t);}function C(e,t){r("ForStatement",e,t);}function M(e,t){r("FunctionDeclaration",e,t);}function B(e,t){r("FunctionExpression",e,t);}function N(e,t){r("Identifier",e,t);}function L(e,t){r("IfStatement",e,t);}function h(e,t){r("LabeledStatement",e,t);}function w(e,t){r("StringLiteral",e,t);}function F(e,t){r("NumericLiteral",e,t);}function O(e,t){r("NullLiteral",e,t);}function X(e,t){r("BooleanLiteral",e,t);}function J(e,t){r("RegExpLiteral",e,t);}function K(e,t){r("LogicalExpression",e,t);}function j(e,t){r("MemberExpression",e,t);}function R(e,t){r("NewExpression",e,t);}function v(e,t){r("Program",e,t);}function k(e,t){r("ObjectExpression",e,t);}function U(e,t){r("ObjectMethod",e,t);}function V(e,t){r("ObjectProperty",e,t);}function W(e,t){r("RestElement",e,t);}function q(e,t){r("ReturnStatement",e,t);}function _(e,t){r("SequenceExpression",e,t);}function z(e,t){r("ParenthesizedExpression",e,t);}function Q(e,t){r("SwitchCase",e,t);}function $(e,t){r("SwitchStatement",e,t);}function G(e,t){r("ThisExpression",e,t);}function Y(e,t){r("ThrowStatement",e,t);}function H(e,t){r("TryStatement",e,t);}function Z(e,t){r("UnaryExpression",e,t);}function ee(e,t){r("UpdateExpression",e,t);}function te(e,t){r("VariableDeclaration",e,t);}function se(e,t){r("VariableDeclarator",e,t);}function re(e,t){r("WhileStatement",e,t);}function ne(e,t){r("WithStatement",e,t);}function ae(e,t){r("AssignmentPattern",e,t);}function oe(e,t){r("ArrayPattern",e,t);}function ie(e,t){r("ArrowFunctionExpression",e,t);}function ce(e,t){r("ClassBody",e,t);}function pe(e,t){r("ClassExpression",e,t);}function le(e,t){r("ClassDeclaration",e,t);}function ue(e,t){r("ExportAllDeclaration",e,t);}function Te(e,t){r("ExportDefaultDeclaration",e,t);}function Se(e,t){r("ExportNamedDeclaration",e,t);}function fe(e,t){r("ExportSpecifier",e,t);}function ye(e,t){r("ForOfStatement",e,t);}function me(e,t){r("ImportDeclaration",e,t);}function de(e,t){r("ImportDefaultSpecifier",e,t);}function Ee(e,t){r("ImportNamespaceSpecifier",e,t);}function xe(e,t){r("ImportSpecifier",e,t);}function Ae(e,t){r("ImportExpression",e,t);}function De(e,t){r("MetaProperty",e,t);}function Pe(e,t){r("ClassMethod",e,t);}function be(e,t){r("ObjectPattern",e,t);}function Ie(e,t){r("SpreadElement",e,t);}function ge(e,t){r("Super",e,t);}function Ce(e,t){r("TaggedTemplateExpression",e,t);}function Me(e,t){r("TemplateElement",e,t);}function Be(e,t){r("TemplateLiteral",e,t);}function Ne(e,t){r("YieldExpression",e,t);}function Le(e,t){r("AwaitExpression",e,t);}function he(e,t){r("Import",e,t);}function we(e,t){r("BigIntLiteral",e,t);}function Fe(e,t){r("ExportNamespaceSpecifier",e,t);}function Oe(e,t){r("OptionalMemberExpression",e,t);}function Xe(e,t){r("OptionalCallExpression",e,t);}function Je(e,t){r("ClassProperty",e,t);}function Ke(e,t){r("ClassAccessorProperty",e,t);}function je(e,t){r("ClassPrivateProperty",e,t);}function Re(e,t){r("ClassPrivateMethod",e,t);}function ve(e,t){r("PrivateName",e,t);}function ke(e,t){r("StaticBlock",e,t);}function Ue(e,t){r("AnyTypeAnnotation",e,t);}function Ve(e,t){r("ArrayTypeAnnotation",e,t);}function We(e,t){r("BooleanTypeAnnotation",e,t);}function qe(e,t){r("BooleanLiteralTypeAnnotation",e,t);}function _e(e,t){r("NullLiteralTypeAnnotation",e,t);}function ze(e,t){r("ClassImplements",e,t);}function Qe(e,t){r("DeclareClass",e,t);}function $e(e,t){r("DeclareFunction",e,t);}function Ge(e,t){r("DeclareInterface",e,t);}function Ye(e,t){r("DeclareModule",e,t);}function He(e,t){r("DeclareModuleExports",e,t);}function Ze(e,t){r("DeclareTypeAlias",e,t);}function et(e,t){r("DeclareOpaqueType",e,t);}function tt(e,t){r("DeclareVariable",e,t);}function st(e,t){r("DeclareExportDeclaration",e,t);}function rt(e,t){r("DeclareExportAllDeclaration",e,t);}function nt(e,t){r("DeclaredPredicate",e,t);}function at(e,t){r("ExistsTypeAnnotation",e,t);}function ot(e,t){r("FunctionTypeAnnotation",e,t);}function it(e,t){r("FunctionTypeParam",e,t);}function ct(e,t){r("GenericTypeAnnotation",e,t);}function pt(e,t){r("InferredPredicate",e,t);}function lt(e,t){r("InterfaceExtends",e,t);}function ut(e,t){r("InterfaceDeclaration",e,t);}function Tt(e,t){r("InterfaceTypeAnnotation",e,t);}function St(e,t){r("IntersectionTypeAnnotation",e,t);}function ft(e,t){r("MixedTypeAnnotation",e,t);}function yt(e,t){r("EmptyTypeAnnotation",e,t);}function mt(e,t){r("NullableTypeAnnotation",e,t);}function dt(e,t){r("NumberLiteralTypeAnnotation",e,t);}function Et(e,t){r("NumberTypeAnnotation",e,t);}function xt(e,t){r("ObjectTypeAnnotation",e,t);}function At(e,t){r("ObjectTypeInternalSlot",e,t);}function Dt(e,t){r("ObjectTypeCallProperty",e,t);}function Pt(e,t){r("ObjectTypeIndexer",e,t);}function bt(e,t){r("ObjectTypeProperty",e,t);}function It(e,t){r("ObjectTypeSpreadProperty",e,t);}function gt(e,t){r("OpaqueType",e,t);}function Ct(e,t){r("QualifiedTypeIdentifier",e,t);}function Mt(e,t){r("StringLiteralTypeAnnotation",e,t);}function Bt(e,t){r("StringTypeAnnotation",e,t);}function Nt(e,t){r("SymbolTypeAnnotation",e,t);}function Lt(e,t){r("ThisTypeAnnotation",e,t);}function ht(e,t){r("TupleTypeAnnotation",e,t);}function wt(e,t){r("TypeofTypeAnnotation",e,t);}function Ft(e,t){r("TypeAlias",e,t);}function Ot(e,t){r("TypeAnnotation",e,t);}function Xt(e,t){r("TypeCastExpression",e,t);}function Jt(e,t){r("TypeParameter",e,t);}function Kt(e,t){r("TypeParameterDeclaration",e,t);}function jt(e,t){r("TypeParameterInstantiation",e,t);}function Rt(e,t){r("UnionTypeAnnotation",e,t);}function vt(e,t){r("Variance",e,t);}function kt(e,t){r("VoidTypeAnnotation",e,t);}function Ut(e,t){r("EnumDeclaration",e,t);}function Vt(e,t){r("EnumBooleanBody",e,t);}function Wt(e,t){r("EnumNumberBody",e,t);}function qt(e,t){r("EnumStringBody",e,t);}function _t(e,t){r("EnumSymbolBody",e,t);}function zt(e,t){r("EnumBooleanMember",e,t);}function Qt(e,t){r("EnumNumberMember",e,t);}function $t(e,t){r("EnumStringMember",e,t);}function Gt(e,t){r("EnumDefaultedMember",e,t);}function Yt(e,t){r("IndexedAccessType",e,t);}function Ht(e,t){r("OptionalIndexedAccessType",e,t);}function Zt(e,t){r("JSXAttribute",e,t);}function es(e,t){r("JSXClosingElement",e,t);}function ts(e,t){r("JSXElement",e,t);}function ss(e,t){r("JSXEmptyExpression",e,t);}function rs(e,t){r("JSXExpressionContainer",e,t);}function ns(e,t){r("JSXSpreadChild",e,t);}function as(e,t){r("JSXIdentifier",e,t);}function os(e,t){r("JSXMemberExpression",e,t);}function is(e,t){r("JSXNamespacedName",e,t);}function cs(e,t){r("JSXOpeningElement",e,t);}function ps(e,t){r("JSXSpreadAttribute",e,t);}function ls(e,t){r("JSXText",e,t);}function us(e,t){r("JSXFragment",e,t);}function Ts(e,t){r("JSXOpeningFragment",e,t);}function Ss(e,t){r("JSXClosingFragment",e,t);}function fs(e,t){r("Noop",e,t);}function ys(e,t){r("Placeholder",e,t);}function ms(e,t){r("V8IntrinsicIdentifier",e,t);}function ds(e,t){r("ArgumentPlaceholder",e,t);}function Es(e,t){r("BindExpression",e,t);}function xs(e,t){r("ImportAttribute",e,t);}function As(e,t){r("Decorator",e,t);}function Ds(e,t){r("DoExpression",e,t);}function Ps(e,t){r("ExportDefaultSpecifier",e,t);}function bs(e,t){r("RecordExpression",e,t);}function Is(e,t){r("TupleExpression",e,t);}function gs(e,t){r("DecimalLiteral",e,t);}function Cs(e,t){r("ModuleExpression",e,t);}function Ms(e,t){r("TopicReference",e,t);}function Bs(e,t){r("PipelineTopicExpression",e,t);}function Ns(e,t){r("PipelineBareFunction",e,t);}function Ls(e,t){r("PipelinePrimaryTopicReference",e,t);}function hs(e,t){r("TSParameterProperty",e,t);}function ws(e,t){r("TSDeclareFunction",e,t);}function Fs(e,t){r("TSDeclareMethod",e,t);}function Os(e,t){r("TSQualifiedName",e,t);}function Xs(e,t){r("TSCallSignatureDeclaration",e,t);}function Js(e,t){r("TSConstructSignatureDeclaration",e,t);}function Ks(e,t){r("TSPropertySignature",e,t);}function js(e,t){r("TSMethodSignature",e,t);}function Rs(e,t){r("TSIndexSignature",e,t);}function vs(e,t){r("TSAnyKeyword",e,t);}function ks(e,t){r("TSBooleanKeyword",e,t);}function Us(e,t){r("TSBigIntKeyword",e,t);}function Vs(e,t){r("TSIntrinsicKeyword",e,t);}function Ws(e,t){r("TSNeverKeyword",e,t);}function qs(e,t){r("TSNullKeyword",e,t);}function _s(e,t){r("TSNumberKeyword",e,t);}function zs(e,t){r("TSObjectKeyword",e,t);}function Qs(e,t){r("TSStringKeyword",e,t);}function $s(e,t){r("TSSymbolKeyword",e,t);}function Gs(e,t){r("TSUndefinedKeyword",e,t);}function Ys(e,t){r("TSUnknownKeyword",e,t);}function Hs(e,t){r("TSVoidKeyword",e,t);}function Zs(e,t){r("TSThisType",e,t);}function er(e,t){r("TSFunctionType",e,t);}function tr(e,t){r("TSConstructorType",e,t);}function sr(e,t){r("TSTypeReference",e,t);}function rr(e,t){r("TSTypePredicate",e,t);}function nr(e,t){r("TSTypeQuery",e,t);}function ar(e,t){r("TSTypeLiteral",e,t);}function or(e,t){r("TSArrayType",e,t);}function ir(e,t){r("TSTupleType",e,t);}function cr(e,t){r("TSOptionalType",e,t);}function pr(e,t){r("TSRestType",e,t);}function lr(e,t){r("TSNamedTupleMember",e,t);}function ur(e,t){r("TSUnionType",e,t);}function Tr(e,t){r("TSIntersectionType",e,t);}function Sr(e,t){r("TSConditionalType",e,t);}function fr(e,t){r("TSInferType",e,t);}function yr(e,t){r("TSParenthesizedType",e,t);}function mr(e,t){r("TSTypeOperator",e,t);}function dr(e,t){r("TSIndexedAccessType",e,t);}function Er(e,t){r("TSMappedType",e,t);}function xr(e,t){r("TSTemplateLiteralType",e,t);}function Ar(e,t){r("TSLiteralType",e,t);}function Dr(e,t){r("TSExpressionWithTypeArguments",e,t);}function Pr(e,t){r("TSInterfaceDeclaration",e,t);}function br(e,t){r("TSInterfaceBody",e,t);}function Ir(e,t){r("TSTypeAliasDeclaration",e,t);}function gr(e,t){r("TSInstantiationExpression",e,t);}function Cr(e,t){r("TSAsExpression",e,t);}function Mr(e,t){r("TSSatisfiesExpression",e,t);}function Br(e,t){r("TSTypeAssertion",e,t);}function Nr(e,t){r("TSEnumBody",e,t);}function Lr(e,t){r("TSEnumDeclaration",e,t);}function hr(e,t){r("TSEnumMember",e,t);}function wr(e,t){r("TSModuleDeclaration",e,t);}function Fr(e,t){r("TSModuleBlock",e,t);}function Or(e,t){r("TSImportType",e,t);}function Xr(e,t){r("TSImportEqualsDeclaration",e,t);}function Jr(e,t){r("TSExternalModuleReference",e,t);}function Kr(e,t){r("TSNonNullExpression",e,t);}function jr(e,t){r("TSExportAssignment",e,t);}function Rr(e,t){r("TSNamespaceExportDeclaration",e,t);}function vr(e,t){r("TSTypeAnnotation",e,t);}function kr(e,t){r("TSTypeParameterInstantiation",e,t);}function Ur(e,t){r("TSTypeParameterDeclaration",e,t);}function Vr(e,t){r("TSTypeParameter",e,t);}function Wr(e,t){r("Standardized",e,t);}function qr(e,t){r("Expression",e,t);}function _r(e,t){r("Binary",e,t);}function zr(e,t){r("Scopable",e,t);}function Qr(e,t){r("BlockParent",e,t);}function $r(e,t){r("Block",e,t);}function Gr(e,t){r("Statement",e,t);}function Yr(e,t){r("Terminatorless",e,t);}function Hr(e,t){r("CompletionStatement",e,t);}function Zr(e,t){r("Conditional",e,t);}function en(e,t){r("Loop",e,t);}function tn(e,t){r("While",e,t);}function sn(e,t){r("ExpressionWrapper",e,t);}function rn(e,t){r("For",e,t);}function nn(e,t){r("ForXStatement",e,t);}function an(e,t){r("Function",e,t);}function on(e,t){r("FunctionParent",e,t);}function cn(e,t){r("Pureish",e,t);}function pn(e,t){r("Declaration",e,t);}function ln(e,t){r("PatternLike",e,t);}function un(e,t){r("LVal",e,t);}function Tn(e,t){r("TSEntityName",e,t);}function Sn(e,t){r("Literal",e,t);}function fn(e,t){r("Immutable",e,t);}function yn(e,t){r("UserWhitespacable",e,t);}function mn(e,t){r("Method",e,t);}function dn(e,t){r("ObjectMember",e,t);}function En(e,t){r("Property",e,t);}function xn(e,t){r("UnaryLike",e,t);}function An(e,t){r("Pattern",e,t);}function Dn(e,t){r("Class",e,t);}function Pn(e,t){r("ImportOrExportDeclaration",e,t);}function bn(e,t){r("ExportDeclaration",e,t);}function In(e,t){r("ModuleSpecifier",e,t);}function gn(e,t){r("Accessor",e,t);}function Cn(e,t){r("Private",e,t);}function Mn(e,t){r("Flow",e,t);}function Bn(e,t){r("FlowType",e,t);}function Nn(e,t){r("FlowBaseAnnotation",e,t);}function Ln(e,t){r("FlowDeclaration",e,t);}function hn(e,t){r("FlowPredicate",e,t);}function wn(e,t){r("EnumBody",e,t);}function Fn(e,t){r("EnumMember",e,t);}function On(e,t){r("JSX",e,t);}function Xn(e,t){r("Miscellaneous",e,t);}function Jn(e,t){r("TypeScript",e,t);}function Kn(e,t){r("TSTypeElement",e,t);}function jn(e,t){r("TSType",e,t);}function Rn(e,t){r("TSBaseType",e,t);}function vn(e,t){(0, n.default)("assertNumberLiteral","assertNumericLiteral"),r("NumberLiteral",e,t);}function kn(e,t){(0, n.default)("assertRegexLiteral","assertRegExpLiteral"),r("RegexLiteral",e,t);}function Un(e,t){(0, n.default)("assertRestProperty","assertRestElement"),r("RestProperty",e,t);}function Vn(e,t){(0, n.default)("assertSpreadProperty","assertSpreadElement"),r("SpreadProperty",e,t);}function Wn(e,t){(0, n.default)("assertModuleDeclaration","assertImportOrExportDeclaration"),r("ModuleDeclaration",e,t);}return e$A}

var e$z={};

var r$f;function a$b(){if(r$f)return e$z;r$f=1,Object.defineProperty(e$z,"__esModule",{value:true}),e$z.default=void 0;var e=o$i();e$z.default=o;function o(t){switch(t){case "string":return (0, e.stringTypeAnnotation)();case "number":return (0, e.numberTypeAnnotation)();case "undefined":return (0, e.voidTypeAnnotation)();case "boolean":return (0, e.booleanTypeAnnotation)();case "function":return (0, e.genericTypeAnnotation)((0, e.identifier)("Function"));case "object":return (0, e.genericTypeAnnotation)((0, e.identifier)("Object"));case "symbol":return (0, e.genericTypeAnnotation)((0, e.identifier)("Symbol"));case "bigint":return (0, e.anyTypeAnnotation)()}throw new Error("Invalid typeof value: "+t)}return e$z}

var e$y={};

var e$x={};

var l$3;function _$5(){if(l$3)return e$x;l$3=1,Object.defineProperty(e$x,"__esModule",{value:true}),e$x.default=m;var s=qn();function c(r){return (0, s.isIdentifier)(r)?r.name:`${r.id.name}.${c(r.qualification)}`}function m(r){const p=Array.from(r),a=new Map,f=new Map,y=new Set,n=[];for(let t=0;t<p.length;t++){const e=p[t];if(e&&!n.includes(e)){if((0, s.isAnyTypeAnnotation)(e))return [e];if((0, s.isFlowBaseAnnotation)(e)){f.set(e.type,e);continue}if((0, s.isUnionTypeAnnotation)(e)){y.has(e.types)||(p.push(...e.types),y.add(e.types));continue}if((0, s.isGenericTypeAnnotation)(e)){const u=c(e.id);if(a.has(u)){let i=a.get(u);i.typeParameters?e.typeParameters&&(i.typeParameters.params.push(...e.typeParameters.params),i.typeParameters.params=m(i.typeParameters.params)):i=e.typeParameters;}else a.set(u,e);continue}n.push(e);}}for(const[,t]of f)n.push(t);for(const[,t]of a)n.push(t);return n}return e$x}

var t$5;function s$3(){if(t$5)return e$y;t$5=1,Object.defineProperty(e$y,"__esModule",{value:true}),e$y.default=i;var o=o$i(),n=_$5();function i(u){const r=(0, n.default)(u);return r.length===1?r[0]:(0, o.unionTypeAnnotation)(r)}return e$y}

var e$w={};

var e$v={};

var g;function d(){if(g)return e$v;g=1,Object.defineProperty(e$v,"__esModule",{value:true}),e$v.default=m;var r=qn();function f(s){return (0, r.isIdentifier)(s)?s.name:(0, r.isThisExpression)(s)?"this":`${s.right.name}.${f(s.left)}`}function m(s){const u=Array.from(s),n=new Map,y=new Map,l=new Set,i=[];for(let t=0;t<u.length;t++){const e=u[t];if(!e||i.includes(e))continue;if((0, r.isTSAnyKeyword)(e))return [e];if((0, r.isTSBaseType)(e)){y.set(e.type,e);continue}if((0, r.isTSUnionType)(e)){l.has(e.types)||(u.push(...e.types),l.add(e.types));continue}const a="typeParameters";if((0, r.isTSTypeReference)(e)&&e[a]){const T=e[a],c=f(e.typeName);if(n.has(c)){let h=n.get(c);const o=h[a];o?(o.params.push(...T.params),o.params=m(o.params)):h=T;}else n.set(c,e);continue}i.push(e);}for(const[,t]of y)i.push(t);for(const[,t]of n)i.push(t);return i}return e$v}

var n$6;function T(){if(n$6)return e$w;n$6=1,Object.defineProperty(e$w,"__esModule",{value:true}),e$w.default=a;var i=o$i(),o=d(),u=qn();function a(s){const p=s.map(t=>(0, u.isTSTypeAnnotation)(t)?t.typeAnnotation:t),r=(0, o.default)(p);return r.length===1?r[0]:(0, i.tsUnionType)(r)}return e$w}

var o$e={};

var i$c;function n$5(){if(i$c)return o$e;i$c=1,Object.defineProperty(o$e,"__esModule",{value:true}),o$e.buildUndefinedNode=o;var e=o$i();function o(){return (0, e.unaryExpression)("void",(0, e.numericLiteral)(0),true)}return o$e}

var e$u={};

var N;function I(){if(N)return e$u;N=1,Object.defineProperty(e$u,"__esModule",{value:true}),e$u.default=O;var y=I$1(),m=qn();const{hasOwn:l}={hasOwn:Function.call.bind(Object.prototype.hasOwnProperty)};function _(r,e,t,i){return r&&typeof r.type=="string"?c(r,e,t,i):r}function u(r,e,t,i){return Array.isArray(r)?r.map(a=>_(a,e,t,i)):_(r,e,t,i)}function O(r,e=true,t=false){return c(r,e,t,new Map)}function c(r,e=true,t=false,i){if(!r)return r;const{type:a}=r,n={type:r.type};if((0, m.isIdentifier)(r))n.name=r.name,l(r,"optional")&&typeof r.optional=="boolean"&&(n.optional=r.optional),l(r,"typeAnnotation")&&(n.typeAnnotation=e?u(r.typeAnnotation,true,t,i):r.typeAnnotation),l(r,"decorators")&&(n.decorators=e?u(r.decorators,true,t,i):r.decorators);else if(l(y.NODE_FIELDS,a))for(const f of Object.keys(y.NODE_FIELDS[a]))l(r,f)&&(e?n[f]=(0, m.isFile)(r)&&f==="comments"?s(r.comments,e,t,i):u(r[f],true,t,i):n[f]=r[f]);else throw new Error(`Unknown node type: "${a}"`);return l(r,"loc")&&(t?n.loc=null:n.loc=r.loc),l(r,"leadingComments")&&(n.leadingComments=s(r.leadingComments,e,t,i)),l(r,"innerComments")&&(n.innerComments=s(r.innerComments,e,t,i)),l(r,"trailingComments")&&(n.trailingComments=s(r.trailingComments,e,t,i)),l(r,"extra")&&(n.extra=Object.assign({},r.extra)),n}function s(r,e,t,i){return !r||!e?r:r.map(a=>{const n=i.get(a);if(n)return n;const{type:f,value:g,loc:q}=a,p={type:f,value:g,loc:q};return t&&(p.loc=null),i.set(a,p),p})}return e$u}

var e$t={};

var r$e;function i$b(){if(r$e)return e$t;r$e=1,Object.defineProperty(e$t,"__esModule",{value:true}),e$t.default=u;var o=I();function u(t){return (0, o.default)(t,false)}return e$t}

var e$s={};

var r$d;function i$a(){if(r$d)return e$s;r$d=1,Object.defineProperty(e$s,"__esModule",{value:true}),e$s.default=u;var o=I();function u(t){return (0, o.default)(t)}return e$s}

var e$r={};

var r$c;function n$4(){if(r$c)return e$r;r$c=1,Object.defineProperty(e$r,"__esModule",{value:true}),e$r.default=t;var o=I();function t(u){return (0, o.default)(u,true,true)}return e$r}

var o$d={};

var r$b;function n$3(){if(r$b)return o$d;r$b=1,Object.defineProperty(o$d,"__esModule",{value:true}),o$d.default=t;var o=I();function t(u){return (0, o.default)(u,false,true)}return o$d}

var a$a={};

var a$9={};

var i$9;function f$6(){if(i$9)return a$9;i$9=1,Object.defineProperty(a$9,"__esModule",{value:true}),a$9.default=a;function a(r,u,t){if(!t||!r)return r;const e=`${u}Comments`;return r[e]?u==="leading"?r[e]=t.concat(r[e]):r[e].push(...t):r[e]=t,r}return a$9}

var r$a;function i$8(){if(r$a)return a$a;r$a=1,Object.defineProperty(a$a,"__esModule",{value:true}),a$a.default=m;var t=f$6();function m(o,u,n,d){return (0, t.default)(o,u,[{type:d?"CommentLine":"CommentBlock",value:n}])}return a$a}

var e$q={};

var r$9={};

var o$c;function u$8(){if(o$c)return r$9;o$c=1,Object.defineProperty(r$9,"__esModule",{value:true}),r$9.default=n;function n(e,t,i){t&&i&&(t[e]=Array.from(new Set([].concat(t[e],i[e]).filter(Boolean))));}return r$9}

var r$8;function u$7(){if(r$8)return e$q;r$8=1,Object.defineProperty(e$q,"__esModule",{value:true}),e$q.default=t;var n=u$8();function t(i,o){(0, n.default)("innerComments",i,o);}return e$q}

var e$p={};

var r$7;function u$6(){if(r$7)return e$p;r$7=1,Object.defineProperty(e$p,"__esModule",{value:true}),e$p.default=t;var i=u$8();function t(n,o){(0, i.default)("leadingComments",n,o);}return e$p}

var e$o={};

var r$6={};

var e$n;function u$5(){if(e$n)return r$6;e$n=1,Object.defineProperty(r$6,"__esModule",{value:true}),r$6.default=t;var i=u$8();function t(n,o){(0, i.default)("trailingComments",n,o);}return r$6}

var i$7;function f$5(){if(i$7)return e$o;i$7=1,Object.defineProperty(e$o,"__esModule",{value:true}),e$o.default=o;var m=u$5(),u=u$6(),n=u$7();function o(e,t){return (0, m.default)(e,t),(0, u.default)(e,t),(0, n.default)(e,t),e}return e$o}

var e$m={};

var o$b;function i$6(){if(o$b)return e$m;o$b=1,Object.defineProperty(e$m,"__esModule",{value:true}),e$m.default=t;var m=B$1();function t(r){return m.COMMENT_KEYS.forEach(u=>{r[u]=null;}),r}return e$m}

var e$l={};

var P;function Y(){if(P)return e$l;P=1,Object.defineProperty(e$l,"__esModule",{value:true}),e$l.WHILE_TYPES=e$l.USERWHITESPACABLE_TYPES=e$l.UNARYLIKE_TYPES=e$l.TYPESCRIPT_TYPES=e$l.TSTYPE_TYPES=e$l.TSTYPEELEMENT_TYPES=e$l.TSENTITYNAME_TYPES=e$l.TSBASETYPE_TYPES=e$l.TERMINATORLESS_TYPES=e$l.STATEMENT_TYPES=e$l.STANDARDIZED_TYPES=e$l.SCOPABLE_TYPES=e$l.PUREISH_TYPES=e$l.PROPERTY_TYPES=e$l.PRIVATE_TYPES=e$l.PATTERN_TYPES=e$l.PATTERNLIKE_TYPES=e$l.OBJECTMEMBER_TYPES=e$l.MODULESPECIFIER_TYPES=e$l.MODULEDECLARATION_TYPES=e$l.MISCELLANEOUS_TYPES=e$l.METHOD_TYPES=e$l.LVAL_TYPES=e$l.LOOP_TYPES=e$l.LITERAL_TYPES=e$l.JSX_TYPES=e$l.IMPORTOREXPORTDECLARATION_TYPES=e$l.IMMUTABLE_TYPES=e$l.FUNCTION_TYPES=e$l.FUNCTIONPARENT_TYPES=e$l.FOR_TYPES=e$l.FORXSTATEMENT_TYPES=e$l.FLOW_TYPES=e$l.FLOWTYPE_TYPES=e$l.FLOWPREDICATE_TYPES=e$l.FLOWDECLARATION_TYPES=e$l.FLOWBASEANNOTATION_TYPES=e$l.EXPRESSION_TYPES=e$l.EXPRESSIONWRAPPER_TYPES=e$l.EXPORTDECLARATION_TYPES=e$l.ENUMMEMBER_TYPES=e$l.ENUMBODY_TYPES=e$l.DECLARATION_TYPES=e$l.CONDITIONAL_TYPES=e$l.COMPLETIONSTATEMENT_TYPES=e$l.CLASS_TYPES=e$l.BLOCK_TYPES=e$l.BLOCKPARENT_TYPES=e$l.BINARY_TYPES=e$l.ACCESSOR_TYPES=void 0;var S=I$1();e$l.STANDARDIZED_TYPES=S.FLIPPED_ALIAS_KEYS.Standardized;e$l.EXPRESSION_TYPES=S.FLIPPED_ALIAS_KEYS.Expression;e$l.BINARY_TYPES=S.FLIPPED_ALIAS_KEYS.Binary;e$l.SCOPABLE_TYPES=S.FLIPPED_ALIAS_KEYS.Scopable;e$l.BLOCKPARENT_TYPES=S.FLIPPED_ALIAS_KEYS.BlockParent;e$l.BLOCK_TYPES=S.FLIPPED_ALIAS_KEYS.Block;e$l.STATEMENT_TYPES=S.FLIPPED_ALIAS_KEYS.Statement;e$l.TERMINATORLESS_TYPES=S.FLIPPED_ALIAS_KEYS.Terminatorless;e$l.COMPLETIONSTATEMENT_TYPES=S.FLIPPED_ALIAS_KEYS.CompletionStatement;e$l.CONDITIONAL_TYPES=S.FLIPPED_ALIAS_KEYS.Conditional;e$l.LOOP_TYPES=S.FLIPPED_ALIAS_KEYS.Loop;e$l.WHILE_TYPES=S.FLIPPED_ALIAS_KEYS.While;e$l.EXPRESSIONWRAPPER_TYPES=S.FLIPPED_ALIAS_KEYS.ExpressionWrapper;e$l.FOR_TYPES=S.FLIPPED_ALIAS_KEYS.For;e$l.FORXSTATEMENT_TYPES=S.FLIPPED_ALIAS_KEYS.ForXStatement;e$l.FUNCTION_TYPES=S.FLIPPED_ALIAS_KEYS.Function;e$l.FUNCTIONPARENT_TYPES=S.FLIPPED_ALIAS_KEYS.FunctionParent;e$l.PUREISH_TYPES=S.FLIPPED_ALIAS_KEYS.Pureish;e$l.DECLARATION_TYPES=S.FLIPPED_ALIAS_KEYS.Declaration;e$l.PATTERNLIKE_TYPES=S.FLIPPED_ALIAS_KEYS.PatternLike;e$l.LVAL_TYPES=S.FLIPPED_ALIAS_KEYS.LVal;e$l.TSENTITYNAME_TYPES=S.FLIPPED_ALIAS_KEYS.TSEntityName;e$l.LITERAL_TYPES=S.FLIPPED_ALIAS_KEYS.Literal;e$l.IMMUTABLE_TYPES=S.FLIPPED_ALIAS_KEYS.Immutable;e$l.USERWHITESPACABLE_TYPES=S.FLIPPED_ALIAS_KEYS.UserWhitespacable;e$l.METHOD_TYPES=S.FLIPPED_ALIAS_KEYS.Method;e$l.OBJECTMEMBER_TYPES=S.FLIPPED_ALIAS_KEYS.ObjectMember;e$l.PROPERTY_TYPES=S.FLIPPED_ALIAS_KEYS.Property;e$l.UNARYLIKE_TYPES=S.FLIPPED_ALIAS_KEYS.UnaryLike;e$l.PATTERN_TYPES=S.FLIPPED_ALIAS_KEYS.Pattern;e$l.CLASS_TYPES=S.FLIPPED_ALIAS_KEYS.Class;const T=e$l.IMPORTOREXPORTDECLARATION_TYPES=S.FLIPPED_ALIAS_KEYS.ImportOrExportDeclaration;e$l.EXPORTDECLARATION_TYPES=S.FLIPPED_ALIAS_KEYS.ExportDeclaration;e$l.MODULESPECIFIER_TYPES=S.FLIPPED_ALIAS_KEYS.ModuleSpecifier;e$l.ACCESSOR_TYPES=S.FLIPPED_ALIAS_KEYS.Accessor;e$l.PRIVATE_TYPES=S.FLIPPED_ALIAS_KEYS.Private;e$l.FLOW_TYPES=S.FLIPPED_ALIAS_KEYS.Flow;e$l.FLOWTYPE_TYPES=S.FLIPPED_ALIAS_KEYS.FlowType;e$l.FLOWBASEANNOTATION_TYPES=S.FLIPPED_ALIAS_KEYS.FlowBaseAnnotation;e$l.FLOWDECLARATION_TYPES=S.FLIPPED_ALIAS_KEYS.FlowDeclaration;e$l.FLOWPREDICATE_TYPES=S.FLIPPED_ALIAS_KEYS.FlowPredicate;e$l.ENUMBODY_TYPES=S.FLIPPED_ALIAS_KEYS.EnumBody;e$l.ENUMMEMBER_TYPES=S.FLIPPED_ALIAS_KEYS.EnumMember;e$l.JSX_TYPES=S.FLIPPED_ALIAS_KEYS.JSX;e$l.MISCELLANEOUS_TYPES=S.FLIPPED_ALIAS_KEYS.Miscellaneous;e$l.TYPESCRIPT_TYPES=S.FLIPPED_ALIAS_KEYS.TypeScript;e$l.TSTYPEELEMENT_TYPES=S.FLIPPED_ALIAS_KEYS.TSTypeElement;e$l.TSTYPE_TYPES=S.FLIPPED_ALIAS_KEYS.TSType;e$l.TSBASETYPE_TYPES=S.FLIPPED_ALIAS_KEYS.TSBaseType;e$l.MODULEDECLARATION_TYPES=T;return e$l}

var e$k={};

var o$a={};

var o$9;function n$2(){if(o$9)return o$a;o$9=1,Object.defineProperty(o$a,"__esModule",{value:true}),o$a.default=s;var r=qn(),i=o$i();function s(e,a){if((0, r.isBlockStatement)(e))return e;let u=[];return (0, r.isEmptyStatement)(e)?u=[]:((0, r.isStatement)(e)||((0, r.isFunction)(a)?e=(0, i.returnStatement)(e):e=(0, i.expressionStatement)(e)),u=[e]),(0, i.blockStatement)(u)}return o$a}

var t$4;function c$6(){if(t$4)return e$k;t$4=1,Object.defineProperty(e$k,"__esModule",{value:true}),e$k.default=i;var s=n$2();function i(e,u="body"){const o=(0, s.default)(e[u],e);return e[u]=o,o}return e$k}

var e$j={};

var e$i={};

var a$8;function _$4(){if(a$8)return e$i;a$8=1,Object.defineProperty(e$i,"__esModule",{value:true}),e$i.default=n;var f=d$2(),u=o$l();function n(i){i=i+"";let e="";for(const t of i)e+=(0, u.isIdentifierChar)(t.codePointAt(0))?t:"-";return e=e.replace(/^[-0-9]+/,""),e=e.replace(/[-\s]+(.)?/g,function(t,o){return o?o.toUpperCase():""}),(0, f.default)(e)||(e=`_${e}`),e||"_"}return e$i}

var i$5;function n$1(){if(i$5)return e$j;i$5=1,Object.defineProperty(e$j,"__esModule",{value:true}),e$j.default=o;var t=_$4();function o(e){return e=(0, t.default)(e),(e==="eval"||e==="arguments")&&(e="_"+e),e}return e$j}

var e$h={};

var u$4;function a$7(){if(u$4)return e$h;u$4=1,Object.defineProperty(e$h,"__esModule",{value:true}),e$h.default=m;var i=qn(),o=o$i();function m(t,e=t.key||t.property){return !t.computed&&(0, i.isIdentifier)(e)&&(e=(0, o.stringLiteral)(e.name)),e}return e$h}

var o$8={};

var i$4;function u$3(){if(i$4)return o$8;i$4=1,Object.defineProperty(o$8,"__esModule",{value:true}),o$8.default=void 0;var s=qn();o$8.default=t;function t(r){if((0, s.isExpressionStatement)(r)&&(r=r.expression),(0, s.isExpression)(r))return r;if((0, s.isClass)(r)?r.type="ClassExpression":(0, s.isFunction)(r)&&(r.type="FunctionExpression"),!(0, s.isExpression)(r))throw new Error(`cannot turn ${r.type} to an expression`);return r}return o$8}

var a$6={};

var e$g={};

var r$5={};

var o$7;function m$2(){if(o$7)return r$5;o$7=1,Object.defineProperty(r$5,"__esModule",{value:true}),r$5.default=t;var f=I$1();function t(e,a,r){if(!e)return;const i=f.VISITOR_KEYS[e.type];if(i){r=r||{},a(e,r);for(const n of i){const u=e[n];if(Array.isArray(u))for(const _ of u)t(_,a,r);else t(u,a,r);}}}return r$5}

var e$f={};

var s$2;function a$5(){if(s$2)return e$f;s$2=1,Object.defineProperty(e$f,"__esModule",{value:true}),e$f.default=u;var i=B$1();const t=["tokens","start","end","loc","raw","rawValue"],n=[...i.COMMENT_KEYS,"comments",...t];function u(r,m={}){const f=m.preserveComments?t:n;for(const e of f)r[e]!=null&&(r[e]=void 0);for(const e of Object.keys(r))e[0]==="_"&&r[e]!=null&&(r[e]=void 0);const c=Object.getOwnPropertySymbols(r);for(const e of c)r[e]=null;}return e$f}

var o$6;function m$1(){if(o$6)return e$g;o$6=1,Object.defineProperty(e$g,"__esModule",{value:true}),e$g.default=i;var t=m$2(),u=a$5();function i(r,s){return (0, t.default)(r,u.default,s),r}return e$g}

var a$4;function _$3(){if(a$4)return a$6;a$4=1,Object.defineProperty(a$6,"__esModule",{value:true}),a$6.default=r;var s=qn(),o=I(),n=m$1();function r(t,i=t.key){let e;return t.kind==="method"?r.increment()+"":((0, s.isIdentifier)(i)?e=i.name:(0, s.isStringLiteral)(i)?e=JSON.stringify(i.value):e=JSON.stringify((0, n.default)((0, o.default)(i))),t.computed&&(e=`[${e}]`),t.static&&(e=`static:${e}`),e)}return r.uid=0,r.increment=function(){return r.uid>=Number.MAX_SAFE_INTEGER?r.uid=0:r.uid++},a$6}

var t$3={};

var a$3;function l$2(){if(a$3)return t$3;a$3=1,Object.defineProperty(t$3,"__esModule",{value:true}),t$3.default=void 0;var i=qn(),u=o$i();t$3.default=n;function n(e,o){if((0, i.isStatement)(e))return e;let s=false,t;if((0, i.isClass)(e))s=true,t="ClassDeclaration";else if((0, i.isFunction)(e))s=true,t="FunctionDeclaration";else if((0, i.isAssignmentExpression)(e))return (0, u.expressionStatement)(e);if(s&&!e.id&&(t=false),!t){if(o)return  false;throw new Error(`cannot turn ${e.type} to a statement`)}return e.type=t,e}return t$3}

var e$e={};

var f$4;function b$1(){if(f$4)return e$e;f$4=1,Object.defineProperty(e$e,"__esModule",{value:true}),e$e.default=void 0;var u=d$2(),e=o$i();e$e.default=n;const c=Function.call.bind(Object.prototype.toString);function p(r){return c(r)==="[object RegExp]"}function a(r){if(typeof r!="object"||r===null||Object.prototype.toString.call(r)!=="[object Object]")return  false;const t=Object.getPrototypeOf(r);return t===null||Object.getPrototypeOf(t)===null}function n(r){if(r===void 0)return (0, e.identifier)("undefined");if(r===true||r===false)return (0, e.booleanLiteral)(r);if(r===null)return (0, e.nullLiteral)();if(typeof r=="string")return (0, e.stringLiteral)(r);if(typeof r=="number"){let t;if(Number.isFinite(r))t=(0, e.numericLiteral)(Math.abs(r));else {let i;Number.isNaN(r)?i=(0, e.numericLiteral)(0):i=(0, e.numericLiteral)(1),t=(0, e.binaryExpression)("/",i,(0, e.numericLiteral)(0));}return (r<0||Object.is(r,-0))&&(t=(0, e.unaryExpression)("-",t)),t}if(p(r)){const t=r.source,i=/\/([a-z]*)$/.exec(r.toString())[1];return (0, e.regExpLiteral)(t,i)}if(Array.isArray(r))return (0, e.arrayExpression)(r.map(n));if(a(r)){const t=[];for(const i of Object.keys(r)){let s;(0, u.default)(i)?s=(0, e.identifier)(i):s=(0, e.stringLiteral)(i),t.push((0, e.objectProperty)(s,n(r[i])));}return (0, e.objectExpression)(t)}throw new Error("don't know how to turn this value into a node")}return e$e}

var e$d={};

var o$5;function u$2(){if(o$5)return e$d;o$5=1,Object.defineProperty(e$d,"__esModule",{value:true}),e$d.default=s;var p=o$i();function s(e,t,i=false){return e.object=(0, p.memberExpression)(e.object,e.property,e.computed),e.property=t,e.computed=!!i,e}return e$d}

var r$4={};

var i$3;function m(){if(i$3)return r$4;i$3=1,Object.defineProperty(r$4,"__esModule",{value:true}),r$4.default=f;var u=B$1(),s=f$5();function f(e,o){if(!e||!o)return e;for(const r of u.INHERIT_KEYS.optional)e[r]==null&&(e[r]=o[r]);for(const r of Object.keys(o))r[0]==="_"&&r!=="__clone"&&(e[r]=o[r]);for(const r of u.INHERIT_KEYS.force)e[r]=o[r];return (0, s.default)(e,o),e}return r$4}

var e$c={};

var o$4;function a$2(){if(o$4)return e$c;o$4=1,Object.defineProperty(e$c,"__esModule",{value:true}),e$c.default=i;var s=o$i(),p=Mr();function i(e,n){if((0, p.isSuper)(e.object))throw new Error("Cannot prepend node to super property access (`super.foo`).");return e.object=(0, s.memberExpression)(n,e.object),e}return e$c}

var e$b={};

var n;function c$5(){if(n)return e$b;n=1,Object.defineProperty(e$b,"__esModule",{value:true}),e$b.default=a;function a(i){const t=[].concat(i),r=Object.create(null);for(;t.length;){const e=t.pop();if(e)switch(e.type){case "ArrayPattern":t.push(...e.elements);break;case "AssignmentExpression":case "AssignmentPattern":case "ForInStatement":case "ForOfStatement":t.push(e.left);break;case "ObjectPattern":t.push(...e.properties);break;case "ObjectProperty":t.push(e.value);break;case "RestElement":case "UpdateExpression":t.push(e.argument);break;case "UnaryExpression":e.operator==="delete"&&t.push(e.argument);break;case "Identifier":r[e.name]=e;break;}}return r}return e$b}

var e$a={};

var l$1;function y$1(){if(l$1)return e$a;l$1=1,Object.defineProperty(e$a,"__esModule",{value:true}),e$a.default=o;var i=qn();function o(d,u,f,m){const r=[].concat(d),t=Object.create(null);for(;r.length;){const e=r.shift();if(!e||m&&((0, i.isAssignmentExpression)(e)||(0, i.isUnaryExpression)(e)||(0, i.isUpdateExpression)(e)))continue;if((0, i.isIdentifier)(e)){u?(t[e.name]=t[e.name]||[]).push(e):t[e.name]=e;continue}if((0, i.isExportDeclaration)(e)&&!(0, i.isExportAllDeclaration)(e)){(0, i.isDeclaration)(e.declaration)&&r.push(e.declaration);continue}if(f){if((0, i.isFunctionDeclaration)(e)){r.push(e.id);continue}if((0, i.isFunctionExpression)(e))continue}const c=o.keys[e.type];if(c)for(let a=0;a<c.length;a++){const x=c[a],n=e[x];n&&(Array.isArray(n)?r.push(...n):r.push(n));}}return t}const p={DeclareClass:["id"],DeclareFunction:["id"],DeclareModule:["id"],DeclareVariable:["id"],DeclareInterface:["id"],DeclareTypeAlias:["id"],DeclareOpaqueType:["id"],InterfaceDeclaration:["id"],TypeAlias:["id"],OpaqueType:["id"],CatchClause:["param"],LabeledStatement:["label"],UnaryExpression:["argument"],AssignmentExpression:["left"],ImportSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportDefaultSpecifier:["local"],ImportDeclaration:["specifiers"],TSImportEqualsDeclaration:["id"],ExportSpecifier:["exported"],ExportNamespaceSpecifier:["exported"],ExportDefaultSpecifier:["exported"],FunctionDeclaration:["id","params"],FunctionExpression:["id","params"],ArrowFunctionExpression:["params"],ObjectMethod:["params"],ClassMethod:["params"],ClassPrivateMethod:["params"],ForInStatement:["left"],ForOfStatement:["left"],ClassDeclaration:["id"],ClassExpression:["id"],RestElement:["argument"],UpdateExpression:["argument"],ObjectProperty:["value"],AssignmentPattern:["left"],ArrayPattern:["elements"],ObjectPattern:["properties"],VariableDeclaration:["declarations"],VariableDeclarator:["id"]};return o.keys=p,e$a}

var e$9={};

var r$3;function f$3(){if(r$3)return e$9;r$3=1,Object.defineProperty(e$9,"__esModule",{value:true}),e$9.default=void 0;var i=y$1();e$9.default=t;function t(u,n){return (0, i.default)(u,n,true)}return e$9}

var e$8={};

var s$1;function c$4(){if(s$1)return e$8;s$1=1,Object.defineProperty(e$8,"__esModule",{value:true}),e$8.default=m;var i=qn();function f(e){return (0, i.isNullLiteral)(e)?"null":(0, i.isRegExpLiteral)(e)?`/${e.pattern}/${e.flags}`:(0, i.isTemplateLiteral)(e)?e.quasis.map(t=>t.value.raw).join(""):e.value!==void 0?String(e.value):null}function a(e){if(!e.computed||(0, i.isLiteral)(e.key))return e.key}function m(e,t){if("id"in e&&e.id)return {name:e.id.name,originalNode:e.id};let l="",r;if((0, i.isObjectProperty)(t,{value:e})?r=a(t):(0, i.isObjectMethod)(e)||(0, i.isClassMethod)(e)?(r=a(e),e.kind==="get"?l="get ":e.kind==="set"&&(l="set ")):(0, i.isVariableDeclarator)(t,{init:e})?r=t.id:(0, i.isAssignmentExpression)(t,{operator:"=",right:e})&&(r=t.left),!r)return null;const n=(0, i.isLiteral)(r)?f(r):(0, i.isIdentifier)(r)?r.name:(0, i.isPrivateName)(r)?r.id.name:null;return n==null?null:{name:l+n,originalNode:r}}return e$8}

var r$2={};

var l;function q(){if(l)return r$2;l=1,Object.defineProperty(r$2,"__esModule",{value:true}),r$2.default=s;var n=I$1();function s(i,e,u){typeof e=="function"&&(e={enter:e});const{enter:f,exit:r}=e;_(i,f,r,u,[]);}function _(i,e,u,f,r){const v=n.VISITOR_KEYS[i.type];if(v){e&&e(i,r,f);for(const m of v){const o=i[m];if(Array.isArray(o))for(let p=0;p<o.length;p++){const c=o[p];c&&(r.push({node:i,key:m,index:p}),_(c,e,u,f,r),r.pop());}else o&&(r.push({node:i,key:m}),_(o,e,u,f,r),r.pop());}u&&u(i,r,f);}}return r$2}

var i$2={};

var f$2;function y(){if(f$2)return i$2;f$2=1,Object.defineProperty(i$2,"__esModule",{value:true}),i$2.default=l;var o=y$1();function l(r,i,u){if(u&&r.type==="Identifier"&&i.type==="ObjectProperty"&&u.type==="ObjectExpression")return  false;const t=o.default.keys[i.type];if(t)for(let s=0;s<t.length;s++){const a=t[s],n=i[a];if(Array.isArray(n)){if(n.includes(r))return  true}else if(n===r)return  true}return  false}return i$2}

var o$3={};

var e$7={};

var i$1;function _$2(){if(i$1)return e$7;i$1=1,Object.defineProperty(e$7,"__esModule",{value:true}),e$7.default=s;var t=qn(),u=B$1();function s(e){return (0, t.isVariableDeclaration)(e)&&(e.kind!=="var"||e[u.BLOCK_SCOPED_SYMBOL])}return e$7}

var o$2;function c$3(){if(o$2)return o$3;o$2=1,Object.defineProperty(o$3,"__esModule",{value:true}),o$3.default=u;var i=qn(),t=_$2();function u(e){return (0, i.isFunctionDeclaration)(e)||(0, i.isClassDeclaration)(e)||(0, t.default)(e)}return o$3}

var a$1={};

var u$1;function f$1(){if(u$1)return a$1;u$1=1,Object.defineProperty(a$1,"__esModule",{value:true}),a$1.default=m;var t=a$e(),i=qn();function m(r){return (0, t.default)(r.type,"Immutable")?true:(0, i.isIdentifier)(r)?r.name==="undefined":false}return a$1}

var e$6={};

var a;function p(){if(a)return e$6;a=1,Object.defineProperty(e$6,"__esModule",{value:true}),e$6.default=u;var f=I$1();function u(r,i){if(typeof r!="object"||typeof i!="object"||r==null||i==null)return r===i;if(r.type!==i.type)return  false;const c=Object.keys(f.NODE_FIELDS[r.type]||r.type),l=f.VISITOR_KEYS[r.type];for(const o of c){const e=r[o],t=i[o];if(typeof e!=typeof t)return  false;if(!(e==null&&t==null)){if(e==null||t==null)return  false;if(Array.isArray(e)){if(!Array.isArray(t)||e.length!==t.length)return  false;for(let n=0;n<e.length;n++)if(!u(e[n],t[n]))return  false;continue}if(typeof e=="object"&&!(l!=null&&l.includes(o))){for(const n of Object.keys(e))if(e[n]!==t[n])return  false;continue}if(!u(e,t))return  false}}return  true}return e$6}

var e$5={};

var c$2;function u(){if(c$2)return e$5;c$2=1,Object.defineProperty(e$5,"__esModule",{value:true}),e$5.default=a;function a(r,e,s){switch(e.type){case "MemberExpression":case "OptionalMemberExpression":return e.property===r?!!e.computed:e.object===r;case "JSXMemberExpression":return e.object===r;case "VariableDeclarator":return e.init===r;case "ArrowFunctionExpression":return e.body===r;case "PrivateName":return  false;case "ClassMethod":case "ClassPrivateMethod":case "ObjectMethod":return e.key===r?!!e.computed:false;case "ObjectProperty":return e.key===r?!!e.computed:!s||s.type!=="ObjectPattern";case "ClassProperty":case "ClassAccessorProperty":return e.key===r?!!e.computed:true;case "ClassPrivateProperty":return e.key!==r;case "ClassDeclaration":case "ClassExpression":return e.superClass===r;case "AssignmentExpression":return e.right===r;case "AssignmentPattern":return e.right===r;case "LabeledStatement":return  false;case "CatchClause":return  false;case "RestElement":return  false;case "BreakStatement":case "ContinueStatement":return  false;case "FunctionDeclaration":case "FunctionExpression":return  false;case "ExportNamespaceSpecifier":case "ExportDefaultSpecifier":return  false;case "ExportSpecifier":return s!=null&&s.source?false:e.local===r;case "ImportDefaultSpecifier":case "ImportNamespaceSpecifier":case "ImportSpecifier":return  false;case "ImportAttribute":return  false;case "JSXAttribute":return  false;case "ObjectPattern":case "ArrayPattern":return  false;case "MetaProperty":return  false;case "ObjectTypeProperty":return e.key!==r;case "TSEnumMember":return e.id!==r;case "TSPropertySignature":return e.key===r?!!e.computed:true}return  true}return e$5}

var e$4={};

var t$2;function c$1(){if(t$2)return e$4;t$2=1,Object.defineProperty(e$4,"__esModule",{value:true}),e$4.default=u;var e=qn();function u(s,r){return (0, e.isBlockStatement)(s)&&((0, e.isFunction)(r)||(0, e.isCatchClause)(r))?false:(0, e.isPattern)(s)&&((0, e.isFunction)(r)||(0, e.isCatchClause)(r))?true:(0, e.isScopable)(s)}return e$4}

var e$3={};

var t$1;function o$1(){if(t$1)return e$3;t$1=1,Object.defineProperty(e$3,"__esModule",{value:true}),e$3.default=u;var i=qn();function u(r){return (0, i.isImportDefaultSpecifier)(r)||(0, i.isIdentifier)(r.imported||r.exported,{name:"default"})}return e$3}

var e$2={};

var t;function s(){if(t)return e$2;t=1,Object.defineProperty(e$2,"__esModule",{value:true}),e$2.default=n;var i=d$2();const a=new Set(["abstract","boolean","byte","char","double","enum","final","float","goto","implements","int","interface","long","native","package","private","protected","public","short","static","synchronized","throws","transient","volatile"]);function n(r){return (0, i.default)(r)&&!a.has(r)}return e$2}

var r$1={};

var i;function _$1(){if(i)return r$1;i=1,Object.defineProperty(r$1,"__esModule",{value:true}),r$1.default=t;var a=qn(),u=B$1();function t(e){return (0, a.isVariableDeclaration)(e,{kind:"var"})&&!e[u.BLOCK_SCOPED_SYMBOL]}return r$1}

var e$1={};

var e={};

var c;function b(){if(c)return e;c=1,Object.defineProperty(e,"__esModule",{value:true}),e.default=s;var l=y$1(),t=qn(),f=o$i(),d=n$5(),m=I();function s(p,o){const i=[];let u=true;for(const e of p)if((0, t.isEmptyStatement)(e)||(u=false),(0, t.isExpression)(e))i.push(e);else if((0, t.isExpressionStatement)(e))i.push(e.expression);else if((0, t.isVariableDeclaration)(e)){if(e.kind!=="var")return;for(const r of e.declarations){const n=(0, l.default)(r);for(const _ of Object.keys(n))o.push({kind:e.kind,id:(0, m.default)(n[_])});r.init&&i.push((0, f.assignmentExpression)("=",r.id,r.init));}u=true;}else if((0, t.isIfStatement)(e)){const r=e.consequent?s([e.consequent],o):(0, d.buildUndefinedNode)(),n=e.alternate?s([e.alternate],o):(0, d.buildUndefinedNode)();if(!r||!n)return;i.push((0, f.conditionalExpression)(e.test,r,n));}else if((0, t.isBlockStatement)(e)){const r=s(e.body,o);if(!r)return;i.push(r);}else if((0, t.isEmptyStatement)(e))p.indexOf(e)===0&&(u=true);else return;return u&&i.push((0, d.buildUndefinedNode)()),i.length===1?i[0]:(0, f.sequenceExpression)(i)}return e}

var o;function f(){if(o)return e$1;o=1,Object.defineProperty(e$1,"__esModule",{value:true}),e$1.default=n;var s=b();function n(r,i){if(!(r!=null&&r.length))return;const u=[],t=(0, s.default)(r,u);if(t){for(const c of u)i.push(c);return t}}return e$1}

var _;function Mr(){return _?r$w:(_=1,function(e){Object.defineProperty(e,"__esModule",{value:true});var t={react:true,assertNode:true,createTypeAnnotationBasedOnTypeof:true,createUnionTypeAnnotation:true,createFlowUnionType:true,createTSUnionType:true,cloneNode:true,clone:true,cloneDeep:true,cloneDeepWithoutLoc:true,cloneWithoutLoc:true,addComment:true,addComments:true,inheritInnerComments:true,inheritLeadingComments:true,inheritsComments:true,inheritTrailingComments:true,removeComments:true,ensureBlock:true,toBindingIdentifierName:true,toBlock:true,toComputedKey:true,toExpression:true,toIdentifier:true,toKeyAlias:true,toStatement:true,valueToNode:true,appendToMemberExpression:true,inherits:true,prependToMemberExpression:true,removeProperties:true,removePropertiesDeep:true,removeTypeDuplicates:true,getAssignmentIdentifiers:true,getBindingIdentifiers:true,getOuterBindingIdentifiers:true,getFunctionName:true,traverse:true,traverseFast:true,shallowEqual:true,is:true,isBinding:true,isBlockScoped:true,isImmutable:true,isLet:true,isNode:true,isNodesEquivalent:true,isPlaceholderType:true,isReferenced:true,isScope:true,isSpecifierDefault:true,isType:true,isValidES3Identifier:true,isValidIdentifier:true,isVar:true,matchesPattern:true,validate:true,buildMatchMemberExpression:true,__internal__deprecationWarning:true};Object.defineProperty(e,"__internal__deprecationWarning",{enumerable:true,get:function(){return qe.default}}),Object.defineProperty(e,"addComment",{enumerable:true,get:function(){return h.default}}),Object.defineProperty(e,"addComments",{enumerable:true,get:function(){return T$1.default}}),Object.defineProperty(e,"appendToMemberExpression",{enumerable:true,get:function(){return W.default}}),Object.defineProperty(e,"assertNode",{enumerable:true,get:function(){return q$1.default}}),Object.defineProperty(e,"buildMatchMemberExpression",{enumerable:true,get:function(){return be.default}}),Object.defineProperty(e,"clone",{enumerable:true,get:function(){return O.default}}),Object.defineProperty(e,"cloneDeep",{enumerable:true,get:function(){return P.default}}),Object.defineProperty(e,"cloneDeepWithoutLoc",{enumerable:true,get:function(){return j.default}}),Object.defineProperty(e,"cloneNode",{enumerable:true,get:function(){return v.default}}),Object.defineProperty(e,"cloneWithoutLoc",{enumerable:true,get:function(){return y$2.default}}),Object.defineProperty(e,"createFlowUnionType",{enumerable:true,get:function(){return l.default}}),Object.defineProperty(e,"createTSUnionType",{enumerable:true,get:function(){return g.default}}),Object.defineProperty(e,"createTypeAnnotationBasedOnTypeof",{enumerable:true,get:function(){return $.default}}),Object.defineProperty(e,"createUnionTypeAnnotation",{enumerable:true,get:function(){return l.default}}),Object.defineProperty(e,"ensureBlock",{enumerable:true,get:function(){return S.default}}),Object.defineProperty(e,"getAssignmentIdentifiers",{enumerable:true,get:function(){return z.default}}),Object.defineProperty(e,"getBindingIdentifiers",{enumerable:true,get:function(){return J.default}}),Object.defineProperty(e,"getFunctionName",{enumerable:true,get:function(){return X.default}}),Object.defineProperty(e,"getOuterBindingIdentifiers",{enumerable:true,get:function(){return Q.default}}),Object.defineProperty(e,"inheritInnerComments",{enumerable:true,get:function(){return E.default}}),Object.defineProperty(e,"inheritLeadingComments",{enumerable:true,get:function(){return B.default}}),Object.defineProperty(e,"inheritTrailingComments",{enumerable:true,get:function(){return I$2.default}}),Object.defineProperty(e,"inherits",{enumerable:true,get:function(){return K.default}}),Object.defineProperty(e,"inheritsComments",{enumerable:true,get:function(){return C.default}}),Object.defineProperty(e,"is",{enumerable:true,get:function(){return x.default}}),Object.defineProperty(e,"isBinding",{enumerable:true,get:function(){return ee.default}}),Object.defineProperty(e,"isBlockScoped",{enumerable:true,get:function(){return re.default}}),Object.defineProperty(e,"isImmutable",{enumerable:true,get:function(){return te.default}}),Object.defineProperty(e,"isLet",{enumerable:true,get:function(){return ue.default}}),Object.defineProperty(e,"isNode",{enumerable:true,get:function(){return ie.default}}),Object.defineProperty(e,"isNodesEquivalent",{enumerable:true,get:function(){return ne.default}}),Object.defineProperty(e,"isPlaceholderType",{enumerable:true,get:function(){return oe.default}}),Object.defineProperty(e,"isReferenced",{enumerable:true,get:function(){return ae.default}}),Object.defineProperty(e,"isScope",{enumerable:true,get:function(){return fe.default}}),Object.defineProperty(e,"isSpecifierDefault",{enumerable:true,get:function(){return me.default}}),Object.defineProperty(e,"isType",{enumerable:true,get:function(){return ce.default}}),Object.defineProperty(e,"isValidES3Identifier",{enumerable:true,get:function(){return de.default}}),Object.defineProperty(e,"isValidIdentifier",{enumerable:true,get:function(){return le.default}}),Object.defineProperty(e,"isVar",{enumerable:true,get:function(){return _e.default}}),Object.defineProperty(e,"matchesPattern",{enumerable:true,get:function(){return se.default}}),Object.defineProperty(e,"prependToMemberExpression",{enumerable:true,get:function(){return R.default}}),e.react=void 0,Object.defineProperty(e,"removeComments",{enumerable:true,get:function(){return N.default}}),Object.defineProperty(e,"removeProperties",{enumerable:true,get:function(){return G.default}}),Object.defineProperty(e,"removePropertiesDeep",{enumerable:true,get:function(){return Y$1.default}}),Object.defineProperty(e,"removeTypeDuplicates",{enumerable:true,get:function(){return H.default}}),Object.defineProperty(e,"shallowEqual",{enumerable:true,get:function(){return k.default}}),Object.defineProperty(e,"toBindingIdentifierName",{enumerable:true,get:function(){return M.default}}),Object.defineProperty(e,"toBlock",{enumerable:true,get:function(){return L.default}}),Object.defineProperty(e,"toComputedKey",{enumerable:true,get:function(){return w.default}}),Object.defineProperty(e,"toExpression",{enumerable:true,get:function(){return A.default}}),Object.defineProperty(e,"toIdentifier",{enumerable:true,get:function(){return D.default}}),Object.defineProperty(e,"toKeyAlias",{enumerable:true,get:function(){return F.default}}),Object.defineProperty(e,"toStatement",{enumerable:true,get:function(){return U.default}}),Object.defineProperty(e,"traverse",{enumerable:true,get:function(){return u$1.default}}),Object.defineProperty(e,"traverseFast",{enumerable:true,get:function(){return Z.default}}),Object.defineProperty(e,"validate",{enumerable:true,get:function(){return pe.default}}),Object.defineProperty(e,"valueToNode",{enumerable:true,get:function(){return V.default}});var s$1=u$a(),p$1=s$8(),b=f$8(),q$1=f$7(),i=zn();Object.keys(i).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===i[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return i[r]}});});var $=a$b(),l=s$3(),g=T(),n=n$5();Object.keys(n).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===n[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return n[r]}});});var o=o$i();Object.keys(o).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===o[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return o[r]}});});var v=I(),O=i$b(),P=i$a(),j=n$4(),y$2=n$3(),h=i$8(),T$1=f$6(),E=u$7(),B=u$6(),C=f$5(),I$2=u$5(),N=i$6(),a=Y();Object.keys(a).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===a[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return a[r]}});});var f$2=B$1();Object.keys(f$2).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===f$2[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return f$2[r]}});});var S=c$6(),M=n$1(),L=n$2(),w=a$7(),A=u$3(),D=_$4(),F=_$3(),U=l$2(),V=b$1(),m$5=I$1();Object.keys(m$5).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===m$5[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return m$5[r]}});});var W=u$2(),K=m(),R=a$2(),G=a$5(),Y$1=m$1(),H=_$5(),z=c$5(),J=y$1(),Q=f$3(),X=c$4(),u$1=q();Object.keys(u$1).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===u$1[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return u$1[r]}});});var Z=m$2(),k=a$h(),x=c$7(),ee=y(),re=c$3(),te=f$1(),ue=_$2(),ie=s$4(),ne=p(),oe=n$8(),ae=u(),fe=c$1(),me=o$1(),ce=a$e(),de=s(),le=d$2(),_e=_$1(),se=d$4(),pe=N$1(),be=m$3(),c=qn();Object.keys(c).forEach(function(r){r==="default"||r==="__esModule"||Object.prototype.hasOwnProperty.call(t,r)||r in e&&e[r]===c[r]||Object.defineProperty(e,r,{enumerable:true,get:function(){return c[r]}});});var qe=m$4(),$e=f();e.react={isReactComponent:s$1.default,isCompatTag:p$1.default,buildChildren:b.default};e.toSequenceExpression=$e.default,process.env.BABEL_TYPES_8_BREAKING&&console.warn("BABEL_TYPES_8_BREAKING is not supported anymore. Use the latest Babel 8.0.0 pre-release instead!");}(r$w),r$w)}

var r=Mr();getDefaultExportFromCjs(r);

const version = "2.0.0";

const babelPluginUntyped = function(api, options) {
  const name = "untyped";
  api.cache.using(() => version);
  return {
    name,
    manipulateOptions(opts) {
      opts.plugins.sort((a) => a.key === name ? -1 : 0);
    },
    visitor: {
      VariableDeclaration(p) {
        const declaration = p.node.declarations[0];
        if (r.isIdentifier(declaration.id) && (r.isFunctionExpression(declaration.init) || r.isArrowFunctionExpression(declaration.init))) {
          const newDeclaration = r.functionDeclaration(
            declaration.id,
            declaration.init.params,
            r.isBlockStatement(declaration.init.body) ? declaration.init.body : r.blockStatement([r.returnStatement(declaration.init.body)])
          );
          newDeclaration.returnType = declaration.init.returnType;
          p.replaceWith(newDeclaration);
        }
      },
      ObjectProperty(p) {
        if (p.node.leadingComments && p.node.leadingComments.length > 0) {
          const schema = parseJSDocs(
            p.node.leadingComments.filter((c) => c.type === "CommentBlock").map((c) => c.value)
          );
          const valueNode = p.node.value.type === "TSTypeAssertion" || p.node.value.type === "TSAsExpression" ? p.node.value.expression : p.node.value;
          if (valueNode.type === "ObjectExpression") {
            const schemaProp = valueNode.properties.find(
              (prop) => "key" in prop && prop.key.type === "Identifier" && prop.key.name === "$schema"
            );
            if (schemaProp && "value" in schemaProp) {
              if (schemaProp.value.type === "ObjectExpression") {
                schemaProp.value.properties.push(
                  ...astify(schema).properties
                );
              }
            } else {
              valueNode.properties.unshift(
                ...astify({ $schema: schema }).properties
              );
            }
          } else {
            p.node.value = r.objectExpression([
              r.objectProperty(r.identifier("$default"), valueNode),
              r.objectProperty(r.identifier("$schema"), astify(schema))
            ]);
          }
          p.node.leadingComments = [];
        }
      },
      FunctionDeclaration(p) {
        const schema = parseJSDocs(
          (p.parent.leadingComments || []).filter((c) => c.type === "CommentBlock").map((c) => c.value)
        );
        schema.type = "function";
        schema.args = [];
        if (!options.experimentalFunctions && !schema.tags?.includes("@untyped")) {
          return;
        }
        if (p.parent.type !== "ExportNamedDeclaration" && p.parent.type !== "ExportDefaultDeclaration") {
          return;
        }
        const _getLines = cachedFn(() => this.file.code.split("\n"));
        const getCode = (loc) => {
          if (!loc) {
            return "";
          }
          const _lines = _getLines();
          return _lines[loc.start.line - 1]?.slice(loc.start.column, loc.end.column).trim() || "";
        };
        for (const [index, param] of p.node.params.entries()) {
          if (param.loc?.end.line !== param.loc?.start.line) {
            continue;
          }
          if (!r.isAssignmentPattern(param) && !r.isIdentifier(param)) {
            continue;
          }
          const lparam = r.isAssignmentPattern(param) ? param.left : param;
          if (!r.isIdentifier(lparam)) {
            continue;
          }
          const arg = {
            name: lparam.name || "arg" + index,
            optional: lparam.optional || void 0
          };
          if (lparam.typeAnnotation) {
            Object.assign(
              arg,
              mergedTypes(
                arg,
                inferAnnotationType(lparam.typeAnnotation, getCode)
              )
            );
          }
          if (param.type === "AssignmentPattern") {
            Object.assign(
              arg,
              mergedTypes(arg, inferArgType(param.right))
            );
          }
          schema.args = schema.args || [];
          schema.args.push(arg);
        }
        if (p.node.returnType?.type === "TSTypeAnnotation") {
          schema.returns = inferAnnotationType(p.node.returnType, getCode);
        }
        schema.tags = schema.tags?.filter((tag) => {
          if (tag.startsWith("@returns")) {
            const { type } = tag.match(/^@returns\s+{(?<type>[\S\s]+)}/)?.groups || {};
            if (type) {
              schema.returns = schema.returns || {};
              Object.assign(schema.returns, getTypeDescriptor(type));
              return false;
            }
          }
          if (tag.startsWith("@param")) {
            const { type, param } = tag.match(/^@param\s+{(?<type>[\S\s]+)}\s+(?<param>\w+)/)?.groups || {};
            if (type && param) {
              const arg = schema.args?.find((arg2) => arg2.name === param);
              if (arg) {
                Object.assign(arg, getTypeDescriptor(type));
                return false;
              }
            }
          }
          return true;
        });
        if (p.parent.type === "ExportDefaultDeclaration") {
          p.replaceWith(astify({ $schema: schema }));
        } else {
          p.replaceWith(
            r.variableDeclaration("const", [
              r.variableDeclarator(
                r.identifier(p.node.id.name),
                astify({ $schema: schema })
              )
            ])
          );
        }
      }
    }
  };
};
function containsIncompleteCodeblock(line = "") {
  const codeDelimiters = line.split("\n").filter((line2) => line2.startsWith("```")).length;
  return !!(codeDelimiters % 2);
}
function clumpLines(lines, delimiters = [" "], separator = " ") {
  const clumps = [];
  while (lines.length > 0) {
    const line = lines.shift();
    if (line && !delimiters.includes(line[0]) && clumps.at(-1) || containsIncompleteCodeblock(clumps.at(-1))) {
      clumps[clumps.length - 1] += separator + line;
    } else {
      clumps.push(line);
    }
  }
  return clumps.filter(Boolean);
}
function parseJSDocs(input) {
  const schema = {
    title: "",
    description: "",
    tags: []
  };
  const lines = (Array.isArray(input) ? input : [input]).flatMap(
    (c) => c.split("\n").map((l) => l.replace(/(^\s*\*+ )|([\s*]+$)/g, ""))
  );
  const firstTag = lines.findIndex((l) => l.startsWith("@"));
  const comments = clumpLines(
    lines.slice(0, firstTag === -1 ? void 0 : firstTag)
  );
  if (comments.length === 1) {
    schema.title = comments[0];
  } else if (comments.length > 1) {
    schema.title = comments[0];
    schema.description = comments.splice(1).join("\n");
  }
  if (firstTag !== -1) {
    const tags = clumpLines(lines.slice(firstTag), ["@"], "\n");
    const typedefs = tags.reduce(
      (typedefs2, tag) => {
        const { typedef, alias } = tag.match(/@typedef\s+{(?<typedef>[\S\s]+)} (?<alias>.*)/)?.groups || {};
        if (typedef && alias) {
          typedefs2[typedef] = alias;
        }
        return typedefs2;
      },
      {}
    );
    for (const tag of tags) {
      if (tag.startsWith("@type")) {
        const type = tag.match(/@type\s+{([\S\s]+)}/)?.[1];
        if (!type) {
          continue;
        }
        Object.assign(schema, getTypeDescriptor(type));
        for (const typedef in typedefs) {
          schema.markdownType = type;
          if (schema.tsType) {
            schema.tsType = schema.tsType.replace(
              new RegExp(typedefs[typedef], "g"),
              typedef
            );
          }
        }
        continue;
      }
      schema.tags.push(tag.trim());
    }
  }
  return schema;
}
function astify(val) {
  if (typeof val === "string") {
    return r.stringLiteral(val);
  }
  if (typeof val === "boolean") {
    return r.booleanLiteral(val);
  }
  if (typeof val === "number") {
    return r.numericLiteral(val);
  }
  if (val === null) {
    return r.nullLiteral();
  }
  if (val === void 0) {
    return r.identifier("undefined");
  }
  if (Array.isArray(val)) {
    return r.arrayExpression(val.map((item) => astify(item)));
  }
  return r.objectExpression(
    Object.getOwnPropertyNames(val).filter(
      (key) => val[key] !== void 0 && val[key] !== null
    ).map(
      (key) => r.objectProperty(
        r.identifier(key),
        astify(val[key])
      )
    )
  );
}
const AST_JSTYPE_MAP = {
  StringLiteral: "string",
  BooleanLiteral: "boolean",
  BigIntLiteral: "bigint",
  DecimalLiteral: "number",
  NumericLiteral: "number",
  ObjectExpression: "object",
  FunctionExpression: "function",
  ArrowFunctionExpression: "function",
  RegExpLiteral: "RegExp"
};
function inferArgType(e, getCode) {
  if (AST_JSTYPE_MAP[e.type]) {
    return getTypeDescriptor(AST_JSTYPE_MAP[e.type]);
  }
  if (e.type === "AssignmentExpression") {
    return inferArgType(e.right);
  }
  if (e.type === "NewExpression" && e.callee.type === "Identifier") {
    return getTypeDescriptor(e.callee.name);
  }
  if (e.type === "ArrayExpression" || e.type === "TupleExpression") {
    const itemTypes = e.elements.filter((el) => r.isExpression(el)).flatMap((el) => inferArgType(el).type);
    return {
      type: "array",
      items: {
        type: normalizeTypes(itemTypes)
      }
    };
  }
  return {};
}
function inferAnnotationType(ann, getCode) {
  if (ann?.type !== "TSTypeAnnotation") {
    return void 0;
  }
  return inferTSType(ann.typeAnnotation, getCode);
}
function inferTSType(tsType, getCode) {
  if (tsType.type === "TSParenthesizedType") {
    return inferTSType(tsType.typeAnnotation, getCode);
  }
  if (tsType.type === "TSTypeReference") {
    if (tsType.typeParameters && "name" in tsType.typeName && tsType.typeName.name === "Array") {
      return {
        type: "array",
        items: inferTSType(tsType.typeParameters.params[0], getCode)
      };
    }
    return getTypeDescriptor(getCode(tsType.loc));
  }
  if (tsType.type === "TSUnionType") {
    return mergedTypes(...tsType.types.map((t2) => inferTSType(t2, getCode)));
  }
  if (tsType.type === "TSArrayType") {
    return {
      type: "array",
      items: inferTSType(tsType.elementType, getCode)
    };
  }
  return getTypeDescriptor(getCode(tsType.loc));
}

export { babelPluginUntyped as default };
