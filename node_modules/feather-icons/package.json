{"name": "feather-icons", "version": "4.29.2", "description": "Simply beautiful open source icons", "repository": {"type": "git", "url": "https://github.com/feathericons/feather.git"}, "author": "<PERSON> <<EMAIL>> (https://colebemis.com)", "license": "MIT", "main": "dist/feather.js", "unpkg": "dist/feather.min.js", "files": ["dist"], "scripts": {"setup": "./bin/setup.sh", "build": "./bin/build.sh", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "format": "prettier --write .", "optimize-svgs": "babel-node bin/optimize-svgs.js", "release": "yarn build && changeset publish"}, "jest": {"collectCoverageFrom": ["src/**/*.js"]}, "dependencies": {"classnames": "^2.2.5", "core-js": "^3.1.3"}, "devDependencies": {"@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.26.2", "babel-cli": "^6.24.1", "babel-loader": "^7.1.1", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "cheerio": "^1.0.0-rc.2", "eslint": "^8.47.0", "html-minifier": "^3.5.8", "jest": "^22.4.4", "prettier": "^2.8.8", "svgo": "^0.7.2", "webpack": "^4.8.3", "webpack-cli": "^2.1.3"}, "prettier": {"singleQuote": true, "trailingComma": "all", "arrowParens": "avoid"}}