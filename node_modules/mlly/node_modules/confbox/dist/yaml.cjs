"use strict";const _format=require("./shared/confbox.3768c7e9.cjs");/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */function isNothing(e){return typeof e>"u"||e===null}function isObject(e){return typeof e=="object"&&e!==null}function toArray(e){return Array.isArray(e)?e:isNothing(e)?[]:[e]}function extend(e,n){var r,o,l,f;if(n)for(f=Object.keys(n),r=0,o=f.length;r<o;r+=1)l=f[r],e[l]=n[l];return e}function repeat(e,n){var r="",o;for(o=0;o<n;o+=1)r+=e;return r}function isNegativeZero(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}var isNothing_1=isNothing,isObject_1=isObject,toArray_1=toArray,repeat_1=repeat,isNegativeZero_1=isNegativeZero,extend_1=extend,common={isNothing:isNothing_1,isObject:isObject_1,toArray:toArray_1,repeat:repeat_1,isNegativeZero:isNegativeZero_1,extend:extend_1};function formatError(e,n){var r="",o=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!n&&e.mark.snippet&&(r+=`

`+e.mark.snippet),o+" "+r):o}function YAMLException$1(e,n){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=n,this.message=formatError(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}YAMLException$1.prototype=Object.create(Error.prototype),YAMLException$1.prototype.constructor=YAMLException$1,YAMLException$1.prototype.toString=function(n){return this.name+": "+formatError(this,n)};var exception=YAMLException$1;function getLine(e,n,r,o,l){var f="",u="",c=Math.floor(l/2)-1;return o-n>c&&(f=" ... ",n=o-c+f.length),r-o>c&&(u=" ...",r=o+c-u.length),{str:f+e.slice(n,r).replace(/\t/g,"\u2192")+u,pos:o-n+f.length}}function padStart(e,n){return common.repeat(" ",n-e.length)+e}function makeSnippet(e,n){if(n=Object.create(n||null),!e.buffer)return null;n.maxLength||(n.maxLength=79),typeof n.indent!="number"&&(n.indent=1),typeof n.linesBefore!="number"&&(n.linesBefore=3),typeof n.linesAfter!="number"&&(n.linesAfter=2);for(var r=/\r?\n|\r|\0/g,o=[0],l=[],f,u=-1;f=r.exec(e.buffer);)l.push(f.index),o.push(f.index+f[0].length),e.position<=f.index&&u<0&&(u=o.length-2);u<0&&(u=o.length-1);var c="",a,p,h=Math.min(e.line+n.linesAfter,l.length).toString().length,t=n.maxLength-(n.indent+h+3);for(a=1;a<=n.linesBefore&&!(u-a<0);a++)p=getLine(e.buffer,o[u-a],l[u-a],e.position-(o[u]-o[u-a]),t),c=common.repeat(" ",n.indent)+padStart((e.line-a+1).toString(),h)+" | "+p.str+`
`+c;for(p=getLine(e.buffer,o[u],l[u],e.position,t),c+=common.repeat(" ",n.indent)+padStart((e.line+1).toString(),h)+" | "+p.str+`
`,c+=common.repeat("-",n.indent+h+3+p.pos)+`^
`,a=1;a<=n.linesAfter&&!(u+a>=l.length);a++)p=getLine(e.buffer,o[u+a],l[u+a],e.position-(o[u]-o[u+a]),t),c+=common.repeat(" ",n.indent)+padStart((e.line+a+1).toString(),h)+" | "+p.str+`
`;return c.replace(/\n$/,"")}var snippet=makeSnippet,TYPE_CONSTRUCTOR_OPTIONS=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],YAML_NODE_KINDS=["scalar","sequence","mapping"];function compileStyleAliases(e){var n={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(o){n[String(o)]=r})}),n}function Type$1(e,n){if(n=n||{},Object.keys(n).forEach(function(r){if(TYPE_CONSTRUCTOR_OPTIONS.indexOf(r)===-1)throw new exception('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=n,this.tag=e,this.kind=n.kind||null,this.resolve=n.resolve||function(){return!0},this.construct=n.construct||function(r){return r},this.instanceOf=n.instanceOf||null,this.predicate=n.predicate||null,this.represent=n.represent||null,this.representName=n.representName||null,this.defaultStyle=n.defaultStyle||null,this.multi=n.multi||!1,this.styleAliases=compileStyleAliases(n.styleAliases||null),YAML_NODE_KINDS.indexOf(this.kind)===-1)throw new exception('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var type=Type$1;function compileList(e,n){var r=[];return e[n].forEach(function(o){var l=r.length;r.forEach(function(f,u){f.tag===o.tag&&f.kind===o.kind&&f.multi===o.multi&&(l=u)}),r[l]=o}),r}function compileMap(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},n,r;function o(l){l.multi?(e.multi[l.kind].push(l),e.multi.fallback.push(l)):e[l.kind][l.tag]=e.fallback[l.tag]=l}for(n=0,r=arguments.length;n<r;n+=1)arguments[n].forEach(o);return e}function Schema$1(e){return this.extend(e)}Schema$1.prototype.extend=function(n){var r=[],o=[];if(n instanceof type)o.push(n);else if(Array.isArray(n))o=o.concat(n);else if(n&&(Array.isArray(n.implicit)||Array.isArray(n.explicit)))n.implicit&&(r=r.concat(n.implicit)),n.explicit&&(o=o.concat(n.explicit));else throw new exception("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(f){if(!(f instanceof type))throw new exception("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(f.loadKind&&f.loadKind!=="scalar")throw new exception("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(f.multi)throw new exception("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),o.forEach(function(f){if(!(f instanceof type))throw new exception("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var l=Object.create(Schema$1.prototype);return l.implicit=(this.implicit||[]).concat(r),l.explicit=(this.explicit||[]).concat(o),l.compiledImplicit=compileList(l,"implicit"),l.compiledExplicit=compileList(l,"explicit"),l.compiledTypeMap=compileMap(l.compiledImplicit,l.compiledExplicit),l};var schema=Schema$1,str=new type("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return e!==null?e:""}}),seq=new type("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return e!==null?e:[]}}),map=new type("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return e!==null?e:{}}}),failsafe=new schema({explicit:[str,seq,map]});function resolveYamlNull(e){if(e===null)return!0;var n=e.length;return n===1&&e==="~"||n===4&&(e==="null"||e==="Null"||e==="NULL")}function constructYamlNull(){return null}function isNull(e){return e===null}var _null=new type("tag:yaml.org,2002:null",{kind:"scalar",resolve:resolveYamlNull,construct:constructYamlNull,predicate:isNull,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});function resolveYamlBoolean(e){if(e===null)return!1;var n=e.length;return n===4&&(e==="true"||e==="True"||e==="TRUE")||n===5&&(e==="false"||e==="False"||e==="FALSE")}function constructYamlBoolean(e){return e==="true"||e==="True"||e==="TRUE"}function isBoolean(e){return Object.prototype.toString.call(e)==="[object Boolean]"}var bool=new type("tag:yaml.org,2002:bool",{kind:"scalar",resolve:resolveYamlBoolean,construct:constructYamlBoolean,predicate:isBoolean,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"});function isHexCode(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function isOctCode(e){return 48<=e&&e<=55}function isDecCode(e){return 48<=e&&e<=57}function resolveYamlInteger(e){if(e===null)return!1;var n=e.length,r=0,o=!1,l;if(!n)return!1;if(l=e[r],(l==="-"||l==="+")&&(l=e[++r]),l==="0"){if(r+1===n)return!0;if(l=e[++r],l==="b"){for(r++;r<n;r++)if(l=e[r],l!=="_"){if(l!=="0"&&l!=="1")return!1;o=!0}return o&&l!=="_"}if(l==="x"){for(r++;r<n;r++)if(l=e[r],l!=="_"){if(!isHexCode(e.charCodeAt(r)))return!1;o=!0}return o&&l!=="_"}if(l==="o"){for(r++;r<n;r++)if(l=e[r],l!=="_"){if(!isOctCode(e.charCodeAt(r)))return!1;o=!0}return o&&l!=="_"}}if(l==="_")return!1;for(;r<n;r++)if(l=e[r],l!=="_"){if(!isDecCode(e.charCodeAt(r)))return!1;o=!0}return!(!o||l==="_")}function constructYamlInteger(e){var n=e,r=1,o;if(n.indexOf("_")!==-1&&(n=n.replace(/_/g,"")),o=n[0],(o==="-"||o==="+")&&(o==="-"&&(r=-1),n=n.slice(1),o=n[0]),n==="0")return 0;if(o==="0"){if(n[1]==="b")return r*parseInt(n.slice(2),2);if(n[1]==="x")return r*parseInt(n.slice(2),16);if(n[1]==="o")return r*parseInt(n.slice(2),8)}return r*parseInt(n,10)}function isInteger(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!common.isNegativeZero(e)}var int=new type("tag:yaml.org,2002:int",{kind:"scalar",resolve:resolveYamlInteger,construct:constructYamlInteger,predicate:isInteger,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),YAML_FLOAT_PATTERN=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function resolveYamlFloat(e){return!(e===null||!YAML_FLOAT_PATTERN.test(e)||e[e.length-1]==="_")}function constructYamlFloat(e){var n,r;return n=e.replace(/_/g,"").toLowerCase(),r=n[0]==="-"?-1:1,"+-".indexOf(n[0])>=0&&(n=n.slice(1)),n===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:n===".nan"?NaN:r*parseFloat(n,10)}var SCIENTIFIC_WITHOUT_DOT=/^[-+]?[0-9]+e/;function representYamlFloat(e,n){var r;if(isNaN(e))switch(n){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(n){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(n){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(common.isNegativeZero(e))return"-0.0";return r=e.toString(10),SCIENTIFIC_WITHOUT_DOT.test(r)?r.replace("e",".e"):r}function isFloat(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||common.isNegativeZero(e))}var float=new type("tag:yaml.org,2002:float",{kind:"scalar",resolve:resolveYamlFloat,construct:constructYamlFloat,predicate:isFloat,represent:representYamlFloat,defaultStyle:"lowercase"}),json=failsafe.extend({implicit:[_null,bool,int,float]}),core=json,YAML_DATE_REGEXP=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),YAML_TIMESTAMP_REGEXP=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function resolveYamlTimestamp(e){return e===null?!1:YAML_DATE_REGEXP.exec(e)!==null||YAML_TIMESTAMP_REGEXP.exec(e)!==null}function constructYamlTimestamp(e){var n,r,o,l,f,u,c,a=0,p=null,h,t,d;if(n=YAML_DATE_REGEXP.exec(e),n===null&&(n=YAML_TIMESTAMP_REGEXP.exec(e)),n===null)throw new Error("Date resolve error");if(r=+n[1],o=+n[2]-1,l=+n[3],!n[4])return new Date(Date.UTC(r,o,l));if(f=+n[4],u=+n[5],c=+n[6],n[7]){for(a=n[7].slice(0,3);a.length<3;)a+="0";a=+a}return n[9]&&(h=+n[10],t=+(n[11]||0),p=(h*60+t)*6e4,n[9]==="-"&&(p=-p)),d=new Date(Date.UTC(r,o,l,f,u,c,a)),p&&d.setTime(d.getTime()-p),d}function representYamlTimestamp(e){return e.toISOString()}var timestamp=new type("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:resolveYamlTimestamp,construct:constructYamlTimestamp,instanceOf:Date,represent:representYamlTimestamp});function resolveYamlMerge(e){return e==="<<"||e===null}var merge=new type("tag:yaml.org,2002:merge",{kind:"scalar",resolve:resolveYamlMerge}),BASE64_MAP=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function resolveYamlBinary(e){if(e===null)return!1;var n,r,o=0,l=e.length,f=BASE64_MAP;for(r=0;r<l;r++)if(n=f.indexOf(e.charAt(r)),!(n>64)){if(n<0)return!1;o+=6}return o%8===0}function constructYamlBinary(e){var n,r,o=e.replace(/[\r\n=]/g,""),l=o.length,f=BASE64_MAP,u=0,c=[];for(n=0;n<l;n++)n%4===0&&n&&(c.push(u>>16&255),c.push(u>>8&255),c.push(u&255)),u=u<<6|f.indexOf(o.charAt(n));return r=l%4*6,r===0?(c.push(u>>16&255),c.push(u>>8&255),c.push(u&255)):r===18?(c.push(u>>10&255),c.push(u>>2&255)):r===12&&c.push(u>>4&255),new Uint8Array(c)}function representYamlBinary(e){var n="",r=0,o,l,f=e.length,u=BASE64_MAP;for(o=0;o<f;o++)o%3===0&&o&&(n+=u[r>>18&63],n+=u[r>>12&63],n+=u[r>>6&63],n+=u[r&63]),r=(r<<8)+e[o];return l=f%3,l===0?(n+=u[r>>18&63],n+=u[r>>12&63],n+=u[r>>6&63],n+=u[r&63]):l===2?(n+=u[r>>10&63],n+=u[r>>4&63],n+=u[r<<2&63],n+=u[64]):l===1&&(n+=u[r>>2&63],n+=u[r<<4&63],n+=u[64],n+=u[64]),n}function isBinary(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}var binary=new type("tag:yaml.org,2002:binary",{kind:"scalar",resolve:resolveYamlBinary,construct:constructYamlBinary,predicate:isBinary,represent:representYamlBinary}),_hasOwnProperty$3=Object.prototype.hasOwnProperty,_toString$2=Object.prototype.toString;function resolveYamlOmap(e){if(e===null)return!0;var n=[],r,o,l,f,u,c=e;for(r=0,o=c.length;r<o;r+=1){if(l=c[r],u=!1,_toString$2.call(l)!=="[object Object]")return!1;for(f in l)if(_hasOwnProperty$3.call(l,f))if(!u)u=!0;else return!1;if(!u)return!1;if(n.indexOf(f)===-1)n.push(f);else return!1}return!0}function constructYamlOmap(e){return e!==null?e:[]}var omap=new type("tag:yaml.org,2002:omap",{kind:"sequence",resolve:resolveYamlOmap,construct:constructYamlOmap}),_toString$1=Object.prototype.toString;function resolveYamlPairs(e){if(e===null)return!0;var n,r,o,l,f,u=e;for(f=new Array(u.length),n=0,r=u.length;n<r;n+=1){if(o=u[n],_toString$1.call(o)!=="[object Object]"||(l=Object.keys(o),l.length!==1))return!1;f[n]=[l[0],o[l[0]]]}return!0}function constructYamlPairs(e){if(e===null)return[];var n,r,o,l,f,u=e;for(f=new Array(u.length),n=0,r=u.length;n<r;n+=1)o=u[n],l=Object.keys(o),f[n]=[l[0],o[l[0]]];return f}var pairs=new type("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:resolveYamlPairs,construct:constructYamlPairs}),_hasOwnProperty$2=Object.prototype.hasOwnProperty;function resolveYamlSet(e){if(e===null)return!0;var n,r=e;for(n in r)if(_hasOwnProperty$2.call(r,n)&&r[n]!==null)return!1;return!0}function constructYamlSet(e){return e!==null?e:{}}var set=new type("tag:yaml.org,2002:set",{kind:"mapping",resolve:resolveYamlSet,construct:constructYamlSet}),_default=core.extend({implicit:[timestamp,merge],explicit:[binary,omap,pairs,set]}),_hasOwnProperty$1=Object.prototype.hasOwnProperty,CONTEXT_FLOW_IN=1,CONTEXT_FLOW_OUT=2,CONTEXT_BLOCK_IN=3,CONTEXT_BLOCK_OUT=4,CHOMPING_CLIP=1,CHOMPING_STRIP=2,CHOMPING_KEEP=3,PATTERN_NON_PRINTABLE=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,PATTERN_NON_ASCII_LINE_BREAKS=/[\x85\u2028\u2029]/,PATTERN_FLOW_INDICATORS=/[,\[\]\{\}]/,PATTERN_TAG_HANDLE=/^(?:!|!!|![a-z\-]+!)$/i,PATTERN_TAG_URI=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function _class(e){return Object.prototype.toString.call(e)}function is_EOL(e){return e===10||e===13}function is_WHITE_SPACE(e){return e===9||e===32}function is_WS_OR_EOL(e){return e===9||e===32||e===10||e===13}function is_FLOW_INDICATOR(e){return e===44||e===91||e===93||e===123||e===125}function fromHexCode(e){var n;return 48<=e&&e<=57?e-48:(n=e|32,97<=n&&n<=102?n-97+10:-1)}function escapedHexLen(e){return e===120?2:e===117?4:e===85?8:0}function fromDecimalCode(e){return 48<=e&&e<=57?e-48:-1}function simpleEscapeSequence(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"\x85":e===95?"\xA0":e===76?"\u2028":e===80?"\u2029":""}function charFromCodepoint(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}for(var simpleEscapeCheck=new Array(256),simpleEscapeMap=new Array(256),i=0;i<256;i++)simpleEscapeCheck[i]=simpleEscapeSequence(i)?1:0,simpleEscapeMap[i]=simpleEscapeSequence(i);function State$1(e,n){this.input=e,this.filename=n.filename||null,this.schema=n.schema||_default,this.onWarning=n.onWarning||null,this.legacy=n.legacy||!1,this.json=n.json||!1,this.listener=n.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function generateError(e,n){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=snippet(r),new exception(n,r)}function throwError(e,n){throw generateError(e,n)}function throwWarning(e,n){e.onWarning&&e.onWarning.call(null,generateError(e,n))}var directiveHandlers={YAML:function(n,r,o){var l,f,u;n.version!==null&&throwError(n,"duplication of %YAML directive"),o.length!==1&&throwError(n,"YAML directive accepts exactly one argument"),l=/^([0-9]+)\.([0-9]+)$/.exec(o[0]),l===null&&throwError(n,"ill-formed argument of the YAML directive"),f=parseInt(l[1],10),u=parseInt(l[2],10),f!==1&&throwError(n,"unacceptable YAML version of the document"),n.version=o[0],n.checkLineBreaks=u<2,u!==1&&u!==2&&throwWarning(n,"unsupported YAML version of the document")},TAG:function(n,r,o){var l,f;o.length!==2&&throwError(n,"TAG directive accepts exactly two arguments"),l=o[0],f=o[1],PATTERN_TAG_HANDLE.test(l)||throwError(n,"ill-formed tag handle (first argument) of the TAG directive"),_hasOwnProperty$1.call(n.tagMap,l)&&throwError(n,'there is a previously declared suffix for "'+l+'" tag handle'),PATTERN_TAG_URI.test(f)||throwError(n,"ill-formed tag prefix (second argument) of the TAG directive");try{f=decodeURIComponent(f)}catch{throwError(n,"tag prefix is malformed: "+f)}n.tagMap[l]=f}};function captureSegment(e,n,r,o){var l,f,u,c;if(n<r){if(c=e.input.slice(n,r),o)for(l=0,f=c.length;l<f;l+=1)u=c.charCodeAt(l),u===9||32<=u&&u<=1114111||throwError(e,"expected valid JSON character");else PATTERN_NON_PRINTABLE.test(c)&&throwError(e,"the stream contains non-printable characters");e.result+=c}}function mergeMappings(e,n,r,o){var l,f,u,c;for(common.isObject(r)||throwError(e,"cannot merge mappings; the provided source object is unacceptable"),l=Object.keys(r),u=0,c=l.length;u<c;u+=1)f=l[u],_hasOwnProperty$1.call(n,f)||(n[f]=r[f],o[f]=!0)}function storeMappingPair(e,n,r,o,l,f,u,c,a){var p,h;if(Array.isArray(l))for(l=Array.prototype.slice.call(l),p=0,h=l.length;p<h;p+=1)Array.isArray(l[p])&&throwError(e,"nested arrays are not supported inside keys"),typeof l=="object"&&_class(l[p])==="[object Object]"&&(l[p]="[object Object]");if(typeof l=="object"&&_class(l)==="[object Object]"&&(l="[object Object]"),l=String(l),n===null&&(n={}),o==="tag:yaml.org,2002:merge")if(Array.isArray(f))for(p=0,h=f.length;p<h;p+=1)mergeMappings(e,n,f[p],r);else mergeMappings(e,n,f,r);else!e.json&&!_hasOwnProperty$1.call(r,l)&&_hasOwnProperty$1.call(n,l)&&(e.line=u||e.line,e.lineStart=c||e.lineStart,e.position=a||e.position,throwError(e,"duplicated mapping key")),l==="__proto__"?Object.defineProperty(n,l,{configurable:!0,enumerable:!0,writable:!0,value:f}):n[l]=f,delete r[l];return n}function readLineBreak(e){var n;n=e.input.charCodeAt(e.position),n===10?e.position++:n===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):throwError(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function skipSeparationSpace(e,n,r){for(var o=0,l=e.input.charCodeAt(e.position);l!==0;){for(;is_WHITE_SPACE(l);)l===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),l=e.input.charCodeAt(++e.position);if(n&&l===35)do l=e.input.charCodeAt(++e.position);while(l!==10&&l!==13&&l!==0);if(is_EOL(l))for(readLineBreak(e),l=e.input.charCodeAt(e.position),o++,e.lineIndent=0;l===32;)e.lineIndent++,l=e.input.charCodeAt(++e.position);else break}return r!==-1&&o!==0&&e.lineIndent<r&&throwWarning(e,"deficient indentation"),o}function testDocumentSeparator(e){var n=e.position,r;return r=e.input.charCodeAt(n),!!((r===45||r===46)&&r===e.input.charCodeAt(n+1)&&r===e.input.charCodeAt(n+2)&&(n+=3,r=e.input.charCodeAt(n),r===0||is_WS_OR_EOL(r)))}function writeFoldedLines(e,n){n===1?e.result+=" ":n>1&&(e.result+=common.repeat(`
`,n-1))}function readPlainScalar(e,n,r){var o,l,f,u,c,a,p,h,t=e.kind,d=e.result,s;if(s=e.input.charCodeAt(e.position),is_WS_OR_EOL(s)||is_FLOW_INDICATOR(s)||s===35||s===38||s===42||s===33||s===124||s===62||s===39||s===34||s===37||s===64||s===96||(s===63||s===45)&&(l=e.input.charCodeAt(e.position+1),is_WS_OR_EOL(l)||r&&is_FLOW_INDICATOR(l)))return!1;for(e.kind="scalar",e.result="",f=u=e.position,c=!1;s!==0;){if(s===58){if(l=e.input.charCodeAt(e.position+1),is_WS_OR_EOL(l)||r&&is_FLOW_INDICATOR(l))break}else if(s===35){if(o=e.input.charCodeAt(e.position-1),is_WS_OR_EOL(o))break}else{if(e.position===e.lineStart&&testDocumentSeparator(e)||r&&is_FLOW_INDICATOR(s))break;if(is_EOL(s))if(a=e.line,p=e.lineStart,h=e.lineIndent,skipSeparationSpace(e,!1,-1),e.lineIndent>=n){c=!0,s=e.input.charCodeAt(e.position);continue}else{e.position=u,e.line=a,e.lineStart=p,e.lineIndent=h;break}}c&&(captureSegment(e,f,u,!1),writeFoldedLines(e,e.line-a),f=u=e.position,c=!1),is_WHITE_SPACE(s)||(u=e.position+1),s=e.input.charCodeAt(++e.position)}return captureSegment(e,f,u,!1),e.result?!0:(e.kind=t,e.result=d,!1)}function readSingleQuotedScalar(e,n){var r,o,l;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,o=l=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(captureSegment(e,o,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)o=e.position,e.position++,l=e.position;else return!0;else is_EOL(r)?(captureSegment(e,o,l,!0),writeFoldedLines(e,skipSeparationSpace(e,!1,n)),o=l=e.position):e.position===e.lineStart&&testDocumentSeparator(e)?throwError(e,"unexpected end of the document within a single quoted scalar"):(e.position++,l=e.position);throwError(e,"unexpected end of the stream within a single quoted scalar")}function readDoubleQuotedScalar(e,n){var r,o,l,f,u,c;if(c=e.input.charCodeAt(e.position),c!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=o=e.position;(c=e.input.charCodeAt(e.position))!==0;){if(c===34)return captureSegment(e,r,e.position,!0),e.position++,!0;if(c===92){if(captureSegment(e,r,e.position,!0),c=e.input.charCodeAt(++e.position),is_EOL(c))skipSeparationSpace(e,!1,n);else if(c<256&&simpleEscapeCheck[c])e.result+=simpleEscapeMap[c],e.position++;else if((u=escapedHexLen(c))>0){for(l=u,f=0;l>0;l--)c=e.input.charCodeAt(++e.position),(u=fromHexCode(c))>=0?f=(f<<4)+u:throwError(e,"expected hexadecimal character");e.result+=charFromCodepoint(f),e.position++}else throwError(e,"unknown escape sequence");r=o=e.position}else is_EOL(c)?(captureSegment(e,r,o,!0),writeFoldedLines(e,skipSeparationSpace(e,!1,n)),r=o=e.position):e.position===e.lineStart&&testDocumentSeparator(e)?throwError(e,"unexpected end of the document within a double quoted scalar"):(e.position++,o=e.position)}throwError(e,"unexpected end of the stream within a double quoted scalar")}function readFlowCollection(e,n){var r=!0,o,l,f,u=e.tag,c,a=e.anchor,p,h,t,d,s,x=Object.create(null),g,A,v,m;if(m=e.input.charCodeAt(e.position),m===91)h=93,s=!1,c=[];else if(m===123)h=125,s=!0,c={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=c),m=e.input.charCodeAt(++e.position);m!==0;){if(skipSeparationSpace(e,!0,n),m=e.input.charCodeAt(e.position),m===h)return e.position++,e.tag=u,e.anchor=a,e.kind=s?"mapping":"sequence",e.result=c,!0;r?m===44&&throwError(e,"expected the node content, but found ','"):throwError(e,"missed comma between flow collection entries"),A=g=v=null,t=d=!1,m===63&&(p=e.input.charCodeAt(e.position+1),is_WS_OR_EOL(p)&&(t=d=!0,e.position++,skipSeparationSpace(e,!0,n))),o=e.line,l=e.lineStart,f=e.position,composeNode(e,n,CONTEXT_FLOW_IN,!1,!0),A=e.tag,g=e.result,skipSeparationSpace(e,!0,n),m=e.input.charCodeAt(e.position),(d||e.line===o)&&m===58&&(t=!0,m=e.input.charCodeAt(++e.position),skipSeparationSpace(e,!0,n),composeNode(e,n,CONTEXT_FLOW_IN,!1,!0),v=e.result),s?storeMappingPair(e,c,x,A,g,v,o,l,f):t?c.push(storeMappingPair(e,null,x,A,g,v,o,l,f)):c.push(g),skipSeparationSpace(e,!0,n),m=e.input.charCodeAt(e.position),m===44?(r=!0,m=e.input.charCodeAt(++e.position)):r=!1}throwError(e,"unexpected end of the stream within a flow collection")}function readBlockScalar(e,n){var r,o,l=CHOMPING_CLIP,f=!1,u=!1,c=n,a=0,p=!1,h,t;if(t=e.input.charCodeAt(e.position),t===124)o=!1;else if(t===62)o=!0;else return!1;for(e.kind="scalar",e.result="";t!==0;)if(t=e.input.charCodeAt(++e.position),t===43||t===45)CHOMPING_CLIP===l?l=t===43?CHOMPING_KEEP:CHOMPING_STRIP:throwError(e,"repeat of a chomping mode identifier");else if((h=fromDecimalCode(t))>=0)h===0?throwError(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):u?throwError(e,"repeat of an indentation width identifier"):(c=n+h-1,u=!0);else break;if(is_WHITE_SPACE(t)){do t=e.input.charCodeAt(++e.position);while(is_WHITE_SPACE(t));if(t===35)do t=e.input.charCodeAt(++e.position);while(!is_EOL(t)&&t!==0)}for(;t!==0;){for(readLineBreak(e),e.lineIndent=0,t=e.input.charCodeAt(e.position);(!u||e.lineIndent<c)&&t===32;)e.lineIndent++,t=e.input.charCodeAt(++e.position);if(!u&&e.lineIndent>c&&(c=e.lineIndent),is_EOL(t)){a++;continue}if(e.lineIndent<c){l===CHOMPING_KEEP?e.result+=common.repeat(`
`,f?1+a:a):l===CHOMPING_CLIP&&f&&(e.result+=`
`);break}for(o?is_WHITE_SPACE(t)?(p=!0,e.result+=common.repeat(`
`,f?1+a:a)):p?(p=!1,e.result+=common.repeat(`
`,a+1)):a===0?f&&(e.result+=" "):e.result+=common.repeat(`
`,a):e.result+=common.repeat(`
`,f?1+a:a),f=!0,u=!0,a=0,r=e.position;!is_EOL(t)&&t!==0;)t=e.input.charCodeAt(++e.position);captureSegment(e,r,e.position,!1)}return!0}function readBlockSequence(e,n){var r,o=e.tag,l=e.anchor,f=[],u,c=!1,a;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=f),a=e.input.charCodeAt(e.position);a!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,throwError(e,"tab characters must not be used in indentation")),!(a!==45||(u=e.input.charCodeAt(e.position+1),!is_WS_OR_EOL(u))));){if(c=!0,e.position++,skipSeparationSpace(e,!0,-1)&&e.lineIndent<=n){f.push(null),a=e.input.charCodeAt(e.position);continue}if(r=e.line,composeNode(e,n,CONTEXT_BLOCK_IN,!1,!0),f.push(e.result),skipSeparationSpace(e,!0,-1),a=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>n)&&a!==0)throwError(e,"bad indentation of a sequence entry");else if(e.lineIndent<n)break}return c?(e.tag=o,e.anchor=l,e.kind="sequence",e.result=f,!0):!1}function readBlockMapping(e,n,r){var o,l,f,u,c,a,p=e.tag,h=e.anchor,t={},d=Object.create(null),s=null,x=null,g=null,A=!1,v=!1,m;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=t),m=e.input.charCodeAt(e.position);m!==0;){if(!A&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,throwError(e,"tab characters must not be used in indentation")),o=e.input.charCodeAt(e.position+1),f=e.line,(m===63||m===58)&&is_WS_OR_EOL(o))m===63?(A&&(storeMappingPair(e,t,d,s,x,null,u,c,a),s=x=g=null),v=!0,A=!0,l=!0):A?(A=!1,l=!0):throwError(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,m=o;else{if(u=e.line,c=e.lineStart,a=e.position,!composeNode(e,r,CONTEXT_FLOW_OUT,!1,!0))break;if(e.line===f){for(m=e.input.charCodeAt(e.position);is_WHITE_SPACE(m);)m=e.input.charCodeAt(++e.position);if(m===58)m=e.input.charCodeAt(++e.position),is_WS_OR_EOL(m)||throwError(e,"a whitespace character is expected after the key-value separator within a block mapping"),A&&(storeMappingPair(e,t,d,s,x,null,u,c,a),s=x=g=null),v=!0,A=!1,l=!1,s=e.tag,x=e.result;else if(v)throwError(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=p,e.anchor=h,!0}else if(v)throwError(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=p,e.anchor=h,!0}if((e.line===f||e.lineIndent>n)&&(A&&(u=e.line,c=e.lineStart,a=e.position),composeNode(e,n,CONTEXT_BLOCK_OUT,!0,l)&&(A?x=e.result:g=e.result),A||(storeMappingPair(e,t,d,s,x,g,u,c,a),s=x=g=null),skipSeparationSpace(e,!0,-1),m=e.input.charCodeAt(e.position)),(e.line===f||e.lineIndent>n)&&m!==0)throwError(e,"bad indentation of a mapping entry");else if(e.lineIndent<n)break}return A&&storeMappingPair(e,t,d,s,x,null,u,c,a),v&&(e.tag=p,e.anchor=h,e.kind="mapping",e.result=t),v}function readTagProperty(e){var n,r=!1,o=!1,l,f,u;if(u=e.input.charCodeAt(e.position),u!==33)return!1;if(e.tag!==null&&throwError(e,"duplication of a tag property"),u=e.input.charCodeAt(++e.position),u===60?(r=!0,u=e.input.charCodeAt(++e.position)):u===33?(o=!0,l="!!",u=e.input.charCodeAt(++e.position)):l="!",n=e.position,r){do u=e.input.charCodeAt(++e.position);while(u!==0&&u!==62);e.position<e.length?(f=e.input.slice(n,e.position),u=e.input.charCodeAt(++e.position)):throwError(e,"unexpected end of the stream within a verbatim tag")}else{for(;u!==0&&!is_WS_OR_EOL(u);)u===33&&(o?throwError(e,"tag suffix cannot contain exclamation marks"):(l=e.input.slice(n-1,e.position+1),PATTERN_TAG_HANDLE.test(l)||throwError(e,"named tag handle cannot contain such characters"),o=!0,n=e.position+1)),u=e.input.charCodeAt(++e.position);f=e.input.slice(n,e.position),PATTERN_FLOW_INDICATORS.test(f)&&throwError(e,"tag suffix cannot contain flow indicator characters")}f&&!PATTERN_TAG_URI.test(f)&&throwError(e,"tag name cannot contain such characters: "+f);try{f=decodeURIComponent(f)}catch{throwError(e,"tag name is malformed: "+f)}return r?e.tag=f:_hasOwnProperty$1.call(e.tagMap,l)?e.tag=e.tagMap[l]+f:l==="!"?e.tag="!"+f:l==="!!"?e.tag="tag:yaml.org,2002:"+f:throwError(e,'undeclared tag handle "'+l+'"'),!0}function readAnchorProperty(e){var n,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&throwError(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),n=e.position;r!==0&&!is_WS_OR_EOL(r)&&!is_FLOW_INDICATOR(r);)r=e.input.charCodeAt(++e.position);return e.position===n&&throwError(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(n,e.position),!0}function readAlias(e){var n,r,o;if(o=e.input.charCodeAt(e.position),o!==42)return!1;for(o=e.input.charCodeAt(++e.position),n=e.position;o!==0&&!is_WS_OR_EOL(o)&&!is_FLOW_INDICATOR(o);)o=e.input.charCodeAt(++e.position);return e.position===n&&throwError(e,"name of an alias node must contain at least one character"),r=e.input.slice(n,e.position),_hasOwnProperty$1.call(e.anchorMap,r)||throwError(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],skipSeparationSpace(e,!0,-1),!0}function composeNode(e,n,r,o,l){var f,u,c,a=1,p=!1,h=!1,t,d,s,x,g,A;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,f=u=c=CONTEXT_BLOCK_OUT===r||CONTEXT_BLOCK_IN===r,o&&skipSeparationSpace(e,!0,-1)&&(p=!0,e.lineIndent>n?a=1:e.lineIndent===n?a=0:e.lineIndent<n&&(a=-1)),a===1)for(;readTagProperty(e)||readAnchorProperty(e);)skipSeparationSpace(e,!0,-1)?(p=!0,c=f,e.lineIndent>n?a=1:e.lineIndent===n?a=0:e.lineIndent<n&&(a=-1)):c=!1;if(c&&(c=p||l),(a===1||CONTEXT_BLOCK_OUT===r)&&(CONTEXT_FLOW_IN===r||CONTEXT_FLOW_OUT===r?g=n:g=n+1,A=e.position-e.lineStart,a===1?c&&(readBlockSequence(e,A)||readBlockMapping(e,A,g))||readFlowCollection(e,g)?h=!0:(u&&readBlockScalar(e,g)||readSingleQuotedScalar(e,g)||readDoubleQuotedScalar(e,g)?h=!0:readAlias(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&throwError(e,"alias node should not have any properties")):readPlainScalar(e,g,CONTEXT_FLOW_IN===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):a===0&&(h=c&&readBlockSequence(e,A))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&throwError(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),t=0,d=e.implicitTypes.length;t<d;t+=1)if(x=e.implicitTypes[t],x.resolve(e.result)){e.result=x.construct(e.result),e.tag=x.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(_hasOwnProperty$1.call(e.typeMap[e.kind||"fallback"],e.tag))x=e.typeMap[e.kind||"fallback"][e.tag];else for(x=null,s=e.typeMap.multi[e.kind||"fallback"],t=0,d=s.length;t<d;t+=1)if(e.tag.slice(0,s[t].tag.length)===s[t].tag){x=s[t];break}x||throwError(e,"unknown tag !<"+e.tag+">"),e.result!==null&&x.kind!==e.kind&&throwError(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+x.kind+'", not "'+e.kind+'"'),x.resolve(e.result,e.tag)?(e.result=x.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):throwError(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}function readDocument(e){var n=e.position,r,o,l,f=!1,u;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(u=e.input.charCodeAt(e.position))!==0&&(skipSeparationSpace(e,!0,-1),u=e.input.charCodeAt(e.position),!(e.lineIndent>0||u!==37));){for(f=!0,u=e.input.charCodeAt(++e.position),r=e.position;u!==0&&!is_WS_OR_EOL(u);)u=e.input.charCodeAt(++e.position);for(o=e.input.slice(r,e.position),l=[],o.length<1&&throwError(e,"directive name must not be less than one character in length");u!==0;){for(;is_WHITE_SPACE(u);)u=e.input.charCodeAt(++e.position);if(u===35){do u=e.input.charCodeAt(++e.position);while(u!==0&&!is_EOL(u));break}if(is_EOL(u))break;for(r=e.position;u!==0&&!is_WS_OR_EOL(u);)u=e.input.charCodeAt(++e.position);l.push(e.input.slice(r,e.position))}u!==0&&readLineBreak(e),_hasOwnProperty$1.call(directiveHandlers,o)?directiveHandlers[o](e,o,l):throwWarning(e,'unknown document directive "'+o+'"')}if(skipSeparationSpace(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,skipSeparationSpace(e,!0,-1)):f&&throwError(e,"directives end mark is expected"),composeNode(e,e.lineIndent-1,CONTEXT_BLOCK_OUT,!1,!0),skipSeparationSpace(e,!0,-1),e.checkLineBreaks&&PATTERN_NON_ASCII_LINE_BREAKS.test(e.input.slice(n,e.position))&&throwWarning(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&testDocumentSeparator(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,skipSeparationSpace(e,!0,-1));return}if(e.position<e.length-1)throwError(e,"end of the stream or a document separator is expected");else return}function loadDocuments(e,n){e=String(e),n=n||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new State$1(e,n),o=e.indexOf("\0");for(o!==-1&&(r.position=o,throwError(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)readDocument(r);return r.documents}function loadAll$1(e,n,r){n!==null&&typeof n=="object"&&typeof r>"u"&&(r=n,n=null);var o=loadDocuments(e,r);if(typeof n!="function")return o;for(var l=0,f=o.length;l<f;l+=1)n(o[l])}function load$1(e,n){var r=loadDocuments(e,n);if(r.length!==0){if(r.length===1)return r[0];throw new exception("expected a single document in the stream, but found more")}}var loadAll_1=loadAll$1,load_1=load$1,loader={loadAll:loadAll_1,load:load_1},_toString=Object.prototype.toString,_hasOwnProperty=Object.prototype.hasOwnProperty,CHAR_BOM=65279,CHAR_TAB=9,CHAR_LINE_FEED=10,CHAR_CARRIAGE_RETURN=13,CHAR_SPACE=32,CHAR_EXCLAMATION=33,CHAR_DOUBLE_QUOTE=34,CHAR_SHARP=35,CHAR_PERCENT=37,CHAR_AMPERSAND=38,CHAR_SINGLE_QUOTE=39,CHAR_ASTERISK=42,CHAR_COMMA=44,CHAR_MINUS=45,CHAR_COLON=58,CHAR_EQUALS=61,CHAR_GREATER_THAN=62,CHAR_QUESTION=63,CHAR_COMMERCIAL_AT=64,CHAR_LEFT_SQUARE_BRACKET=91,CHAR_RIGHT_SQUARE_BRACKET=93,CHAR_GRAVE_ACCENT=96,CHAR_LEFT_CURLY_BRACKET=123,CHAR_VERTICAL_LINE=124,CHAR_RIGHT_CURLY_BRACKET=125,ESCAPE_SEQUENCES={};ESCAPE_SEQUENCES[0]="\\0",ESCAPE_SEQUENCES[7]="\\a",ESCAPE_SEQUENCES[8]="\\b",ESCAPE_SEQUENCES[9]="\\t",ESCAPE_SEQUENCES[10]="\\n",ESCAPE_SEQUENCES[11]="\\v",ESCAPE_SEQUENCES[12]="\\f",ESCAPE_SEQUENCES[13]="\\r",ESCAPE_SEQUENCES[27]="\\e",ESCAPE_SEQUENCES[34]='\\"',ESCAPE_SEQUENCES[92]="\\\\",ESCAPE_SEQUENCES[133]="\\N",ESCAPE_SEQUENCES[160]="\\_",ESCAPE_SEQUENCES[8232]="\\L",ESCAPE_SEQUENCES[8233]="\\P";var DEPRECATED_BOOLEANS_SYNTAX=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],DEPRECATED_BASE60_SYNTAX=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function compileStyleMap(e,n){var r,o,l,f,u,c,a;if(n===null)return{};for(r={},o=Object.keys(n),l=0,f=o.length;l<f;l+=1)u=o[l],c=String(n[u]),u.slice(0,2)==="!!"&&(u="tag:yaml.org,2002:"+u.slice(2)),a=e.compiledTypeMap.fallback[u],a&&_hasOwnProperty.call(a.styleAliases,c)&&(c=a.styleAliases[c]),r[u]=c;return r}function encodeHex(e){var n,r,o;if(n=e.toString(16).toUpperCase(),e<=255)r="x",o=2;else if(e<=65535)r="u",o=4;else if(e<=4294967295)r="U",o=8;else throw new exception("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+common.repeat("0",o-n.length)+n}var QUOTING_TYPE_SINGLE=1,QUOTING_TYPE_DOUBLE=2;function State(e){this.schema=e.schema||_default,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=common.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=compileStyleMap(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?QUOTING_TYPE_DOUBLE:QUOTING_TYPE_SINGLE,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function indentString(e,n){for(var r=common.repeat(" ",n),o=0,l=-1,f="",u,c=e.length;o<c;)l=e.indexOf(`
`,o),l===-1?(u=e.slice(o),o=c):(u=e.slice(o,l+1),o=l+1),u.length&&u!==`
`&&(f+=r),f+=u;return f}function generateNextLine(e,n){return`
`+common.repeat(" ",e.indent*n)}function testImplicitResolving(e,n){var r,o,l;for(r=0,o=e.implicitTypes.length;r<o;r+=1)if(l=e.implicitTypes[r],l.resolve(n))return!0;return!1}function isWhitespace(e){return e===CHAR_SPACE||e===CHAR_TAB}function isPrintable(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==CHAR_BOM||65536<=e&&e<=1114111}function isNsCharOrWhitespace(e){return isPrintable(e)&&e!==CHAR_BOM&&e!==CHAR_CARRIAGE_RETURN&&e!==CHAR_LINE_FEED}function isPlainSafe(e,n,r){var o=isNsCharOrWhitespace(e),l=o&&!isWhitespace(e);return(r?o:o&&e!==CHAR_COMMA&&e!==CHAR_LEFT_SQUARE_BRACKET&&e!==CHAR_RIGHT_SQUARE_BRACKET&&e!==CHAR_LEFT_CURLY_BRACKET&&e!==CHAR_RIGHT_CURLY_BRACKET)&&e!==CHAR_SHARP&&!(n===CHAR_COLON&&!l)||isNsCharOrWhitespace(n)&&!isWhitespace(n)&&e===CHAR_SHARP||n===CHAR_COLON&&l}function isPlainSafeFirst(e){return isPrintable(e)&&e!==CHAR_BOM&&!isWhitespace(e)&&e!==CHAR_MINUS&&e!==CHAR_QUESTION&&e!==CHAR_COLON&&e!==CHAR_COMMA&&e!==CHAR_LEFT_SQUARE_BRACKET&&e!==CHAR_RIGHT_SQUARE_BRACKET&&e!==CHAR_LEFT_CURLY_BRACKET&&e!==CHAR_RIGHT_CURLY_BRACKET&&e!==CHAR_SHARP&&e!==CHAR_AMPERSAND&&e!==CHAR_ASTERISK&&e!==CHAR_EXCLAMATION&&e!==CHAR_VERTICAL_LINE&&e!==CHAR_EQUALS&&e!==CHAR_GREATER_THAN&&e!==CHAR_SINGLE_QUOTE&&e!==CHAR_DOUBLE_QUOTE&&e!==CHAR_PERCENT&&e!==CHAR_COMMERCIAL_AT&&e!==CHAR_GRAVE_ACCENT}function isPlainSafeLast(e){return!isWhitespace(e)&&e!==CHAR_COLON}function codePointAt(e,n){var r=e.charCodeAt(n),o;return r>=55296&&r<=56319&&n+1<e.length&&(o=e.charCodeAt(n+1),o>=56320&&o<=57343)?(r-55296)*1024+o-56320+65536:r}function needIndentIndicator(e){var n=/^\n* /;return n.test(e)}var STYLE_PLAIN=1,STYLE_SINGLE=2,STYLE_LITERAL=3,STYLE_FOLDED=4,STYLE_DOUBLE=5;function chooseScalarStyle(e,n,r,o,l,f,u,c){var a,p=0,h=null,t=!1,d=!1,s=o!==-1,x=-1,g=isPlainSafeFirst(codePointAt(e,0))&&isPlainSafeLast(codePointAt(e,e.length-1));if(n||u)for(a=0;a<e.length;p>=65536?a+=2:a++){if(p=codePointAt(e,a),!isPrintable(p))return STYLE_DOUBLE;g=g&&isPlainSafe(p,h,c),h=p}else{for(a=0;a<e.length;p>=65536?a+=2:a++){if(p=codePointAt(e,a),p===CHAR_LINE_FEED)t=!0,s&&(d=d||a-x-1>o&&e[x+1]!==" ",x=a);else if(!isPrintable(p))return STYLE_DOUBLE;g=g&&isPlainSafe(p,h,c),h=p}d=d||s&&a-x-1>o&&e[x+1]!==" "}return!t&&!d?g&&!u&&!l(e)?STYLE_PLAIN:f===QUOTING_TYPE_DOUBLE?STYLE_DOUBLE:STYLE_SINGLE:r>9&&needIndentIndicator(e)?STYLE_DOUBLE:u?f===QUOTING_TYPE_DOUBLE?STYLE_DOUBLE:STYLE_SINGLE:d?STYLE_FOLDED:STYLE_LITERAL}function writeScalar(e,n,r,o,l){e.dump=function(){if(n.length===0)return e.quotingType===QUOTING_TYPE_DOUBLE?'""':"''";if(!e.noCompatMode&&(DEPRECATED_BOOLEANS_SYNTAX.indexOf(n)!==-1||DEPRECATED_BASE60_SYNTAX.test(n)))return e.quotingType===QUOTING_TYPE_DOUBLE?'"'+n+'"':"'"+n+"'";var f=e.indent*Math.max(1,r),u=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-f),c=o||e.flowLevel>-1&&r>=e.flowLevel;function a(p){return testImplicitResolving(e,p)}switch(chooseScalarStyle(n,c,e.indent,u,a,e.quotingType,e.forceQuotes&&!o,l)){case STYLE_PLAIN:return n;case STYLE_SINGLE:return"'"+n.replace(/'/g,"''")+"'";case STYLE_LITERAL:return"|"+blockHeader(n,e.indent)+dropEndingNewline(indentString(n,f));case STYLE_FOLDED:return">"+blockHeader(n,e.indent)+dropEndingNewline(indentString(foldString(n,u),f));case STYLE_DOUBLE:return'"'+escapeString(n)+'"';default:throw new exception("impossible error: invalid scalar style")}}()}function blockHeader(e,n){var r=needIndentIndicator(e)?String(n):"",o=e[e.length-1]===`
`,l=o&&(e[e.length-2]===`
`||e===`
`),f=l?"+":o?"":"-";return r+f+`
`}function dropEndingNewline(e){return e[e.length-1]===`
`?e.slice(0,-1):e}function foldString(e,n){for(var r=/(\n+)([^\n]*)/g,o=function(){var p=e.indexOf(`
`);return p=p!==-1?p:e.length,r.lastIndex=p,foldLine(e.slice(0,p),n)}(),l=e[0]===`
`||e[0]===" ",f,u;u=r.exec(e);){var c=u[1],a=u[2];f=a[0]===" ",o+=c+(!l&&!f&&a!==""?`
`:"")+foldLine(a,n),l=f}return o}function foldLine(e,n){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,o,l=0,f,u=0,c=0,a="";o=r.exec(e);)c=o.index,c-l>n&&(f=u>l?u:c,a+=`
`+e.slice(l,f),l=f+1),u=c;return a+=`
`,e.length-l>n&&u>l?a+=e.slice(l,u)+`
`+e.slice(u+1):a+=e.slice(l),a.slice(1)}function escapeString(e){for(var n="",r=0,o,l=0;l<e.length;r>=65536?l+=2:l++)r=codePointAt(e,l),o=ESCAPE_SEQUENCES[r],!o&&isPrintable(r)?(n+=e[l],r>=65536&&(n+=e[l+1])):n+=o||encodeHex(r);return n}function writeFlowSequence(e,n,r){var o="",l=e.tag,f,u,c;for(f=0,u=r.length;f<u;f+=1)c=r[f],e.replacer&&(c=e.replacer.call(r,String(f),c)),(writeNode(e,n,c,!1,!1)||typeof c>"u"&&writeNode(e,n,null,!1,!1))&&(o!==""&&(o+=","+(e.condenseFlow?"":" ")),o+=e.dump);e.tag=l,e.dump="["+o+"]"}function writeBlockSequence(e,n,r,o){var l="",f=e.tag,u,c,a;for(u=0,c=r.length;u<c;u+=1)a=r[u],e.replacer&&(a=e.replacer.call(r,String(u),a)),(writeNode(e,n+1,a,!0,!0,!1,!0)||typeof a>"u"&&writeNode(e,n+1,null,!0,!0,!1,!0))&&((!o||l!=="")&&(l+=generateNextLine(e,n)),e.dump&&CHAR_LINE_FEED===e.dump.charCodeAt(0)?l+="-":l+="- ",l+=e.dump);e.tag=f,e.dump=l||"[]"}function writeFlowMapping(e,n,r){var o="",l=e.tag,f=Object.keys(r),u,c,a,p,h;for(u=0,c=f.length;u<c;u+=1)h="",o!==""&&(h+=", "),e.condenseFlow&&(h+='"'),a=f[u],p=r[a],e.replacer&&(p=e.replacer.call(r,a,p)),writeNode(e,n,a,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),writeNode(e,n,p,!1,!1)&&(h+=e.dump,o+=h));e.tag=l,e.dump="{"+o+"}"}function writeBlockMapping(e,n,r,o){var l="",f=e.tag,u=Object.keys(r),c,a,p,h,t,d;if(e.sortKeys===!0)u.sort();else if(typeof e.sortKeys=="function")u.sort(e.sortKeys);else if(e.sortKeys)throw new exception("sortKeys must be a boolean or a function");for(c=0,a=u.length;c<a;c+=1)d="",(!o||l!=="")&&(d+=generateNextLine(e,n)),p=u[c],h=r[p],e.replacer&&(h=e.replacer.call(r,p,h)),writeNode(e,n+1,p,!0,!0,!0)&&(t=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,t&&(e.dump&&CHAR_LINE_FEED===e.dump.charCodeAt(0)?d+="?":d+="? "),d+=e.dump,t&&(d+=generateNextLine(e,n)),writeNode(e,n+1,h,!0,t)&&(e.dump&&CHAR_LINE_FEED===e.dump.charCodeAt(0)?d+=":":d+=": ",d+=e.dump,l+=d));e.tag=f,e.dump=l||"{}"}function detectType(e,n,r){var o,l,f,u,c,a;for(l=r?e.explicitTypes:e.implicitTypes,f=0,u=l.length;f<u;f+=1)if(c=l[f],(c.instanceOf||c.predicate)&&(!c.instanceOf||typeof n=="object"&&n instanceof c.instanceOf)&&(!c.predicate||c.predicate(n))){if(r?c.multi&&c.representName?e.tag=c.representName(n):e.tag=c.tag:e.tag="?",c.represent){if(a=e.styleMap[c.tag]||c.defaultStyle,_toString.call(c.represent)==="[object Function]")o=c.represent(n,a);else if(_hasOwnProperty.call(c.represent,a))o=c.represent[a](n,a);else throw new exception("!<"+c.tag+'> tag resolver accepts not "'+a+'" style');e.dump=o}return!0}return!1}function writeNode(e,n,r,o,l,f,u){e.tag=null,e.dump=r,detectType(e,r,!1)||detectType(e,r,!0);var c=_toString.call(e.dump),a=o,p;o&&(o=e.flowLevel<0||e.flowLevel>n);var h=c==="[object Object]"||c==="[object Array]",t,d;if(h&&(t=e.duplicates.indexOf(r),d=t!==-1),(e.tag!==null&&e.tag!=="?"||d||e.indent!==2&&n>0)&&(l=!1),d&&e.usedDuplicates[t])e.dump="*ref_"+t;else{if(h&&d&&!e.usedDuplicates[t]&&(e.usedDuplicates[t]=!0),c==="[object Object]")o&&Object.keys(e.dump).length!==0?(writeBlockMapping(e,n,e.dump,l),d&&(e.dump="&ref_"+t+e.dump)):(writeFlowMapping(e,n,e.dump),d&&(e.dump="&ref_"+t+" "+e.dump));else if(c==="[object Array]")o&&e.dump.length!==0?(e.noArrayIndent&&!u&&n>0?writeBlockSequence(e,n-1,e.dump,l):writeBlockSequence(e,n,e.dump,l),d&&(e.dump="&ref_"+t+e.dump)):(writeFlowSequence(e,n,e.dump),d&&(e.dump="&ref_"+t+" "+e.dump));else if(c==="[object String]")e.tag!=="?"&&writeScalar(e,e.dump,n,f,a);else{if(c==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new exception("unacceptable kind of an object to dump "+c)}e.tag!==null&&e.tag!=="?"&&(p=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?p="!"+p:p.slice(0,18)==="tag:yaml.org,2002:"?p="!!"+p.slice(18):p="!<"+p+">",e.dump=p+" "+e.dump)}return!0}function getDuplicateReferences(e,n){var r=[],o=[],l,f;for(inspectNode(e,r,o),l=0,f=o.length;l<f;l+=1)n.duplicates.push(r[o[l]]);n.usedDuplicates=new Array(f)}function inspectNode(e,n,r){var o,l,f;if(e!==null&&typeof e=="object")if(l=n.indexOf(e),l!==-1)r.indexOf(l)===-1&&r.push(l);else if(n.push(e),Array.isArray(e))for(l=0,f=e.length;l<f;l+=1)inspectNode(e[l],n,r);else for(o=Object.keys(e),l=0,f=o.length;l<f;l+=1)inspectNode(e[o[l]],n,r)}function dump$1(e,n){n=n||{};var r=new State(n);r.noRefs||getDuplicateReferences(e,r);var o=e;return r.replacer&&(o=r.replacer.call({"":o},"",o)),writeNode(r,0,o,!0,!0)?r.dump+`
`:""}var dump_1=dump$1,dumper={dump:dump_1},load=loader.load,dump=dumper.dump;function parseYAML(e,n){const r=load(e,n);return _format.storeFormat(e,r,n),r}function stringifyYAML(e,n){const r=_format.getFormat(e,{preserveIndentation:!1}),o=typeof r.indent=="string"?r.indent.length:r.indent,l=dump(e,{indent:o,...n});return r.whitespace.start+l.trim()+r.whitespace.end}exports.parseYAML=parseYAML,exports.stringifyYAML=stringifyYAML;
