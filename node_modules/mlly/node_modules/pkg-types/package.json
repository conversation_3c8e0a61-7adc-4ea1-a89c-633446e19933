{"name": "pkg-types", "version": "1.3.1", "description": "Node.js utilities and TypeScript definitions for `package.json` and `tsconfig.json`", "license": "MIT", "main": "./dist/index.cjs", "sideEffects": false, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "types": "./dist/index.d.ts", "repository": "unjs/pkg-types", "files": ["dist"], "scripts": {"build": "unbuild", "prepack": "pnpm build", "dev": "vitest --typecheck", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint": "eslint && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "test": "vitest run --typecheck --coverage"}, "dependencies": {"confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1"}, "devDependencies": {"@types/node": "^22.10.6", "@vitest/coverage-v8": "^2.1.8", "automd": "^0.3.12", "changelogen": "^0.5.7", "eslint": "^9.18.0", "eslint-config-unjs": "^0.4.2", "expect-type": "^1.1.0", "jiti": "^2.4.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^2.1.8"}, "packageManager": "pnpm@9.15.4"}