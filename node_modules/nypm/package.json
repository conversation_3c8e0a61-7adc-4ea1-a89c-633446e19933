{"name": "nypm", "version": "0.6.0", "description": "Unified Package Manager for Node.js", "repository": "unjs/nypm", "license": "MIT", "sideEffects": false, "type": "module", "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "bin": {"nypm": "./dist/cli.mjs"}, "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"citty": "^0.1.6", "consola": "^3.4.0", "pathe": "^2.0.3", "pkg-types": "^2.0.0", "tinyexec": "^0.3.2"}, "devDependencies": {"@types/node": "^22.13.5", "@vitest/coverage-v8": "^3.0.7", "automd": "^0.3.12", "changelogen": "^0.5.7", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.2", "std-env": "^3.8.0", "typescript": "^5.7.3", "ufo": "^1.5.4", "unbuild": "^3.3.1", "vitest": "^3.0.7"}, "engines": {"node": "^14.16.0 || >=16.10.0"}, "scripts": {"build": "automd && unbuild", "dev": "vitest dev", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint . --fix && prettier -w src test", "nypm": "jiti src/cli.ts", "release": "pnpm test && pnpm build && changelogen --release --push && pnpm publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit"}}